{"name": "quick-format-unescaped", "version": "4.0.4", "description": "Solves a problem with util.format", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "nyc -- node test", "test:html": "nyc --reporter=html -- node test"}, "author": "<PERSON>", "devDependencies": {"fastbench": "^1.0.1", "nyc": "^15.0.0"}, "dependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/davidmarkclements/quick-format.git"}, "keywords": [], "license": "MIT", "bugs": {"url": "https://github.com/davidmarkclements/quick-format/issues"}, "homepage": "https://github.com/davidmarkclements/quick-format#readme"}