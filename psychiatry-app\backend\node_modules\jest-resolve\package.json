{"name": "jest-resolve", "version": "29.7.0", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-resolve"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-pnp-resolver": "^1.2.2", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "resolve": "^1.20.0", "resolve.exports": "^2.0.0", "slash": "^3.0.0"}, "devDependencies": {"@tsd/typescript": "^5.0.4", "@types/graceful-fs": "^4.1.3", "@types/pnpapi": "^0.0.2", "@types/resolve": "^1.20.2", "tsd-lite": "^0.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}