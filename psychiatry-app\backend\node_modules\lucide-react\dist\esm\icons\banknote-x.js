/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M13 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5", key: "16nib6" }],
  ["path", { d: "m17 17 5 5", key: "p7ous7" }],
  ["path", { d: "M18 12h.01", key: "yjnet6" }],
  ["path", { d: "m22 17-5 5", key: "gqnmv0" }],
  ["path", { d: "M6 12h.01", key: "c2rlol" }],
  ["circle", { cx: "12", cy: "12", r: "2", key: "1c9p78" }]
];
const BanknoteX = createLucideIcon("banknote-x", __iconNode);

export { __iconNode, BanknoteX as default };
//# sourceMappingURL=banknote-x.js.map
