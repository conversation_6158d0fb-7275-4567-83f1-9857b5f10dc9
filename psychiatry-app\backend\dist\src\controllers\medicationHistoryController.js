"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedicationHistoryController = void 0;
const medicationHistoryService_1 = require("../services/medicationHistoryService");
const validation_1 = require("../utils/validation");
class MedicationHistoryController {
    static async createMedicationHistory(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const validatedData = validation_1.createMedicationHistorySchema.parse(req.body);
            const result = await medicationHistoryService_1.MedicationHistoryService.createMedicationHistory(validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            res.status(201).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getAllMedicationHistory(req, res, next) {
        try {
            const { patientId, medicationName, isActive, page = 1, limit = 10 } = req.query;
            const filters = {
                patientId: patientId,
                medicationName: medicationName,
                isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
            };
            const result = await medicationHistoryService_1.MedicationHistoryService.getAllMedicationHistory(filters, parseInt(page), parseInt(limit));
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getMedicationHistoryById(req, res, next) {
        try {
            const { id } = req.params;
            const result = await medicationHistoryService_1.MedicationHistoryService.getMedicationHistoryById(id);
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getMedicationHistoryByPatient(req, res, next) {
        try {
            const { patientId } = req.params;
            const { isActive, limit = 20 } = req.query;
            const result = await medicationHistoryService_1.MedicationHistoryService.getMedicationHistoryByPatient(patientId, isActive === 'true' ? true : isActive === 'false' ? false : undefined, parseInt(limit));
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async updateMedicationHistory(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const validatedData = validation_1.updateMedicationHistorySchema.parse(req.body);
            const result = await medicationHistoryService_1.MedicationHistoryService.updateMedicationHistory(id, validatedData, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async deleteMedicationHistory(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const result = await medicationHistoryService_1.MedicationHistoryService.deleteMedicationHistory(id, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getActiveMedications(req, res, next) {
        try {
            const { patientId } = req.params;
            const result = await medicationHistoryService_1.MedicationHistoryService.getMedicationHistoryByPatient(patientId, true, 50);
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async discontinueMedication(req, res, next) {
        try {
            if (!req.user) {
                res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
                return;
            }
            const { id } = req.params;
            const { discontinuedReason, endDate } = req.body;
            const result = await medicationHistoryService_1.MedicationHistoryService.updateMedicationHistory(id, {
                isActive: false,
                endDate: endDate || new Date().toISOString(),
                discontinuedReason: discontinuedReason || 'Discontinued by clinician',
            }, req.user.id, { ipAddress: req.ip, userAgent: req.get('User-Agent') });
            if (!result.success) {
                res.status(404).json(result);
                return;
            }
            res.json(result);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.MedicationHistoryController = MedicationHistoryController;
//# sourceMappingURL=medicationHistoryController.js.map