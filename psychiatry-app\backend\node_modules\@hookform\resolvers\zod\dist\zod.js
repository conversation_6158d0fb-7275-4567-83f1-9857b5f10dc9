var e=require("@hookform/resolvers"),r=require("react-hook-form");function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach(function(n){if("default"!==n){var o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,o.get?o:{enumerable:!0,get:function(){return e[n]}})}}),r.default=e,r}var o=/*#__PURE__*/n(require("zod/v4/core"));function t(e,r){try{var n=e()}catch(e){return r(e)}return n&&n.then?n.then(void 0,r):n}function s(e,n){for(var o={};e.length;){var t=e[0],s=t.code,i=t.message,a=t.path.join(".");if(!o[a])if("unionErrors"in t){var u=t.unionErrors[0].errors[0];o[a]={message:u.message,type:u.code}}else o[a]={message:i,type:s};if("unionErrors"in t&&t.unionErrors.forEach(function(r){return r.errors.forEach(function(r){return e.push(r)})}),n){var c=o[a].types,f=c&&c[t.code];o[a]=r.appendErrors(a,n,o,s,f?[].concat(f,t.message):t.message)}e.shift()}return o}function i(e,n){for(var o={};e.length;){var t=e[0],s=t.code,i=t.message,a=t.path.join(".");if(!o[a])if("invalid_union"===t.code){var u=t.errors[0][0];o[a]={message:u.message,type:u.code}}else o[a]={message:i,type:s};if("invalid_union"===t.code&&t.errors.forEach(function(r){return r.forEach(function(r){return e.push(r)})}),n){var c=o[a].types,f=c&&c[t.code];o[a]=r.appendErrors(a,n,o,s,f?[].concat(f,t.message):t.message)}e.shift()}return o}exports.zodResolver=function(r,n,a){if(void 0===a&&(a={}),function(e){return"_def"in e&&"object"==typeof e._def&&"typeName"in e._def}(r))return function(o,i,u){try{return Promise.resolve(t(function(){return Promise.resolve(r["sync"===a.mode?"parse":"parseAsync"](o,n)).then(function(r){return u.shouldUseNativeValidation&&e.validateFieldsNatively({},u),{errors:{},values:a.raw?Object.assign({},o):r}})},function(r){if(function(e){return Array.isArray(null==e?void 0:e.issues)}(r))return{values:{},errors:e.toNestErrors(s(r.errors,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)};throw r}))}catch(e){return Promise.reject(e)}};if(function(e){return"_zod"in e&&"object"==typeof e._zod}(r))return function(s,u,c){try{return Promise.resolve(t(function(){return Promise.resolve(("sync"===a.mode?o.parse:o.parseAsync)(r,s,n)).then(function(r){return c.shouldUseNativeValidation&&e.validateFieldsNatively({},c),{errors:{},values:a.raw?Object.assign({},s):r}})},function(r){if(function(e){return e instanceof o.$ZodError}(r))return{values:{},errors:e.toNestErrors(i(r.issues,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)};throw r}))}catch(e){return Promise.reject(e)}};throw new Error("Invalid input: not a Zod schema")};
//# sourceMappingURL=zod.js.map
