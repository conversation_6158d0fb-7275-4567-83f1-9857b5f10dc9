"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var index_exports = {};
__export(index_exports, {
  assertNodeAPISupported: () => import_chunk_O5EOXX3N.assertNodeAPISupported,
  binaryTargets: () => import_chunk_7MLUNQIZ.binaryTargets,
  getBinaryTargetForCurrentPlatform: () => import_chunk_7PMGXL6S.getBinaryTargetForCurrentPlatform,
  getNodeAPIName: () => import_chunk_2U36ISZO.getNodeAPIName,
  getPlatformInfo: () => import_chunk_7PMGXL6S.getPlatformInfo,
  getos: () => import_chunk_7PMGXL6S.getos,
  jestConsoleContext: () => import_chunk_HE3NEP2U.jestConsoleContext,
  jestContext: () => import_chunk_HE3NEP2U.jestContext,
  jestStdoutContext: () => import_chunk_HE3NEP2U.jestStdoutContext,
  link: () => import_chunk_M5T7GI2R.link,
  processExitContext: () => import_chunk_HE3NEP2U.processExitContext
});
module.exports = __toCommonJS(index_exports);
var import_chunk_6HZWON4S = require("./chunk-6HZWON4S.js");
var import_chunk_HE3NEP2U = require("./chunk-HE3NEP2U.js");
var import_chunk_O5EOXX3N = require("./chunk-O5EOXX3N.js");
var import_chunk_2U36ISZO = require("./chunk-2U36ISZO.js");
var import_chunk_7PMGXL6S = require("./chunk-7PMGXL6S.js");
var import_chunk_M5T7GI2R = require("./chunk-M5T7GI2R.js");
var import_chunk_FWMN4WME = require("./chunk-FWMN4WME.js");
var import_chunk_YVXCXD3A = require("./chunk-YVXCXD3A.js");
var import_chunk_7MLUNQIZ = require("./chunk-7MLUNQIZ.js");
var import_chunk_2ESYSVXG = require("./chunk-2ESYSVXG.js");
(0, import_chunk_7MLUNQIZ.init_binaryTargets)();
