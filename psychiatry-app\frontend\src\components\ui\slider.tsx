import React, { useState, useCallback } from 'react';
import { cn } from '../../lib/utils';

interface SliderProps {
  value?: number[];
  defaultValue?: number[];
  min?: number;
  max?: number;
  step?: number;
  onValueChange?: (value: number[]) => void;
  className?: string;
  disabled?: boolean;
}

export const Slider: React.FC<SliderProps> = ({
  value,
  defaultValue = [0],
  min = 0,
  max = 100,
  step = 1,
  onValueChange,
  className,
  disabled = false,
}) => {
  const [internalValue, setInternalValue] = useState(value || defaultValue);
  const currentValue = value || internalValue;

  const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = [Number(event.target.value)];
    if (!value) {
      setInternalValue(newValue);
    }
    onValueChange?.(newValue);
  }, [value, onValueChange]);

  const percentage = ((currentValue[0] - min) / (max - min)) * 100;

  return (
    <div className={cn("relative flex items-center w-full", className)}>
      <div className="relative w-full h-2 bg-gray-200 rounded-full">
        <div 
          className="absolute h-2 bg-blue-600 rounded-full"
          style={{ width: `${percentage}%` }}
        />
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={currentValue[0]}
          onChange={handleChange}
          disabled={disabled}
          className="absolute inset-0 w-full h-2 opacity-0 cursor-pointer disabled:cursor-not-allowed"
        />
        <div 
          className="absolute w-4 h-4 bg-white border-2 border-blue-600 rounded-full shadow-sm transform -translate-y-1"
          style={{ left: `calc(${percentage}% - 8px)` }}
        />
      </div>
    </div>
  );
};

export default Slider;
