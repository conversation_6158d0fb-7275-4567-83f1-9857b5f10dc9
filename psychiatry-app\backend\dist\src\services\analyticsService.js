"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const client_1 = require("@prisma/client");
const date_fns_1 = require("date-fns");
const prisma = new client_1.PrismaClient();
class AnalyticsService {
    static async getDashboardAnalytics(userId, userRole) {
        const now = new Date();
        const today = (0, date_fns_1.startOfDay)(now);
        const thisWeek = (0, date_fns_1.startOfWeek)(now);
        const thisMonth = (0, date_fns_1.startOfMonth)(now);
        const patientFilter = {};
        const appointmentFilter = {};
        const [totalPatients, newPatientsThisWeek, newPatientsThisMonth, totalAppointments, todayAppointments, thisWeekAppointments, upcomingAppointments, totalLabResults, pendingLabResults, flaggedLabResults, totalUsers, activeUsers,] = await Promise.all([
            prisma.patient.count({
                where: {
                    isDeleted: false,
                    ...patientFilter
                },
            }),
            prisma.patient.count({
                where: {
                    isDeleted: false,
                    createdAt: { gte: thisWeek },
                    ...patientFilter,
                },
            }),
            prisma.patient.count({
                where: {
                    isDeleted: false,
                    createdAt: { gte: thisMonth },
                    ...patientFilter,
                },
            }),
            prisma.appointment.count({
                where: {
                    isDeleted: false,
                    ...appointmentFilter
                },
            }),
            prisma.appointment.count({
                where: {
                    isDeleted: false,
                    date: { gte: today, lt: (0, date_fns_1.endOfDay)(now) },
                    status: { in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'] },
                    ...appointmentFilter,
                },
            }),
            prisma.appointment.count({
                where: {
                    isDeleted: false,
                    date: { gte: thisWeek, lt: (0, date_fns_1.endOfWeek)(now) },
                    ...appointmentFilter,
                },
            }),
            prisma.appointment.count({
                where: {
                    isDeleted: false,
                    date: { gte: now },
                    status: { in: ['SCHEDULED', 'CONFIRMED'] },
                    ...appointmentFilter,
                },
            }),
            prisma.labResult.count({
                where: {
                    isDeleted: false
                },
            }),
            prisma.labResult.count({
                where: {
                    isDeleted: false,
                    status: { in: ['PENDING', 'IN_PROGRESS'] },
                },
            }),
            prisma.labResult.count({
                where: {
                    isDeleted: false,
                    flagged: true,
                },
            }),
            userRole === 'ADMIN' ? prisma.user.count() : 0,
            userRole === 'ADMIN' ? prisma.user.count({ where: { isActive: true } }) : 0,
        ]);
        const analytics = {
            patients: {
                total: Number(totalPatients),
                newThisWeek: Number(newPatientsThisWeek),
                newThisMonth: Number(newPatientsThisMonth),
                growthRate: totalPatients > 0 ? Number((Number(newPatientsThisMonth) / Number(totalPatients)) * 100) : 0,
            },
            appointments: {
                total: Number(totalAppointments),
                today: Number(todayAppointments),
                thisWeek: Number(thisWeekAppointments),
                upcoming: Number(upcomingAppointments),
            },
            labResults: {
                total: Number(totalLabResults),
                pending: Number(pendingLabResults),
                flagged: Number(flaggedLabResults),
                flaggedPercentage: totalLabResults > 0 ? Number((Number(flaggedLabResults) / Number(totalLabResults)) * 100) : 0,
            },
            system: userRole === 'ADMIN' ? {
                totalUsers: Number(totalUsers),
                activeUsers: Number(activeUsers),
                activePercentage: totalUsers > 0 ? Number((Number(activeUsers) / Number(totalUsers)) * 100) : 0,
            } : null,
        };
        return {
            success: true,
            data: { analytics },
        };
    }
    static async getPatientAnalytics(dateRange, userId, userRole) {
        const fromDate = new Date(dateRange.from);
        const toDate = new Date(dateRange.to);
        const patientFilter = userRole === 'CLINICIAN' ? { createdBy: userId } : {};
        const [genderDistribution, ageGroups, registrationTrend, patientsWithAppointments, patientsWithLabResults, stateDistribution,] = await Promise.all([
            prisma.patient.groupBy({
                by: ['gender'],
                where: {
                    isDeleted: false,
                    createdAt: { gte: fromDate, lte: toDate },
                    ...patientFilter,
                },
                _count: { gender: true },
            }),
            prisma.$queryRaw `
        SELECT
          CASE
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) < 18 THEN 'Under 18'
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) BETWEEN 18 AND 30 THEN '18-30'
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) BETWEEN 31 AND 50 THEN '31-50'
            WHEN (strftime('%Y', 'now') - strftime('%Y', "dateOfBirth")) BETWEEN 51 AND 70 THEN '51-70'
            ELSE 'Over 70'
          END as age_group,
          COUNT(*) as count
        FROM "patients"
        WHERE "isDeleted" = false
          AND "createdAt" >= ${fromDate}
          AND "createdAt" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw `AND "createdBy" = ${userId}` : prisma.$queryRaw ``}
        GROUP BY age_group
        ORDER BY age_group
      `,
            prisma.$queryRaw `
        SELECT
          DATE("createdAt") as date,
          COUNT(*) as count
        FROM "patients"
        WHERE "isDeleted" = false
          AND "createdAt" >= ${fromDate}
          AND "createdAt" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw `AND "createdBy" = ${userId}` : prisma.$queryRaw ``}
        GROUP BY DATE("createdAt")
        ORDER BY date
      `,
            prisma.patient.count({
                where: {
                    isDeleted: false,
                    createdAt: { gte: fromDate, lte: toDate },
                    appointments: { some: {
                            isDeleted: false
                        } },
                    ...patientFilter,
                },
            }),
            prisma.patient.count({
                where: {
                    isDeleted: false,
                    createdAt: { gte: fromDate, lte: toDate },
                    labResults: { some: {
                            isDeleted: false
                        } },
                    ...patientFilter,
                },
            }),
            prisma.patient.groupBy({
                by: ['address'],
                where: {
                    isDeleted: false,
                    createdAt: { gte: fromDate, lte: toDate },
                    address: { not: null },
                    ...patientFilter,
                },
                _count: { address: true },
            }),
        ]);
        const totalPatients = await prisma.patient.count({
            where: {
                isDeleted: false,
                createdAt: { gte: fromDate, lte: toDate },
                ...patientFilter,
            },
        });
        const analytics = {
            demographics: {
                gender: genderDistribution.reduce((acc, item) => {
                    acc[item.gender] = Number(item._count.gender);
                    return acc;
                }, {}),
                ageGroups: Array.isArray(ageGroups) ? ageGroups.map(group => ({
                    age_group: String(group.age_group),
                    count: Number(group.count)
                })) : [],
            },
            trends: {
                registration: Array.isArray(registrationTrend) ? registrationTrend.map(trend => ({
                    date: String(trend.date),
                    count: Number(trend.count)
                })) : [],
            },
            engagement: {
                totalPatients: Number(totalPatients),
                withAppointments: Number(patientsWithAppointments),
                withLabResults: Number(patientsWithLabResults),
                appointmentRate: totalPatients > 0 ? Number((Number(patientsWithAppointments) / Number(totalPatients)) * 100) : 0,
                labResultRate: totalPatients > 0 ? Number((Number(patientsWithLabResults) / Number(totalPatients)) * 100) : 0,
            },
            geographic: {
                states: stateDistribution.slice(0, 10).map(state => ({
                    address: String(state.address),
                    _count: { address: Number(state._count.address) }
                })),
            },
        };
        return {
            success: true,
            data: { analytics },
        };
    }
    static async getAppointmentAnalytics(dateRange, userId, userRole) {
        const fromDate = new Date(dateRange.from);
        const toDate = new Date(dateRange.to);
        const appointmentFilter = userRole === 'CLINICIAN' ? { providerId: userId } : {};
        const [statusDistribution, typeDistribution, dailyTrend, providerStats, durationStats, cancellationStats,] = await Promise.all([
            prisma.appointment.groupBy({
                by: ['status'],
                where: {
                    isDeleted: false,
                    date: { gte: fromDate, lte: toDate },
                    ...appointmentFilter,
                },
                _count: { status: true },
            }),
            prisma.appointment.groupBy({
                by: ['type'],
                where: {
                    isDeleted: false,
                    date: { gte: fromDate, lte: toDate },
                    ...appointmentFilter,
                },
                _count: { type: true },
            }),
            prisma.$queryRaw `
        SELECT
          DATE("date") as day,
          COUNT(*) as count
        FROM "appointments"
        WHERE "isDeleted" = false
          AND "date" >= ${fromDate}
          AND "date" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw `AND "providerId" = ${userId}` : prisma.$queryRaw ``}
        GROUP BY DATE("date")
        ORDER BY day
      `,
            userRole === 'ADMIN' ? prisma.$queryRaw `
        SELECT
          u."firstName" as first_name,
          u."lastName" as last_name,
          COUNT(a.id) as total_appointments,
          COUNT(CASE WHEN a.status = 'COMPLETED' THEN 1 END) as completed,
          COUNT(CASE WHEN a.status = 'CANCELLED' THEN 1 END) as cancelled,
          AVG(a.duration) as avg_duration
        FROM "users" u
        LEFT JOIN "appointments" a ON u.id = a."providerId"
          AND a."isDeleted" = false
          AND a."date" >= ${fromDate}
          AND a."date" <= ${toDate}
        WHERE u.role IN ('ADMIN', 'CLINICIAN')
          AND u."isActive" = true
        GROUP BY u.id, u."firstName", u."lastName"
        ORDER BY total_appointments DESC
      ` : [],
            prisma.appointment.aggregate({
                where: {
                    isDeleted: false,
                    date: { gte: fromDate, lte: toDate },
                    status: 'COMPLETED',
                    ...appointmentFilter,
                },
                _avg: { duration: true },
                _min: { duration: true },
                _max: { duration: true },
            }),
            prisma.$queryRaw `
        SELECT
          CAST(strftime('%w', "date") AS INTEGER) as day_of_week,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled
        FROM "appointments"
        WHERE "isDeleted" = false
          AND "date" >= ${fromDate}
          AND "date" <= ${toDate}
          ${userRole === 'CLINICIAN' ? prisma.$queryRaw `AND "providerId" = ${userId}` : prisma.$queryRaw ``}
        GROUP BY CAST(strftime('%w', "date") AS INTEGER)
        ORDER BY day_of_week
      `,
        ]);
        const totalAppointments = await prisma.appointment.count({
            where: {
                isDeleted: false,
                date: { gte: fromDate, lte: toDate },
                ...appointmentFilter,
            },
        });
        const analytics = {
            overview: {
                total: totalAppointments,
                statusDistribution: statusDistribution.reduce((acc, item) => {
                    acc[item.status] = item._count.status;
                    return acc;
                }, {}),
                typeDistribution: typeDistribution.reduce((acc, item) => {
                    acc[item.type] = item._count.type;
                    return acc;
                }, {}),
            },
            trends: {
                daily: dailyTrend,
            },
            performance: {
                providers: userRole === 'ADMIN' ? providerStats : null,
                duration: {
                    avg_minutes: Math.round(durationStats._avg.duration || 0),
                    min_minutes: durationStats._min.duration || 0,
                    max_minutes: durationStats._max.duration || 0
                },
                cancellationByDay: cancellationStats,
            },
        };
        return {
            success: true,
            data: { analytics },
        };
    }
    static async getLabResultAnalytics(dateRange, userId, userRole) {
        const fromDate = new Date(dateRange.from);
        const toDate = new Date(dateRange.to);
        const [testTypeDistribution, statusDistribution, flaggedResults, turnaroundStats, monthlyTrend,] = await Promise.all([
            prisma.labResult.groupBy({
                by: ['testType'],
                where: {
                    isDeleted: false,
                    testDate: { gte: fromDate, lte: toDate },
                },
                _count: { testType: true },
            }),
            prisma.labResult.groupBy({
                by: ['status'],
                where: {
                    isDeleted: false,
                    testDate: { gte: fromDate, lte: toDate },
                },
                _count: { status: true },
            }),
            prisma.$queryRaw `
        SELECT
          "testType" as test_type,
          COUNT(*) as total,
          COUNT(CASE WHEN flags IS NOT NULL AND flags != '{}' THEN 1 END) as flagged
        FROM "lab_results"
        WHERE "isDeleted" = false
          AND "testDate" >= ${fromDate}
          AND "testDate" <= ${toDate}
        GROUP BY "testType"
        ORDER BY flagged DESC
      `,
            prisma.$queryRaw `
        SELECT
          AVG((julianday("createdAt") - julianday("testDate")) * 24) as avg_hours,
          MIN((julianday("createdAt") - julianday("testDate")) * 24) as min_hours,
          MAX((julianday("createdAt") - julianday("testDate")) * 24) as max_hours
        FROM "lab_results"
        WHERE "isDeleted" = false
          AND "testDate" >= ${fromDate}
          AND "testDate" <= ${toDate}
          AND status = 'COMPLETED'
      `,
            prisma.$queryRaw `
        SELECT
          strftime('%Y-%m-01', "testDate") as month,
          COUNT(*) as total,
          COUNT(CASE WHEN flags IS NOT NULL AND flags != '{}' THEN 1 END) as flagged
        FROM "lab_results"
        WHERE "isDeleted" = false
          AND "testDate" >= ${fromDate}
          AND "testDate" <= ${toDate}
        GROUP BY strftime('%Y-%m-01', "testDate")
        ORDER BY month
      `,
        ]);
        const totalLabResults = await prisma.labResult.count({
            where: {
                isDeleted: false,
                testDate: { gte: fromDate, lte: toDate },
            },
        });
        const analytics = {
            overview: {
                total: totalLabResults,
                testTypeDistribution: testTypeDistribution.reduce((acc, item) => {
                    acc[item.testType] = item._count.testType;
                    return acc;
                }, {}),
                statusDistribution: statusDistribution.reduce((acc, item) => {
                    acc[item.status] = item._count.status;
                    return acc;
                }, {}),
            },
            quality: {
                flaggedByTestType: flaggedResults,
                turnaroundTime: turnaroundStats[0] || { avg_hours: 0, min_hours: 0, max_hours: 0 },
            },
            trends: {
                monthly: monthlyTrend,
            },
        };
        return {
            success: true,
            data: { analytics },
        };
    }
    static async getSystemAnalytics(dateRange, _userId, userRole) {
        if (userRole !== 'ADMIN') {
            return {
                success: false,
                error: 'Access denied. Admin role required.',
            };
        }
        const fromDate = new Date(dateRange.from);
        const toDate = new Date(dateRange.to);
        const [userActivity, auditSummary, usageTrends, errorAnalysis,] = await Promise.all([
            prisma.$queryRaw `
        SELECT
          u.role,
          COUNT(DISTINCT u.id) as total_users,
          COUNT(DISTINCT CASE WHEN u."lastLogin" >= ${(0, date_fns_1.subDays)(new Date(), 7)} THEN u.id END) as active_last_7_days,
          COUNT(DISTINCT CASE WHEN u."lastLogin" >= ${(0, date_fns_1.subDays)(new Date(), 30)} THEN u.id END) as active_last_30_days
        FROM "users" u
        WHERE u."isActive" = true
        GROUP BY u.role
      `,
            prisma.auditLog.groupBy({
                by: ['action', 'entityType'],
                where: {
                    timestamp: { gte: fromDate, lte: toDate },
                },
                _count: { action: true },
            }),
            prisma.$queryRaw `
        SELECT 
          DATE("timestamp") as date,
          "entityType",
          "action",
          COUNT(*) as count
        FROM "audit_logs" 
        WHERE "timestamp" >= ${fromDate}
          AND "timestamp" <= ${toDate}
        GROUP BY DATE("timestamp"), "entityType", "action"
        ORDER BY date
      `,
            [],
        ]);
        const analytics = {
            users: {
                activity: userActivity,
            },
            audit: {
                summary: auditSummary.reduce((acc, item) => {
                    const key = `${item.entityType}_${item.action}`;
                    acc[key] = item._count.action;
                    return acc;
                }, {}),
            },
            usage: {
                trends: usageTrends,
            },
            errors: {
                analysis: errorAnalysis,
            },
        };
        return {
            success: true,
            data: { analytics },
        };
    }
}
exports.AnalyticsService = AnalyticsService;
//# sourceMappingURL=analyticsService.js.map