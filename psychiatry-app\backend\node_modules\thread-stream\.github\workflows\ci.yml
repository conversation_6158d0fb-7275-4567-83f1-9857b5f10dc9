name: CI

on:
  push:
    paths-ignore:
        - 'docs/**'
        - '*.md'
  pull_request:
    paths-ignore:
        - 'docs/**'
        - '*.md'

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: "${{ github.workflow }} @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}"
  cancel-in-progress: true

jobs:
  dependency-review:
    name: Dependency Review
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Check out repo
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Dependency review
        uses: actions/dependency-review-action@v4

  test:
    name: Test
    runs-on: ${{ matrix.os }}
    permissions:
      contents: read
    strategy:
      matrix:
        node-version: [18, 20, 22]
        os: [macos-latest, ubuntu-latest, windows-latest]
        exclude:
          - os: windows-latest
            node-version: 22

    steps:
      - name: Check out repo
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Setup Node ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: npm i --ignore-scripts

      - name: Run tests
        run: npm run test:ci

      - name: Coveralls Parallel
        uses: coverallsapp/github-action@v2.3.0
        with:
          github-token: ${{ secrets.github_token }}
          parallel: true
          flag-name: run-${{ matrix.node-version }}-${{ matrix.os }}

  coverage:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Coveralls Finished
        uses: coverallsapp/github-action@v2.3.0
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          parallel-finished: true

  automerge:
    name: Automerge Dependabot PRs
    if: >
        github.event_name == 'pull_request' &&
        github.event.pull_request.user.login == 'dependabot[bot]'
    needs: test
    permissions:
      pull-requests: write
      contents: write
    runs-on: ubuntu-latest
    steps:
      - uses: fastify/github-action-merge-dependabot@v3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
