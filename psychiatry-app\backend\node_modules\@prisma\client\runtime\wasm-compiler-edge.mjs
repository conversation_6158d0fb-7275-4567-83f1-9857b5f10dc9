var Lu=Object.create;var Br=Object.defineProperty;var Uu=Object.getOwnPropertyDescriptor;var Fu=Object.getOwnPropertyNames;var $u=Object.getPrototypeOf,Vu=Object.prototype.hasOwnProperty;var de=(e,t)=>()=>(e&&(t=e(e=0)),t);var oe=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),$t=(e,t)=>{for(var r in t)Br(e,r,{get:t[r],enumerable:!0})},Po=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Fu(t))!Vu.call(e,i)&&i!==r&&Br(e,i,{get:()=>t[i],enumerable:!(n=Uu(t,i))||n.enumerable});return e};var ke=(e,t,r)=>(r=e!=null?Lu($u(e)):{},Po(t||!e||!e.__esModule?Br(r,"default",{value:e,enumerable:!0}):r,e)),qu=e=>Po(Br({},"__esModule",{value:!0}),e);function Wn(e,t){if(t=t.toLowerCase(),t==="utf8"||t==="utf-8")return new h(Gu.encode(e));if(t==="base64"||t==="base64url")return e=e.replace(/-/g,"+").replace(/_/g,"/"),e=e.replace(/[^A-Za-z0-9+/]/g,""),new h([...atob(e)].map(r=>r.charCodeAt(0)));if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return new h([...e].map(r=>r.charCodeAt(0)));if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let r=new h(e.length*2),n=new DataView(r.buffer);for(let i=0;i<e.length;i++)n.setUint16(i*2,e.charCodeAt(i),!0);return r}if(t==="hex"){let r=new h(e.length/2);for(let n=0,i=0;i<e.length;i+=2,n++)r[n]=parseInt(e.slice(i,i+2),16);return r}vo(`encoding "${t}"`)}function Bu(e){let r=Object.getOwnPropertyNames(DataView.prototype).filter(d=>d.startsWith("get")||d.startsWith("set")),n=r.map(d=>d.replace("get","read").replace("set","write")),i=(d,f)=>function(T=0){return K(T,"offset"),ce(T,"offset"),Z(T,"offset",this.length-1),new DataView(this.buffer)[r[d]](T,f)},o=(d,f)=>function(T,v=0){let A=r[d].match(/set(\w+\d+)/)[1].toLowerCase(),R=Qu[A];return K(v,"offset"),ce(v,"offset"),Z(v,"offset",this.length-1),ju(T,"value",R[0],R[1]),new DataView(this.buffer)[r[d]](v,T,f),v+parseInt(r[d].match(/\d+/)[0])/8},s=d=>{d.forEach(f=>{f.includes("Uint")&&(e[f.replace("Uint","UInt")]=e[f]),f.includes("Float64")&&(e[f.replace("Float64","Double")]=e[f]),f.includes("Float32")&&(e[f.replace("Float32","Float")]=e[f])})};n.forEach((d,f)=>{d.startsWith("read")&&(e[d]=i(f,!1),e[d+"LE"]=i(f,!0),e[d+"BE"]=i(f,!1)),d.startsWith("write")&&(e[d]=o(f,!1),e[d+"LE"]=o(f,!0),e[d+"BE"]=o(f,!1)),s([d,d+"LE",d+"BE"])})}function vo(e){throw new Error(`Buffer polyfill does not implement "${e}"`)}function jr(e,t){if(!(e instanceof Uint8Array))throw new TypeError(`The "${t}" argument must be an instance of Buffer or Uint8Array`)}function Z(e,t,r=Wu+1){if(e<0||e>r){let n=new RangeError(`The value of "${t}" is out of range. It must be >= 0 && <= ${r}. Received ${e}`);throw n.code="ERR_OUT_OF_RANGE",n}}function K(e,t){if(typeof e!="number"){let r=new TypeError(`The "${t}" argument must be of type number. Received type ${typeof e}.`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function ce(e,t){if(!Number.isInteger(e)||Number.isNaN(e)){let r=new RangeError(`The value of "${t}" is out of range. It must be an integer. Received ${e}`);throw r.code="ERR_OUT_OF_RANGE",r}}function ju(e,t,r,n){if(e<r||e>n){let i=new RangeError(`The value of "${t}" is out of range. It must be >= ${r} and <= ${n}. Received ${e}`);throw i.code="ERR_OUT_OF_RANGE",i}}function To(e,t){if(typeof e!="string"){let r=new TypeError(`The "${t}" argument must be of type string. Received type ${typeof e}`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function Ku(e,t="utf8"){return h.from(e,t)}var h,Qu,Gu,Hu,Ju,Wu,y,Kn,l=de(()=>{"use strict";h=class e extends Uint8Array{_isBuffer=!0;get offset(){return this.byteOffset}static alloc(t,r=0,n="utf8"){return To(n,"encoding"),e.allocUnsafe(t).fill(r,n)}static allocUnsafe(t){return e.from(t)}static allocUnsafeSlow(t){return e.from(t)}static isBuffer(t){return t&&!!t._isBuffer}static byteLength(t,r="utf8"){if(typeof t=="string")return Wn(t,r).byteLength;if(t&&t.byteLength)return t.byteLength;let n=new TypeError('The "string" argument must be of type string or an instance of Buffer or ArrayBuffer.');throw n.code="ERR_INVALID_ARG_TYPE",n}static isEncoding(t){return Ju.includes(t)}static compare(t,r){jr(t,"buff1"),jr(r,"buff2");for(let n=0;n<t.length;n++){if(t[n]<r[n])return-1;if(t[n]>r[n])return 1}return t.length===r.length?0:t.length>r.length?1:-1}static from(t,r="utf8"){if(t&&typeof t=="object"&&t.type==="Buffer")return new e(t.data);if(typeof t=="number")return new e(new Uint8Array(t));if(typeof t=="string")return Wn(t,r);if(ArrayBuffer.isView(t)){let{byteOffset:n,byteLength:i,buffer:o}=t;return"map"in t&&typeof t.map=="function"?new e(t.map(s=>s%256),n,i):new e(o,n,i)}if(t&&typeof t=="object"&&("length"in t||"byteLength"in t||"buffer"in t))return new e(t);throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}static concat(t,r){if(t.length===0)return e.alloc(0);let n=[].concat(...t.map(o=>[...o])),i=e.alloc(r!==void 0?r:n.length);return i.set(r!==void 0?n.slice(0,r):n),i}slice(t=0,r=this.length){return this.subarray(t,r)}subarray(t=0,r=this.length){return Object.setPrototypeOf(super.subarray(t,r),e.prototype)}reverse(){return super.reverse(),this}readIntBE(t,r){K(t,"offset"),ce(t,"offset"),Z(t,"offset",this.length-1),K(r,"byteLength"),ce(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return n.getUint8(0)&128&&(i-=Math.pow(256,r)),i}readIntLE(t,r){K(t,"offset"),ce(t,"offset"),Z(t,"offset",this.length-1),K(r,"byteLength"),ce(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return n.getUint8(r-1)&128&&(i-=Math.pow(256,r)),i}readUIntBE(t,r){K(t,"offset"),ce(t,"offset"),Z(t,"offset",this.length-1),K(r,"byteLength"),ce(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return i}readUintBE(t,r){return this.readUIntBE(t,r)}readUIntLE(t,r){K(t,"offset"),ce(t,"offset"),Z(t,"offset",this.length-1),K(r,"byteLength"),ce(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return i}readUintLE(t,r){return this.readUIntLE(t,r)}writeIntBE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntBE(t,r,n)}writeIntLE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntLE(t,r,n)}writeUIntBE(t,r,n){K(r,"offset"),ce(r,"offset"),Z(r,"offset",this.length-1),K(n,"byteLength"),ce(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=n-1;o>=0;o--)i.setUint8(o,t&255),t=t/256;return r+n}writeUintBE(t,r,n){return this.writeUIntBE(t,r,n)}writeUIntLE(t,r,n){K(r,"offset"),ce(r,"offset"),Z(r,"offset",this.length-1),K(n,"byteLength"),ce(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=0;o<n;o++)i.setUint8(o,t&255),t=t/256;return r+n}writeUintLE(t,r,n){return this.writeUIntLE(t,r,n)}toJSON(){return{type:"Buffer",data:Array.from(this)}}swap16(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=2)t.setUint16(r,t.getUint16(r,!0),!1);return this}swap32(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=4)t.setUint32(r,t.getUint32(r,!0),!1);return this}swap64(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=8)t.setBigUint64(r,t.getBigUint64(r,!0),!1);return this}compare(t,r=0,n=t.length,i=0,o=this.length){return jr(t,"target"),K(r,"targetStart"),K(n,"targetEnd"),K(i,"sourceStart"),K(o,"sourceEnd"),Z(r,"targetStart"),Z(n,"targetEnd",t.length),Z(i,"sourceStart"),Z(o,"sourceEnd",this.length),e.compare(this.slice(i,o),t.slice(r,n))}equals(t){return jr(t,"otherBuffer"),this.length===t.length&&this.every((r,n)=>r===t[n])}copy(t,r=0,n=0,i=this.length){Z(r,"targetStart"),Z(n,"sourceStart",this.length),Z(i,"sourceEnd"),r>>>=0,n>>>=0,i>>>=0;let o=0;for(;n<i&&!(this[n]===void 0||t[r]===void 0);)t[r]=this[n],o++,n++,r++;return o}write(t,r,n,i="utf8"){let o=typeof r=="string"?0:r??0,s=typeof n=="string"?this.length-o:n??this.length-o;return i=typeof r=="string"?r:typeof n=="string"?n:i,K(o,"offset"),K(s,"length"),Z(o,"offset",this.length),Z(s,"length",this.length),(i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le")&&(s=s-s%2),Wn(t,i).copy(this,o,0,s)}fill(t=0,r=0,n=this.length,i="utf-8"){let o=typeof r=="string"?0:r,s=typeof n=="string"?this.length:n;if(i=typeof r=="string"?r:typeof n=="string"?n:i,t=e.from(typeof t=="number"?[t]:t??[],i),To(i,"encoding"),Z(o,"offset",this.length),Z(s,"end",this.length),t.length!==0)for(let d=o;d<s;d+=t.length)super.set(t.slice(0,t.length+d>=this.length?this.length-d:t.length),d);return this}includes(t,r=null,n="utf-8"){return this.indexOf(t,r,n)!==-1}lastIndexOf(t,r=null,n="utf-8"){return this.indexOf(t,r,n,!0)}indexOf(t,r=null,n="utf-8",i=!1){let o=i?this.findLastIndex.bind(this):this.findIndex.bind(this);n=typeof r=="string"?r:n;let s=e.from(typeof t=="number"?[t]:t,n),d=typeof r=="string"?0:r;return d=typeof r=="number"?d:null,d=Number.isNaN(d)?null:d,d??=i?this.length:0,d=d<0?this.length+d:d,s.length===0&&i===!1?d>=this.length?this.length:d:s.length===0&&i===!0?(d>=this.length?this.length:d)||this.length:o((f,T)=>(i?T<=d:T>=d)&&this[T]===s[0]&&s.every((A,R)=>this[T+R]===A))}toString(t="utf8",r=0,n=this.length){if(r=r<0?0:r,t=t.toString().toLowerCase(),n<=0)return"";if(t==="utf8"||t==="utf-8")return Hu.decode(this.slice(r,n));if(t==="base64"||t==="base64url"){let i=btoa(this.reduce((o,s)=>o+Kn(s),""));return t==="base64url"?i.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):i}if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return this.slice(r,n).reduce((i,o)=>i+Kn(o&(t==="ascii"?127:255)),"");if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let i=new DataView(this.buffer.slice(r,n));return Array.from({length:i.byteLength/2},(o,s)=>s*2+1<i.byteLength?Kn(i.getUint16(s*2,!0)):"").join("")}if(t==="hex")return this.slice(r,n).reduce((i,o)=>i+o.toString(16).padStart(2,"0"),"");vo(`encoding "${t}"`)}toLocaleString(){return this.toString()}inspect(){return`<Buffer ${this.toString("hex").match(/.{1,2}/g).join(" ")}>`}};Qu={int8:[-128,127],int16:[-32768,32767],int32:[-2147483648,2147483647],uint8:[0,255],uint16:[0,65535],uint32:[0,4294967295],float32:[-1/0,1/0],float64:[-1/0,1/0],bigint64:[-0x8000000000000000n,0x7fffffffffffffffn],biguint64:[0n,0xffffffffffffffffn]},Gu=new TextEncoder,Hu=new TextDecoder,Ju=["utf8","utf-8","hex","base64","ascii","binary","base64url","ucs2","ucs-2","utf16le","utf-16le","latin1","latin-1"],Wu=4294967295;Bu(h.prototype);y=new Proxy(Ku,{construct(e,[t,r]){return h.from(t,r)},get(e,t){return h[t]}}),Kn=String.fromCodePoint});var g,u=de(()=>{"use strict";g={nextTick:(e,...t)=>{setTimeout(()=>{e(...t)},0)},env:{},version:"",cwd:()=>"/",stderr:{},argv:["/bin/node"],pid:1e4}});var w,c=de(()=>{"use strict";w=globalThis.performance??(()=>{let e=Date.now();return{now:()=>Date.now()-e}})()});var b,p=de(()=>{"use strict";b=()=>{};b.prototype=b});var m=de(()=>{"use strict"});function So(e,t){var r,n,i,o,s,d,f,T,v=e.constructor,A=v.precision;if(!e.s||!t.s)return t.s||(t=new v(e)),G?$(t,A):t;if(f=e.d,T=t.d,s=e.e,i=t.e,f=f.slice(),o=s-i,o){for(o<0?(n=f,o=-o,d=T.length):(n=T,i=s,d=f.length),s=Math.ceil(A/j),d=s>d?s+1:d+1,o>d&&(o=d,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for(d=f.length,o=T.length,d-o<0&&(o=d,n=T,T=f,f=n),r=0;o;)r=(f[--o]=f[o]+T[o]+r)/X|0,f[o]%=X;for(r&&(f.unshift(r),++i),d=f.length;f[--d]==0;)f.pop();return t.d=f,t.e=i,G?$(t,A):t}function Ae(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Je+e)}function ve(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=j-n.length,r&&(o+=Ue(r)),o+=n;s=e[t],n=s+"",r=j-n.length,r&&(o+=Ue(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function Io(e,t){var r,n,i,o,s,d,f=0,T=0,v=e.constructor,A=v.precision;if(z(e)>16)throw Error(Yn+z(e));if(!e.s)return new v(fe);for(t==null?(G=!1,d=A):d=t,s=new v(.03125);e.abs().gte(.1);)e=e.times(s),T+=5;for(n=Math.log(He(2,T))/Math.LN10*2+5|0,d+=n,r=i=o=new v(fe),v.precision=d;;){if(i=$(i.times(e),d),r=r.times(++f),s=o.plus(De(i,r,d)),ve(s.d).slice(0,d)===ve(o.d).slice(0,d)){for(;T--;)o=$(o.times(o),d);return v.precision=A,t==null?(G=!0,$(o,A)):o}o=s}}function z(e){for(var t=e.e*j,r=e.d[0];r>=10;r/=10)t++;return t}function zn(e,t,r){if(t>e.LN10.sd())throw G=!0,r&&(e.precision=r),Error(he+"LN10 precision limit exceeded");return $(new e(e.LN10),t)}function Ue(e){for(var t="";e--;)t+="0";return t}function Vt(e,t){var r,n,i,o,s,d,f,T,v,A=1,R=10,C=e,D=C.d,I=C.constructor,M=I.precision;if(C.s<1)throw Error(he+(C.s?"NaN":"-Infinity"));if(C.eq(fe))return new I(0);if(t==null?(G=!1,T=M):T=t,C.eq(10))return t==null&&(G=!0),zn(I,T);if(T+=R,I.precision=T,r=ve(D),n=r.charAt(0),o=z(C),Math.abs(o)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)C=C.times(e),r=ve(C.d),n=r.charAt(0),A++;o=z(C),n>1?(C=new I("0."+r),o++):C=new I(n+"."+r.slice(1))}else return f=zn(I,T+2,M).times(o+""),C=Vt(new I(n+"."+r.slice(1)),T-R).plus(f),I.precision=M,t==null?(G=!0,$(C,M)):C;for(d=s=C=De(C.minus(fe),C.plus(fe),T),v=$(C.times(C),T),i=3;;){if(s=$(s.times(v),T),f=d.plus(De(s,new I(i),T)),ve(f.d).slice(0,T)===ve(d.d).slice(0,T))return d=d.times(2),o!==0&&(d=d.plus(zn(I,T+2,M).times(o+""))),d=De(d,new I(A),T),I.precision=M,t==null?(G=!0,$(d,M)):d;d=f,i+=2}}function Ao(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=ct(r/j),e.d=[],n=(r+1)%j,r<0&&(n+=j),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=j;n<i;)e.d.push(+t.slice(n,n+=j));t=t.slice(n),n=j-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),G&&(e.e>Qr||e.e<-Qr))throw Error(Yn+r)}else e.s=0,e.e=0,e.d=[0];return e}function $(e,t,r){var n,i,o,s,d,f,T,v,A=e.d;for(s=1,o=A[0];o>=10;o/=10)s++;if(n=t-s,n<0)n+=j,i=t,T=A[v=0];else{if(v=Math.ceil((n+1)/j),o=A.length,v>=o)return e;for(T=o=A[v],s=1;o>=10;o/=10)s++;n%=j,i=n-j+s}if(r!==void 0&&(o=He(10,s-i-1),d=T/o%10|0,f=t<0||A[v+1]!==void 0||T%o,f=r<4?(d||f)&&(r==0||r==(e.s<0?3:2)):d>5||d==5&&(r==4||f||r==6&&(n>0?i>0?T/He(10,s-i):0:A[v-1])%10&1||r==(e.s<0?8:7))),t<1||!A[0])return f?(o=z(e),A.length=1,t=t-o-1,A[0]=He(10,(j-t%j)%j),e.e=ct(-t/j)||0):(A.length=1,A[0]=e.e=e.s=0),e;if(n==0?(A.length=v,o=1,v--):(A.length=v+1,o=He(10,j-n),A[v]=i>0?(T/He(10,s-i)%He(10,i)|0)*o:0),f)for(;;)if(v==0){(A[0]+=o)==X&&(A[0]=1,++e.e);break}else{if(A[v]+=o,A[v]!=X)break;A[v--]=0,o=1}for(n=A.length;A[--n]===0;)A.pop();if(G&&(e.e>Qr||e.e<-Qr))throw Error(Yn+z(e));return e}function Oo(e,t){var r,n,i,o,s,d,f,T,v,A,R=e.constructor,C=R.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new R(e),G?$(t,C):t;if(f=e.d,A=t.d,n=t.e,T=e.e,f=f.slice(),s=T-n,s){for(v=s<0,v?(r=f,s=-s,d=A.length):(r=A,n=T,d=f.length),i=Math.max(Math.ceil(C/j),d)+2,s>i&&(s=i,r.length=1),r.reverse(),i=s;i--;)r.push(0);r.reverse()}else{for(i=f.length,d=A.length,v=i<d,v&&(d=i),i=0;i<d;i++)if(f[i]!=A[i]){v=f[i]<A[i];break}s=0}for(v&&(r=f,f=A,A=r,t.s=-t.s),d=f.length,i=A.length-d;i>0;--i)f[d++]=0;for(i=A.length;i>s;){if(f[--i]<A[i]){for(o=i;o&&f[--o]===0;)f[o]=X-1;--f[o],f[i]+=X}f[i]-=A[i]}for(;f[--d]===0;)f.pop();for(;f[0]===0;f.shift())--n;return f[0]?(t.d=f,t.e=n,G?$(t,C):t):new R(0)}function We(e,t,r){var n,i=z(e),o=ve(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ue(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+Ue(-i-1)+o,r&&(n=r-s)>0&&(o+=Ue(n))):i>=s?(o+=Ue(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Ue(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Ue(n))),e.s<0?"-"+o:o}function Co(e,t){if(e.length>t)return e.length=t,!0}function ko(e){var t,r,n;function i(o){var s=this;if(!(s instanceof i))return new i(o);if(s.constructor=i,o instanceof i){s.s=o.s,s.e=o.e,s.d=(o=o.d)?o.slice():o;return}if(typeof o=="number"){if(o*0!==0)throw Error(Je+o);if(o>0)s.s=1;else if(o<0)o=-o,s.s=-1;else{s.s=0,s.e=0,s.d=[0];return}if(o===~~o&&o<1e7){s.e=0,s.d=[o];return}return Ao(s,o.toString())}else if(typeof o!="string")throw Error(Je+o);if(o.charCodeAt(0)===45?(o=o.slice(1),s.s=-1):s.s=1,Yu.test(o))Ao(s,o);else throw Error(Je+o)}if(i.prototype=S,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=ko,i.config=i.set=Zu,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function Zu(e){if(!e||typeof e!="object")throw Error(he+"Object expected");var t,r,n,i=["precision",1,ut,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(ct(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Je+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Je+r+": "+n);return this}var ut,zu,Zn,G,he,Je,Yn,ct,He,Yu,fe,X,j,Ro,Qr,S,De,Zn,Gr,Do=de(()=>{"use strict";l();u();c();p();m();a();ut=1e9,zu={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},G=!0,he="[DecimalError] ",Je=he+"Invalid argument: ",Yn=he+"Exponent out of range: ",ct=Math.floor,He=Math.pow,Yu=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,X=1e7,j=7,Ro=9007199254740991,Qr=ct(Ro/j),S={};S.absoluteValue=S.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};S.comparedTo=S.cmp=function(e){var t,r,n,i,o=this;if(e=new o.constructor(e),o.s!==e.s)return o.s||-e.s;if(o.e!==e.e)return o.e>e.e^o.s<0?1:-1;for(n=o.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(o.d[t]!==e.d[t])return o.d[t]>e.d[t]^o.s<0?1:-1;return n===i?0:n>i^o.s<0?1:-1};S.decimalPlaces=S.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*j;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};S.dividedBy=S.div=function(e){return De(this,new this.constructor(e))};S.dividedToIntegerBy=S.idiv=function(e){var t=this,r=t.constructor;return $(De(t,new r(e),0,1),r.precision)};S.equals=S.eq=function(e){return!this.cmp(e)};S.exponent=function(){return z(this)};S.greaterThan=S.gt=function(e){return this.cmp(e)>0};S.greaterThanOrEqualTo=S.gte=function(e){return this.cmp(e)>=0};S.isInteger=S.isint=function(){return this.e>this.d.length-2};S.isNegative=S.isneg=function(){return this.s<0};S.isPositive=S.ispos=function(){return this.s>0};S.isZero=function(){return this.s===0};S.lessThan=S.lt=function(e){return this.cmp(e)<0};S.lessThanOrEqualTo=S.lte=function(e){return this.cmp(e)<1};S.logarithm=S.log=function(e){var t,r=this,n=r.constructor,i=n.precision,o=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(fe))throw Error(he+"NaN");if(r.s<1)throw Error(he+(r.s?"NaN":"-Infinity"));return r.eq(fe)?new n(0):(G=!1,t=De(Vt(r,o),Vt(e,o),o),G=!0,$(t,i))};S.minus=S.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Oo(t,e):So(t,(e.s=-e.s,e))};S.modulo=S.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(he+"NaN");return r.s?(G=!1,t=De(r,e,0,1).times(e),G=!0,r.minus(t)):$(new n(r),i)};S.naturalExponential=S.exp=function(){return Io(this)};S.naturalLogarithm=S.ln=function(){return Vt(this)};S.negated=S.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};S.plus=S.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?So(t,e):Oo(t,(e.s=-e.s,e))};S.precision=S.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Je+e);if(t=z(i)+1,n=i.d.length-1,r=n*j+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};S.squareRoot=S.sqrt=function(){var e,t,r,n,i,o,s,d=this,f=d.constructor;if(d.s<1){if(!d.s)return new f(0);throw Error(he+"NaN")}for(e=z(d),G=!1,i=Math.sqrt(+d),i==0||i==1/0?(t=ve(d.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=ct((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new f(t)):n=new f(i.toString()),r=f.precision,i=s=r+3;;)if(o=n,n=o.plus(De(d,o,s+2)).times(.5),ve(o.d).slice(0,s)===(t=ve(n.d)).slice(0,s)){if(t=t.slice(s-3,s+1),i==s&&t=="4999"){if($(o,r+1,0),o.times(o).eq(d)){n=o;break}}else if(t!="9999")break;s+=4}return G=!0,$(n,r)};S.times=S.mul=function(e){var t,r,n,i,o,s,d,f,T,v=this,A=v.constructor,R=v.d,C=(e=new A(e)).d;if(!v.s||!e.s)return new A(0);for(e.s*=v.s,r=v.e+e.e,f=R.length,T=C.length,f<T&&(o=R,R=C,C=o,s=f,f=T,T=s),o=[],s=f+T,n=s;n--;)o.push(0);for(n=T;--n>=0;){for(t=0,i=f+n;i>n;)d=o[i]+C[n]*R[i-n-1]+t,o[i--]=d%X|0,t=d/X|0;o[i]=(o[i]+t)%X|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=r,G?$(e,A.precision):e};S.toDecimalPlaces=S.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(Ae(e,0,ut),t===void 0?t=n.rounding:Ae(t,0,8),$(r,e+z(r)+1,t))};S.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=We(n,!0):(Ae(e,0,ut),t===void 0?t=i.rounding:Ae(t,0,8),n=$(new i(n),e+1,t),r=We(n,!0,e+1)),r};S.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?We(i):(Ae(e,0,ut),t===void 0?t=o.rounding:Ae(t,0,8),n=$(new o(i),e+z(i)+1,t),r=We(n.abs(),!1,e+z(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};S.toInteger=S.toint=function(){var e=this,t=e.constructor;return $(new t(e),z(e)+1,t.rounding)};S.toNumber=function(){return+this};S.toPower=S.pow=function(e){var t,r,n,i,o,s,d=this,f=d.constructor,T=12,v=+(e=new f(e));if(!e.s)return new f(fe);if(d=new f(d),!d.s){if(e.s<1)throw Error(he+"Infinity");return d}if(d.eq(fe))return d;if(n=f.precision,e.eq(fe))return $(d,n);if(t=e.e,r=e.d.length-1,s=t>=r,o=d.s,s){if((r=v<0?-v:v)<=Ro){for(i=new f(fe),t=Math.ceil(n/j+4),G=!1;r%2&&(i=i.times(d),Co(i.d,t)),r=ct(r/2),r!==0;)d=d.times(d),Co(d.d,t);return G=!0,e.s<0?new f(fe).div(i):$(i,n)}}else if(o<0)throw Error(he+"NaN");return o=o<0&&e.d[Math.max(t,r)]&1?-1:1,d.s=1,G=!1,i=e.times(Vt(d,n+T)),G=!0,i=Io(i),i.s=o,i};S.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?(r=z(i),n=We(i,r<=o.toExpNeg||r>=o.toExpPos)):(Ae(e,1,ut),t===void 0?t=o.rounding:Ae(t,0,8),i=$(new o(i),e,t),r=z(i),n=We(i,e<=r||r<=o.toExpNeg,e)),n};S.toSignificantDigits=S.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(Ae(e,1,ut),t===void 0?t=n.rounding:Ae(t,0,8)),$(new n(r),e,t)};S.toString=S.valueOf=S.val=S.toJSON=S[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=z(e),r=e.constructor;return We(e,t<=r.toExpNeg||t>=r.toExpPos)};De=function(){function e(n,i){var o,s=0,d=n.length;for(n=n.slice();d--;)o=n[d]*i+s,n[d]=o%X|0,s=o/X|0;return s&&n.unshift(s),n}function t(n,i,o,s){var d,f;if(o!=s)f=o>s?1:-1;else for(d=f=0;d<o;d++)if(n[d]!=i[d]){f=n[d]>i[d]?1:-1;break}return f}function r(n,i,o){for(var s=0;o--;)n[o]-=s,s=n[o]<i[o]?1:0,n[o]=s*X+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s){var d,f,T,v,A,R,C,D,I,M,be,le,V,ue,Ge,Jn,Ee,Vr,qr=n.constructor,Nu=n.s==i.s?1:-1,Te=n.d,W=i.d;if(!n.s)return new qr(n);if(!i.s)throw Error(he+"Division by zero");for(f=n.e-i.e,Ee=W.length,Ge=Te.length,C=new qr(Nu),D=C.d=[],T=0;W[T]==(Te[T]||0);)++T;if(W[T]>(Te[T]||0)&&--f,o==null?le=o=qr.precision:s?le=o+(z(n)-z(i))+1:le=o,le<0)return new qr(0);if(le=le/j+2|0,T=0,Ee==1)for(v=0,W=W[0],le++;(T<Ge||v)&&le--;T++)V=v*X+(Te[T]||0),D[T]=V/W|0,v=V%W|0;else{for(v=X/(W[0]+1)|0,v>1&&(W=e(W,v),Te=e(Te,v),Ee=W.length,Ge=Te.length),ue=Ee,I=Te.slice(0,Ee),M=I.length;M<Ee;)I[M++]=0;Vr=W.slice(),Vr.unshift(0),Jn=W[0],W[1]>=X/2&&++Jn;do v=0,d=t(W,I,Ee,M),d<0?(be=I[0],Ee!=M&&(be=be*X+(I[1]||0)),v=be/Jn|0,v>1?(v>=X&&(v=X-1),A=e(W,v),R=A.length,M=I.length,d=t(A,I,R,M),d==1&&(v--,r(A,Ee<R?Vr:W,R))):(v==0&&(d=v=1),A=W.slice()),R=A.length,R<M&&A.unshift(0),r(I,A,M),d==-1&&(M=I.length,d=t(W,I,Ee,M),d<1&&(v++,r(I,Ee<M?Vr:W,M))),M=I.length):d===0&&(v++,I=[0]),D[T++]=v,d&&I[0]?I[M++]=Te[ue]||0:(I=[Te[ue]],M=1);while((ue++<Ge||I[0]!==void 0)&&le--)}return D[0]||D.shift(),C.e=f,$(C,s?o+z(C)+1:o)}}();Zn=ko(zu);fe=new Zn(1);Gr=Zn});var P,ne,a=de(()=>{"use strict";Do();P=class extends Gr{static isDecimal(t){return t instanceof Gr}static random(t=20){{let n=globalThis.crypto.getRandomValues(new Uint8Array(t)).reduce((i,o)=>i+o,"");return new Gr(`0.${n.slice(0,t)}`)}}},ne=P});function ic(){return!1}function Yo(){return{dev:0,ino:0,mode:0,nlink:0,uid:0,gid:0,rdev:0,size:0,blksize:0,blocks:0,atimeMs:0,mtimeMs:0,ctimeMs:0,birthtimeMs:0,atime:new Date,mtime:new Date,ctime:new Date,birthtime:new Date}}function oc(){return Yo()}function sc(){return[]}function ac(e){e(null,[])}function lc(){return""}function uc(){return""}function cc(){}function pc(){}function mc(){}function dc(){}function fc(){}function gc(){}var yc,hc,Zo,Xo=de(()=>{"use strict";l();u();c();p();m();a();yc={},hc={existsSync:ic,lstatSync:Yo,statSync:oc,readdirSync:sc,readdir:ac,readlinkSync:lc,realpathSync:uc,chmodSync:cc,renameSync:pc,mkdirSync:mc,rmdirSync:dc,rmSync:fc,unlinkSync:gc,promises:yc},Zo=hc});var es=oe(()=>{"use strict";l();u();c();p();m();a()});function wc(...e){return e.join("/")}function bc(...e){return e.join("/")}function Ec(e){let t=ts(e),r=rs(e),[n,i]=t.split(".");return{root:"/",dir:r,base:t,ext:i,name:n}}function ts(e){let t=e.split("/");return t[t.length-1]}function rs(e){return e.split("/").slice(0,-1).join("/")}var ns,xc,Pc,Kr,is=de(()=>{"use strict";l();u();c();p();m();a();ns="/",xc={sep:ns},Pc={basename:ts,dirname:rs,join:bc,parse:Ec,posix:xc,resolve:wc,sep:ns},Kr=Pc});var os=oe((my,Tc)=>{Tc.exports={name:"@prisma/internals",version:"6.9.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.1","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e","@prisma/schema-engine-wasm":"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var ri={};$t(ri,{Hash:()=>jt,createHash:()=>ss,default:()=>dt,randomFillSync:()=>Yr,randomUUID:()=>zr,webcrypto:()=>Qt});function zr(){return globalThis.crypto.randomUUID()}function Yr(e,t,r){return t!==void 0&&(r!==void 0?e=e.subarray(t,t+r):e=e.subarray(t)),globalThis.crypto.getRandomValues(e)}function ss(e){return new jt(e)}var Qt,jt,dt,Ke=de(()=>{"use strict";l();u();c();p();m();a();Qt=globalThis.crypto;jt=class{#e=[];#r;constructor(t){this.#r=t}update(t){this.#e.push(t)}async digest(){let t=new Uint8Array(this.#e.reduce((i,o)=>i+o.length,0)),r=0;for(let i of this.#e)t.set(i,r),r+=i.length;let n=await globalThis.crypto.subtle.digest(this.#r,t);return new Uint8Array(n)}},dt={webcrypto:Qt,randomUUID:zr,randomFillSync:Yr,createHash:ss,Hash:jt}});var us=oe((Vy,ls)=>{"use strict";l();u();c();p();m();a();ls.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var ms=oe((eh,ps)=>{"use strict";l();u();c();p();m();a();ps.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var oi=oe((ah,ds)=>{"use strict";l();u();c();p();m();a();var kc=ms();ds.exports=e=>typeof e=="string"?e.replace(kc(),""):e});var fs=oe((xh,en)=>{"use strict";l();u();c();p();m();a();en.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};en.exports.default=en.exports});var ui=oe((Px,ws)=>{"use strict";l();u();c();p();m();a();ws.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var d=0,f,T,v,A,R,C,D,I,M,be,le,V,ue=[];for(f=0;f<i;f++)ue.push(f+1),ue.push(t.charCodeAt(s+f));for(var Ge=ue.length-1;d<o-3;)for(M=r.charCodeAt(s+(T=d)),be=r.charCodeAt(s+(v=d+1)),le=r.charCodeAt(s+(A=d+2)),V=r.charCodeAt(s+(R=d+3)),C=d+=4,f=0;f<Ge;f+=2)D=ue[f],I=ue[f+1],T=e(D,T,v,M,I),v=e(T,v,A,be,I),A=e(v,A,R,le,I),C=e(A,R,C,V,I),ue[f]=C,R=A,A=v,v=T,T=D;for(;d<o;)for(M=r.charCodeAt(s+(T=d)),C=++d,f=0;f<Ge;f+=2)D=ue[f],ue[f]=C=e(D,T,C,M,ue[f+1]),T=D;return C}}()});var Ts=de(()=>{"use strict";l();u();c();p();m();a()});var vs=de(()=>{"use strict";l();u();c();p();m();a()});var Gs=oe((xC,Sp)=>{Sp.exports={name:"@prisma/engines-version",version:"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"81e4af48011447c3cc503a190e86995b66d2a28e"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var bn,Hs=de(()=>{"use strict";l();u();c();p();m();a();bn=class{events={};on(t,r){return this.events[t]||(this.events[t]=[]),this.events[t].push(r),this}emit(t,...r){return this.events[t]?(this.events[t].forEach(n=>{n(...r)}),!0):!1}}});var Ni=oe(et=>{"use strict";l();u();c();p();m();a();Object.defineProperty(et,"__esModule",{value:!0});et.anumber=Mi;et.abytes=Ua;et.ahash=bm;et.aexists=Em;et.aoutput=xm;function Mi(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function wm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Ua(e,...t){if(!wm(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function bm(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Mi(e.outputLen),Mi(e.blockLen)}function Em(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function xm(e,t){Ua(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var ol=oe(k=>{"use strict";l();u();c();p();m();a();Object.defineProperty(k,"__esModule",{value:!0});k.add5L=k.add5H=k.add4H=k.add4L=k.add3H=k.add3L=k.rotlBL=k.rotlBH=k.rotlSL=k.rotlSH=k.rotr32L=k.rotr32H=k.rotrBL=k.rotrBH=k.rotrSL=k.rotrSH=k.shrSL=k.shrSH=k.toBig=void 0;k.fromBig=Ui;k.split=Fa;k.add=Za;var Rn=BigInt(2**32-1),Li=BigInt(32);function Ui(e,t=!1){return t?{h:Number(e&Rn),l:Number(e>>Li&Rn)}:{h:Number(e>>Li&Rn)|0,l:Number(e&Rn)|0}}function Fa(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=Ui(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var $a=(e,t)=>BigInt(e>>>0)<<Li|BigInt(t>>>0);k.toBig=$a;var Va=(e,t,r)=>e>>>r;k.shrSH=Va;var qa=(e,t,r)=>e<<32-r|t>>>r;k.shrSL=qa;var Ba=(e,t,r)=>e>>>r|t<<32-r;k.rotrSH=Ba;var ja=(e,t,r)=>e<<32-r|t>>>r;k.rotrSL=ja;var Qa=(e,t,r)=>e<<64-r|t>>>r-32;k.rotrBH=Qa;var Ga=(e,t,r)=>e>>>r-32|t<<64-r;k.rotrBL=Ga;var Ha=(e,t)=>t;k.rotr32H=Ha;var Ja=(e,t)=>e;k.rotr32L=Ja;var Wa=(e,t,r)=>e<<r|t>>>32-r;k.rotlSH=Wa;var Ka=(e,t,r)=>t<<r|e>>>32-r;k.rotlSL=Ka;var za=(e,t,r)=>t<<r-32|e>>>64-r;k.rotlBH=za;var Ya=(e,t,r)=>e<<r-32|t>>>64-r;k.rotlBL=Ya;function Za(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var Xa=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);k.add3L=Xa;var el=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;k.add3H=el;var tl=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);k.add4L=tl;var rl=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;k.add4H=rl;var nl=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);k.add5L=nl;var il=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;k.add5H=il;var Pm={fromBig:Ui,split:Fa,toBig:$a,shrSH:Va,shrSL:qa,rotrSH:Ba,rotrSL:ja,rotrBH:Qa,rotrBL:Ga,rotr32H:Ha,rotr32L:Ja,rotlSH:Wa,rotlSL:Ka,rotlBH:za,rotlBL:Ya,add:Za,add3L:Xa,add3H:el,add4L:tl,add4H:rl,add5H:il,add5L:nl};k.default=Pm});var sl=oe(Sn=>{"use strict";l();u();c();p();m();a();Object.defineProperty(Sn,"__esModule",{value:!0});Sn.crypto=void 0;var qe=(Ke(),qu(ri));Sn.crypto=qe&&typeof qe=="object"&&"webcrypto"in qe?qe.webcrypto:qe&&typeof qe=="object"&&"randomBytes"in qe?qe:void 0});var ul=oe(L=>{"use strict";l();u();c();p();m();a();Object.defineProperty(L,"__esModule",{value:!0});L.Hash=L.nextTick=L.byteSwapIfBE=L.isLE=void 0;L.isBytes=Tm;L.u8=vm;L.u32=Am;L.createView=Cm;L.rotr=Rm;L.rotl=Sm;L.byteSwap=Vi;L.byteSwap32=Im;L.bytesToHex=km;L.hexToBytes=Dm;L.asyncLoop=Mm;L.utf8ToBytes=ll;L.toBytes=In;L.concatBytes=Nm;L.checkOpts=Lm;L.wrapConstructor=Um;L.wrapConstructorWithOpts=Fm;L.wrapXOFConstructorWithOpts=$m;L.randomBytes=Vm;var Rt=sl(),$i=Ni();function Tm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function vm(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function Am(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function Cm(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Rm(e,t){return e<<32-t|e>>>t}function Sm(e,t){return e<<t|e>>>32-t>>>0}L.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function Vi(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}L.byteSwapIfBE=L.isLE?e=>e:e=>Vi(e);function Im(e){for(let t=0;t<e.length;t++)e[t]=Vi(e[t])}var Om=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function km(e){(0,$i.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=Om[e[r]];return t}var Me={_0:48,_9:57,A:65,F:70,a:97,f:102};function al(e){if(e>=Me._0&&e<=Me._9)return e-Me._0;if(e>=Me.A&&e<=Me.F)return e-(Me.A-10);if(e>=Me.a&&e<=Me.f)return e-(Me.a-10)}function Dm(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=al(e.charCodeAt(o)),d=al(e.charCodeAt(o+1));if(s===void 0||d===void 0){let f=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+f+'" at index '+o)}n[i]=s*16+d}return n}var _m=async()=>{};L.nextTick=_m;async function Mm(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,L.nextTick)(),n+=o)}}function ll(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function In(e){return typeof e=="string"&&(e=ll(e)),(0,$i.abytes)(e),e}function Nm(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,$i.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var Fi=class{clone(){return this._cloneInto()}};L.Hash=Fi;function Lm(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function Um(e){let t=n=>e().update(In(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function Fm(e){let t=(n,i)=>e(i).update(In(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function $m(e){let t=(n,i)=>e(i).update(In(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function Vm(e=32){if(Rt.crypto&&typeof Rt.crypto.getRandomValues=="function")return Rt.crypto.getRandomValues(new Uint8Array(e));if(Rt.crypto&&typeof Rt.crypto.randomBytes=="function")return Rt.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var hl=oe(Q=>{"use strict";l();u();c();p();m();a();Object.defineProperty(Q,"__esModule",{value:!0});Q.shake256=Q.shake128=Q.keccak_512=Q.keccak_384=Q.keccak_256=Q.keccak_224=Q.sha3_512=Q.sha3_384=Q.sha3_256=Q.sha3_224=Q.Keccak=void 0;Q.keccakP=gl;var St=Ni(),fr=ol(),Ne=ul(),ml=[],dl=[],fl=[],qm=BigInt(0),dr=BigInt(1),Bm=BigInt(2),jm=BigInt(7),Qm=BigInt(256),Gm=BigInt(113);for(let e=0,t=dr,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],ml.push(2*(5*n+r)),dl.push((e+1)*(e+2)/2%64);let i=qm;for(let o=0;o<7;o++)t=(t<<dr^(t>>jm)*Gm)%Qm,t&Bm&&(i^=dr<<(dr<<BigInt(o))-dr);fl.push(i)}var[Hm,Jm]=(0,fr.split)(fl,!0),cl=(e,t,r)=>r>32?(0,fr.rotlBH)(e,t,r):(0,fr.rotlSH)(e,t,r),pl=(e,t,r)=>r>32?(0,fr.rotlBL)(e,t,r):(0,fr.rotlSL)(e,t,r);function gl(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let d=(s+8)%10,f=(s+2)%10,T=r[f],v=r[f+1],A=cl(T,v,1)^r[d],R=pl(T,v,1)^r[d+1];for(let C=0;C<50;C+=10)e[s+C]^=A,e[s+C+1]^=R}let i=e[2],o=e[3];for(let s=0;s<24;s++){let d=dl[s],f=cl(i,o,d),T=pl(i,o,d),v=ml[s];i=e[v],o=e[v+1],e[v]=f,e[v+1]=T}for(let s=0;s<50;s+=10){for(let d=0;d<10;d++)r[d]=e[s+d];for(let d=0;d<10;d++)e[s+d]^=~r[(d+2)%10]&r[(d+4)%10]}e[0]^=Hm[n],e[1]^=Jm[n]}r.fill(0)}var gr=class e extends Ne.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,St.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,Ne.u32)(this.state)}keccak(){Ne.isLE||(0,Ne.byteSwap32)(this.state32),gl(this.state32,this.rounds),Ne.isLE||(0,Ne.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,St.aexists)(this);let{blockLen:r,state:n}=this;t=(0,Ne.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let d=0;d<s;d++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,St.aexists)(this,!1),(0,St.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,St.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,St.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};Q.Keccak=gr;var Be=(e,t,r)=>(0,Ne.wrapConstructor)(()=>new gr(t,e,r));Q.sha3_224=Be(6,144,224/8);Q.sha3_256=Be(6,136,256/8);Q.sha3_384=Be(6,104,384/8);Q.sha3_512=Be(6,72,512/8);Q.keccak_224=Be(1,144,224/8);Q.keccak_256=Be(1,136,256/8);Q.keccak_384=Be(1,104,384/8);Q.keccak_512=Be(1,72,512/8);var yl=(e,t,r)=>(0,Ne.wrapXOFConstructorWithOpts)((n={})=>new gr(t,e,n.dkLen===void 0?r:n.dkLen,!0));Q.shake128=yl(31,168,128/8);Q.shake256=yl(31,136,256/8)});var Al=oe((k1,je)=>{"use strict";l();u();c();p();m();a();var{sha3_512:Wm}=hl(),bl=24,yr=32,qi=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function El(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var xl=(e="")=>El(Wm(e)).toString(36).slice(1),wl=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),Km=e=>wl[Math.floor(e()*wl.length)],Pl=({globalObj:e=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+qi(yr,t):qi(yr,t);return xl(n).substring(0,yr)},Tl=e=>()=>e++,zm=476782367,vl=({random:e=Math.random,counter:t=Tl(Math.floor(e()*zm)),length:r=bl,fingerprint:n=Pl({random:e})}={})=>function(){let o=Km(e),s=Date.now().toString(36),d=t().toString(36),f=qi(r,e),T=`${s+f+d+n}`;return`${o+xl(T).substring(1,r)}`},Ym=vl(),Zm=(e,{minLength:t=2,maxLength:r=yr}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};je.exports.getConstants=()=>({defaultLength:bl,bigLength:yr});je.exports.init=vl;je.exports.createId=Ym;je.exports.bufToBigInt=El;je.exports.createCounter=Tl;je.exports.createFingerprint=Pl;je.exports.isCuid=Zm});var Cl=oe((F1,hr)=>{"use strict";l();u();c();p();m();a();var{createId:Xm,init:ed,getConstants:td,isCuid:rd}=Al();hr.exports.createId=Xm;hr.exports.init=ed;hr.exports.getConstants=td;hr.exports.isCuid=rd});l();u();c();p();m();a();var No={};$t(No,{defineExtension:()=>_o,getExtensionContext:()=>Mo});l();u();c();p();m();a();l();u();c();p();m();a();function _o(e){return typeof e=="function"?e:t=>t.$extends(e)}l();u();c();p();m();a();function Mo(e){return e}var Uo={};$t(Uo,{validator:()=>Lo});l();u();c();p();m();a();l();u();c();p();m();a();function Lo(...e){return t=>t}l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();var Xn,Fo,$o,Vo,qo=!0;typeof g<"u"&&({FORCE_COLOR:Xn,NODE_DISABLE_COLORS:Fo,NO_COLOR:$o,TERM:Vo}=g.env||{},qo=g.stdout&&g.stdout.isTTY);var Xu={enabled:!Fo&&$o==null&&Vo!=="dumb"&&(Xn!=null&&Xn!=="0"||qo)};function q(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!Xu.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var rg=q(0,0),Hr=q(1,22),Jr=q(2,22),ng=q(3,23),Wr=q(4,24),ig=q(7,27),og=q(8,28),sg=q(9,29),ag=q(30,39),pt=q(31,39),Bo=q(32,39),jo=q(33,39),Qo=q(34,39),lg=q(35,39),Go=q(36,39),ug=q(37,39),Ho=q(90,39),cg=q(90,39),pg=q(40,49),mg=q(41,49),dg=q(42,49),fg=q(43,49),gg=q(44,49),yg=q(45,49),hg=q(46,49),wg=q(47,49);l();u();c();p();m();a();var ec=100,Jo=["green","yellow","blue","magenta","cyan","red"],qt=[],Wo=Date.now(),tc=0,ei=typeof g<"u"?g.env:{};globalThis.DEBUG??=ei.DEBUG??"";globalThis.DEBUG_COLORS??=ei.DEBUG_COLORS?ei.DEBUG_COLORS==="true":!0;var Bt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function rc(e){let t={color:Jo[tc++%Jo.length],enabled:Bt.enabled(e),namespace:e,log:Bt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:d}=t;if(n.length!==0&&qt.push([o,...n]),qt.length>ec&&qt.shift(),Bt.enabled(o)||i){let f=n.map(v=>typeof v=="string"?v:nc(v)),T=`+${Date.now()-Wo}ms`;Wo=Date.now(),d(o,...f,T)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var Y=new Proxy(rc,{get:(e,t)=>Bt[t],set:(e,t,r)=>Bt[t]=r});function nc(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function Ko(e=7500){let t=qt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function zo(){qt.length=0}l();u();c();p();m();a();l();u();c();p();m();a();var vc=os(),ti=vc.version;l();u();c();p();m();a();function mt(e){let t=Ac();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":Cc(e))}function Ac(){let e=g.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function Cc(e){return e?.previewFeatures.includes("queryCompiler")?"client":"library"}l();u();c();p();m();a();var as="prisma+postgres",Zr=`${as}:`;function Xr(e){return e?.toString().startsWith(`${Zr}//`)??!1}function ni(e){if(!Xr(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")}var Ht={};$t(Ht,{error:()=>Ic,info:()=>Sc,log:()=>Rc,query:()=>Oc,should:()=>cs,tags:()=>Gt,warn:()=>ii});l();u();c();p();m();a();var Gt={error:pt("prisma:error"),warn:jo("prisma:warn"),info:Go("prisma:info"),query:Qo("prisma:query")},cs={warn:()=>!g.env.PRISMA_DISABLE_WARNINGS};function Rc(...e){console.log(...e)}function ii(e,...t){cs.warn()&&console.warn(`${Gt.warn} ${e}`,...t)}function Sc(e,...t){console.info(`${Gt.info} ${e}`,...t)}function Ic(e,...t){console.error(`${Gt.error} ${e}`,...t)}function Oc(e,...t){console.log(`${Gt.query} ${e}`,...t)}l();u();c();p();m();a();function xe(e,t){throw new Error(t)}l();u();c();p();m();a();function si(e,t){return Object.prototype.hasOwnProperty.call(e,t)}l();u();c();p();m();a();function ft(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}l();u();c();p();m();a();function ai(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}l();u();c();p();m();a();function O(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}l();u();c();p();m();a();var gs=new Set,tn=(e,t,...r)=>{gs.has(e)||(gs.add(e),ii(t,...r))};var U=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};O(U,"PrismaClientInitializationError");l();u();c();p();m();a();var ee=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};O(ee,"PrismaClientKnownRequestError");l();u();c();p();m();a();var pe=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};O(pe,"PrismaClientRustPanicError");l();u();c();p();m();a();var se=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};O(se,"PrismaClientUnknownRequestError");l();u();c();p();m();a();var ie=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};O(ie,"PrismaClientValidationError");l();u();c();p();m();a();a();function ze(e){return e===null?e:Array.isArray(e)?e.map(ze):typeof e=="object"?Dc(e)?_c(e):e.constructor!==null&&e.constructor.name!=="Object"?e:ft(e,ze):e}function Dc(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function _c({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new ne(t);case"Json":return JSON.parse(t);default:xe(t,"Unknown tagged value")}}l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();var Ce=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};l();u();c();p();m();a();function Fe(e){return e.substring(0,1).toLowerCase()+e.substring(1)}l();u();c();p();m();a();function hs(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}l();u();c();p();m();a();function Jt(e){let t;return{get(){return t||(t={value:e()}),t.value}}}l();u();c();p();m();a();function Mc(e){return{models:li(e.models),enums:li(e.enums),types:li(e.types)}}function li(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}l();u();c();p();m();a();function gt(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function rn(e){return e.toString()!=="Invalid Date"}l();u();c();p();m();a();a();function yt(e){return P.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}l();u();c();p();m();a();l();u();c();p();m();a();var nn={};$t(nn,{ModelAction:()=>Wt,datamodelEnumToSchemaEnum:()=>Nc});l();u();c();p();m();a();l();u();c();p();m();a();function Nc(e){return{name:e.name,values:e.values.map(t=>t.name)}}l();u();c();p();m();a();var Wt=(V=>(V.findUnique="findUnique",V.findUniqueOrThrow="findUniqueOrThrow",V.findFirst="findFirst",V.findFirstOrThrow="findFirstOrThrow",V.findMany="findMany",V.create="create",V.createMany="createMany",V.createManyAndReturn="createManyAndReturn",V.update="update",V.updateMany="updateMany",V.updateManyAndReturn="updateManyAndReturn",V.upsert="upsert",V.delete="delete",V.deleteMany="deleteMany",V.groupBy="groupBy",V.count="count",V.aggregate="aggregate",V.findRaw="findRaw",V.aggregateRaw="aggregateRaw",V))(Wt||{});var Lc=ke(us());var Uc={red:pt,gray:Ho,dim:Jr,bold:Hr,underline:Wr,highlightSource:e=>e.highlight()},Fc={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function $c({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function Vc({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let d=[""],f=t?" in":":";if(n?(d.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),d.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${f}`))):d.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${f}`)),t&&d.push(s.underline(qc(t))),i){d.push("");let T=[i.toString()];o&&(T.push(o),T.push(s.dim(")"))),d.push(T.join("")),o&&d.push("")}else d.push(""),o&&d.push(o),d.push("");return d.push(r),d.join(`
`)}function qc(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function on(e){let t=e.showColors?Uc:Fc,r;return typeof $getTemplateParameters<"u"?r=$getTemplateParameters(e,t):r=$c(e),Vc(r,t)}l();u();c();p();m();a();var Cs=ke(ui());l();u();c();p();m();a();function xs(e,t,r){let n=Ps(e),i=Bc(n),o=Qc(i);o?sn(o,t,r):t.addErrorMessage(()=>"Unknown error")}function Ps(e){return e.errors.flatMap(t=>t.kind==="Union"?Ps(t):[t])}function Bc(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:jc(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function jc(e,t){return[...new Set(e.concat(t))]}function Qc(e){return ai(e,(t,r)=>{let n=bs(t),i=bs(r);return n!==i?n-i:Es(t)-Es(r)})}function bs(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function Es(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}l();u();c();p();m();a();var ge=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};l();u();c();p();m();a();l();u();c();p();m();a();vs();l();u();c();p();m();a();var ht=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};Ts();l();u();c();p();m();a();l();u();c();p();m();a();var an=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};l();u();c();p();m();a();var ln=e=>e,un={bold:ln,red:ln,green:ln,dim:ln,enabled:!1},As={bold:Hr,red:pt,green:Bo,dim:Jr,enabled:!0},wt={write(e){e.writeLine(",")}};l();u();c();p();m();a();var Re=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};l();u();c();p();m();a();var $e=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var bt=class extends $e{items=[];addItem(t){return this.items.push(new an(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new Re("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(wt,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var Et=class e extends $e{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let d;if(o.value instanceof e?d=o.value.getField(s):o.value instanceof bt&&(d=o.value.getField(Number(s))),!d)return;o=d}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new Re("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(wt,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};l();u();c();p();m();a();var te=class extends $e{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new Re(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};l();u();c();p();m();a();var Kt=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(wt,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function sn(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":Gc(e,t);break;case"IncludeOnScalar":Hc(e,t);break;case"EmptySelection":Jc(e,t,r);break;case"UnknownSelectionField":Yc(e,t);break;case"InvalidSelectionValue":Zc(e,t);break;case"UnknownArgument":Xc(e,t);break;case"UnknownInputField":ep(e,t);break;case"RequiredArgumentMissing":tp(e,t);break;case"InvalidArgumentType":rp(e,t);break;case"InvalidArgumentValue":np(e,t);break;case"ValueTooLarge":ip(e,t);break;case"SomeFieldsMissing":op(e,t);break;case"TooManyFieldsGiven":sp(e,t);break;case"Union":xs(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function Gc(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function Hc(e,t){let[r,n]=zt(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new ge(s.name,"true"));t.addErrorMessage(s=>{let d=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?d+=` on model ${s.bold(i.name)}. ${Yt(s)}`:d+=".",d+=`
Note that ${s.bold("include")} statements only accept relation fields.`,d})}function Jc(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){Wc(e,t,i);return}if(n.hasField("select")){Kc(e,t);return}}if(r?.[Fe(e.outputType.name)]){zc(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function Wc(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new ge(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function Kc(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Is(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${Yt(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function zc(e,t){let r=new Kt;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new ge("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=zt(e.selectionPath),d=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(d){let f=d?.value.asObject()??new Et;f.addSuggestion(n),d.value=f}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function Yc(e,t){let r=Os(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":Is(n,e.outputType);break;case"include":ap(n,e.outputType);break;case"omit":lp(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(Yt(n)),i.join(" ")})}function Zc(e,t){let r=Os(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function Xc(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),up(n,e.arguments)),t.addErrorMessage(i=>Rs(i,r,e.arguments.map(o=>o.name)))}function ep(e,t){let[r,n]=zt(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&ks(o,e.inputType)}t.addErrorMessage(o=>Rs(o,n,e.inputType.fields.map(s=>s.name)))}function Rs(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=pp(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(Yt(e)),n.join(" ")}function tp(e,t){let r;t.addErrorMessage(f=>r?.value instanceof te&&r.value.text==="null"?`Argument \`${f.green(o)}\` must not be ${f.red("null")}.`:`Argument \`${f.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=zt(e.argumentPath),s=new Kt,d=n.getDeepFieldValue(i)?.asObject();if(d)if(r=d.getField(o),r&&d.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let f of e.inputTypes[0].fields)s.addField(f.name,f.typeNames.join(" | "));d.addSuggestion(new ge(o,s).makeRequired())}else{let f=e.inputTypes.map(Ss).join(" | ");d.addSuggestion(new ge(o,f).makeRequired())}}function Ss(e){return e.kind==="list"?`${Ss(e.elementType)}[]`:e.name}function rp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=cn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function np(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=cn("or",e.argument.typeNames.map(d=>i.green(d)));o.push(` Expected ${s}.`)}return o.join("")})}function ip(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof te&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function op(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&ks(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${cn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(Yt(i)),o.join(" ")})}function sp(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${cn("and",i.map(d=>o.red(d)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Is(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ge(r.name,"true"))}function ap(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new ge(r.name,"true"))}function lp(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new ge(r.name,"true"))}function up(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new ge(r.name,r.typeNames.join(" | ")))}function Os(e,t){let[r,n]=zt(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),d=i.getFieldValue("omit")?.asObject(),f=o?.getField(n);return o&&f?{parentKind:"select",parent:o,field:f,fieldName:n}:(f=s?.getField(n),s&&f?{parentKind:"include",field:f,parent:s,fieldName:n}:(f=d?.getField(n),d&&f?{parentKind:"omit",field:f,parent:d,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function ks(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ge(r.name,r.typeNames.join(" | ")))}function zt(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function Yt({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function cn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var cp=3;function pp(e,t){let r=1/0,n;for(let i of t){let o=(0,Cs.default)(e,i);o>cp||o<r&&(r=o,n=i)}return n}l();u();c();p();m();a();l();u();c();p();m();a();var Zt=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function xt(e){return e instanceof Zt}l();u();c();p();m();a();var pn=Symbol(),pi=new WeakMap,_e=class{constructor(t){t===pn?pi.set(this,`Prisma.${this._getName()}`):pi.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return pi.get(this)}},Xt=class extends _e{_getNamespace(){return"NullTypes"}},er=class extends Xt{#e};di(er,"DbNull");var tr=class extends Xt{#e};di(tr,"JsonNull");var rr=class extends Xt{#e};di(rr,"AnyNull");var mi={classes:{DbNull:er,JsonNull:tr,AnyNull:rr},instances:{DbNull:new er(pn),JsonNull:new tr(pn),AnyNull:new rr(pn)}};function di(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}l();u();c();p();m();a();var Ds=": ",mn=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+Ds.length}write(t){let r=new Re(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(Ds).write(this.value)}};var fi=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function Pt(e){return new fi(_s(e))}function _s(e){let t=new Et;for(let[r,n]of Object.entries(e)){let i=new mn(r,Ms(n));t.addField(i)}return t}function Ms(e){if(typeof e=="string")return new te(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new te(String(e));if(typeof e=="bigint")return new te(`${e}n`);if(e===null)return new te("null");if(e===void 0)return new te("undefined");if(yt(e))return new te(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return y.isBuffer(e)?new te(`Buffer.alloc(${e.byteLength})`):new te(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=rn(e)?e.toISOString():"Invalid Date";return new te(`new Date("${t}")`)}return e instanceof _e?new te(`Prisma.${e._getName()}`):xt(e)?new te(`prisma.${Fe(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?mp(e):typeof e=="object"?_s(e):new te(Object.prototype.toString.call(e))}function mp(e){let t=new bt;for(let r of e)t.addItem(Ms(r));return t}function dn(e,t){let r=t==="pretty"?As:un,n=e.renderAllMessages(r),i=new ht(0,{colors:r}).write(e).toString();return{message:n,args:i}}function fn({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let d=Pt(e);for(let A of t)sn(A,d,s);let{message:f,args:T}=dn(d,r),v=on({message:f,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:T});throw new ie(v,{clientVersion:o})}l();u();c();p();m();a();l();u();c();p();m();a();function Se(e){return e.replace(/^./,t=>t.toLowerCase())}l();u();c();p();m();a();function Ls(e,t,r){let n=Se(r);return!t.result||!(t.result.$allModels||t.result[n])?e:dp({...e,...Ns(t.name,e,t.result.$allModels),...Ns(t.name,e,t.result[n])})}function dp(e){let t=new Ce,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return ft(e,n=>({...n,needs:r(n.name,new Set)}))}function Ns(e,t,r){return r?ft(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:fp(t,o,i)})):{}}function fp(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function Us(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function Fs(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var gn=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new Ce;modelExtensionsCache=new Ce;queryCallbacksCache=new Ce;clientExtensions=Jt(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=Jt(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>Ls(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=Se(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},Tt=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new gn(t))}isEmpty(){return this.head===void 0}append(t){return new e(new gn(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};l();u();c();p();m();a();var yn=class{constructor(t){this.name=t}};function $s(e){return e instanceof yn}function gp(e){return new yn(e)}l();u();c();p();m();a();l();u();c();p();m();a();var Vs=Symbol(),nr=class{constructor(t){if(t!==Vs)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?gi:t}},gi=new nr(Vs);function Ie(e){return e instanceof nr}var yp={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},qs="explicitly `undefined` values are not allowed";function hi({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=Tt.empty(),callsite:o,clientMethod:s,errorFormat:d,clientVersion:f,previewFeatures:T,globalOmit:v}){let A=new yi({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:d,clientVersion:f,previewFeatures:T,globalOmit:v});return{modelName:e,action:yp[t],query:ir(r,A)}}function ir({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:js(r,n),selection:hp(e,t,i,n)}}function hp(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),xp(e,n)):wp(n,t,r)}function wp(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&bp(n,t,e),Ep(n,r,e),n}function bp(e,t,r){for(let[n,i]of Object.entries(t)){if(Ie(i))continue;let o=r.nestSelection(n);if(wi(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=ir(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=ir(i,o)}}function Ep(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=Fs(i,n);for(let[s,d]of Object.entries(o)){if(Ie(d))continue;wi(d,r.nestSelection(s));let f=r.findField(s);n?.[s]&&!f||(e[s]=!d)}}function xp(e,t){let r={},n=t.getComputedFields(),i=Us(e,n);for(let[o,s]of Object.entries(i)){if(Ie(s))continue;let d=t.nestSelection(o);wi(s,d);let f=t.findField(o);if(!(n?.[o]&&!f)){if(s===!1||s===void 0||Ie(s)){r[o]=!1;continue}if(s===!0){f?.kind==="object"?r[o]=ir({},d):r[o]=!0;continue}r[o]=ir(s,d)}}return r}function Bs(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(gt(e)){if(rn(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if($s(e))return{$type:"Param",value:e.name};if(xt(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return Pp(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:y.from(r,n,i).toString("base64")}}if(Tp(e))return e.values;if(yt(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof _e){if(e!==mi.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(vp(e))return e.toJSON();if(typeof e=="object")return js(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function js(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);Ie(i)||(i!==void 0?r[n]=Bs(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:qs}))}return r}function Pp(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||Ie(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(Bs(o,i))}return r}function Tp(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function vp(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function wi(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:qs})}var yi=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){fn({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[Fe(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:xe(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};l();u();c();p();m();a();function Qs(e){if(!e._hasPreviewFlag("metrics"))throw new ie("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var or=class{_client;constructor(t){this._client=t}prometheus(t){return Qs(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return Qs(this._client),this._client._engine.metrics({format:"json",...t})}};l();u();c();p();m();a();function Ap(e,t){let r=Jt(()=>Cp(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function Cp(e){throw new Error("Prisma.dmmf is not available when running in edge runtimes.")}function bi(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}l();u();c();p();m();a();var Ei=new WeakMap,hn="$$PrismaTypedSql",sr=class{constructor(t,r){Ei.set(this,{sql:t,values:r}),Object.defineProperty(this,hn,{value:hn})}get sql(){return Ei.get(this).sql}get values(){return Ei.get(this).values}};function Rp(e){return(...t)=>new sr(e,t)}function wn(e){return e!=null&&e[hn]===hn}l();u();c();p();m();a();var Mu=ke(Gs());l();u();c();p();m();a();Hs();Xo();is();l();u();c();p();m();a();var ye=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,d)=>s+(d instanceof e?d.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],d=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let f=0;for(;f<s.values.length;)this.values[o++]=s.values[f++],this.strings[o]=s.strings[f];this.strings[o]+=d}else this.values[o++]=s,this.strings[o]=d}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function Ip(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new ye([r,...Array(e.length-1).fill(t),n],e)}function Js(e){return new ye([e],[])}var Op=Js("");function Ws(e,...t){return new ye(e,t)}l();u();c();p();m();a();l();u();c();p();m();a();function ar(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}l();u();c();p();m();a();function ae(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}l();u();c();p();m();a();function Ye(e){let t=new Ce;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}l();u();c();p();m();a();l();u();c();p();m();a();var En={enumerable:!0,configurable:!0,writable:!0};function xn(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>En,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var Ks=Symbol.for("nodejs.util.inspect.custom");function Pe(e,t){let r=kp(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let d=r.get(s);return d?d.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let d=r.get(s);return d?d.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=zs(Reflect.ownKeys(o),r),d=zs(Array.from(r.keys()),r);return[...new Set([...s,...d,...n])]},set(o,s,d){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,d))},getOwnPropertyDescriptor(o,s){let d=Reflect.getOwnPropertyDescriptor(o,s);if(d&&!d.configurable)return d;let f=r.get(s);return f?f.getPropertyDescriptor?{...En,...f?.getPropertyDescriptor(s)}:En:d},defineProperty(o,s,d){return n.add(s),Reflect.defineProperty(o,s,d)},getPrototypeOf:()=>Object.prototype});return i[Ks]=function(){let o={...this};return delete o[Ks],o},i}function kp(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function zs(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}l();u();c();p();m();a();function vt(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}l();u();c();p();m();a();function At(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}l();u();c();p();m();a();function Ys(e){if(e===void 0)return"";let t=Pt(e);return new ht(0,{colors:un}).write(t).toString()}l();u();c();p();m();a();var Dp="P2037";function Pn({error:e,user_facing_error:t},r,n){return t.error_code?new ee(_p(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new se(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function _p(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===Dp&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();var xi=class{getLocation(){return null}};function Ve(e){return typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new xi}l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();var Zs={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function Ct(e={}){let t=Np(e);return Object.entries(t).reduce((n,[i,o])=>(Zs[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function Np(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function Tn(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function Xs(e,t){let r=Tn(e);return t({action:"aggregate",unpacker:r,argsMapper:Ct})(e)}l();u();c();p();m();a();function Lp(e={}){let{select:t,...r}=e;return typeof t=="object"?Ct({...r,_count:t}):Ct({...r,_count:{_all:!0}})}function Up(e={}){return typeof e.select=="object"?t=>Tn(e)(t)._count:t=>Tn(e)(t)._count._all}function ea(e,t){return t({action:"count",unpacker:Up(e),argsMapper:Lp})(e)}l();u();c();p();m();a();function Fp(e={}){let t=Ct(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function $p(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function ta(e,t){return t({action:"groupBy",unpacker:$p(e),argsMapper:Fp})(e)}function ra(e,t,r){if(t==="aggregate")return n=>Xs(n,r);if(t==="count")return n=>ea(n,r);if(t==="groupBy")return n=>ta(n,r)}l();u();c();p();m();a();function na(e,t){let r=t.fields.filter(i=>!i.relationName),n=hs(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new Zt(e,o,s.type,s.isList,s.kind==="enum")},...xn(Object.keys(n))})}l();u();c();p();m();a();l();u();c();p();m();a();var ia=e=>Array.isArray(e)?e:e.split("."),Pi=(e,t)=>ia(t).reduce((r,n)=>r&&r[n],e),oa=(e,t,r)=>ia(t).reduceRight((n,i,o,s)=>Object.assign({},Pi(e,s.slice(0,o)),{[i]:n}),r);function Vp(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function qp(e,t,r){return t===void 0?e??{}:oa(t,r,e||!0)}function Ti(e,t,r,n,i,o){let d=e._runtimeDataModel.models[t].fields.reduce((f,T)=>({...f,[T.name]:T}),{});return f=>{let T=Ve(e._errorFormat),v=Vp(n,i),A=qp(f,o,v),R=r({dataPath:v,callsite:T})(A),C=Bp(e,t);return new Proxy(R,{get(D,I){if(!C.includes(I))return D[I];let be=[d[I].type,r,I],le=[v,A];return Ti(e,...be,...le)},...xn([...C,...Object.getOwnPropertyNames(R)])})}}function Bp(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var jp=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],Qp=["aggregate","count","groupBy"];function vi(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[Gp(e,t),Jp(e,t),ar(r),ae("name",()=>t),ae("$name",()=>t),ae("$parent",()=>e._appliedParent)];return Pe({},n)}function Gp(e,t){let r=Se(t),n=Object.keys(Wt).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=d=>f=>{let T=Ve(e._errorFormat);return e._createPrismaPromise(v=>{let A={args:f,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:v,callsite:T};return e._request({...A,...d})},{action:o,args:f,model:t})};return jp.includes(o)?Ti(e,t,s):Hp(i)?ra(e,i,s):s({})}}}function Hp(e){return Qp.includes(e)}function Jp(e,t){return Ye(ae("fields",()=>{let r=e._runtimeDataModel.models[t];return na(t,r)}))}l();u();c();p();m();a();function sa(e){return e.replace(/^./,t=>t.toUpperCase())}var Ai=Symbol();function lr(e){let t=[Wp(e),Kp(e),ae(Ai,()=>e),ae("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(ar(r)),Pe(e,t)}function Wp(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function Kp(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(Se),n=[...new Set(t.concat(r))];return Ye({getKeys(){return n},getPropertyValue(i){let o=sa(i);if(e._runtimeDataModel.models[o]!==void 0)return vi(e,o);if(e._runtimeDataModel.models[i]!==void 0)return vi(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function aa(e){return e[Ai]?e[Ai]:e}function la(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return lr(t)}l();u();c();p();m();a();l();u();c();p();m();a();function ua({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],d=[];for(let f of Object.values(o)){if(n){if(n[f.name])continue;let T=f.needs.filter(v=>n[v]);T.length>0&&d.push(vt(T))}else if(r){if(!r[f.name])continue;let T=f.needs.filter(v=>!r[v]);T.length>0&&d.push(vt(T))}zp(e,f.needs)&&s.push(Yp(f,Pe(e,s)))}return s.length>0||d.length>0?Pe(e,[...s,...d]):e}function zp(e,t){return t.every(r=>si(e,r))}function Yp(e,t){return Ye(ae(e.name,()=>e.compute(t)))}l();u();c();p();m();a();function vn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=vn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&ca({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&ca({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function ca({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||Ie(s))continue;let f=n.models[r].fields.find(v=>v.name===o);if(!f||f.kind!=="object"||!f.relationName)continue;let T=typeof s=="object"?s:{};t[o]=vn({visitor:i,result:t[o],args:T,modelName:f.type,runtimeDataModel:n})}}function pa({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:vn({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(d,f,T)=>{let v=Se(f);return ua({result:d,modelName:v,select:T.select,omit:T.select?void 0:{...o?.[v],...T.omit},extensions:n})}})}l();u();c();p();m();a();l();u();c();p();m();a();a();l();u();c();p();m();a();var Zp=["$connect","$disconnect","$on","$transaction","$use","$extends"],ma=Zp;function da(e){if(e instanceof ye)return Xp(e);if(wn(e))return em(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=ur(e[n]);return r}let t={};for(let r in e)t[r]=ur(e[r]);return t}function Xp(e){return new ye(e.strings,e.values)}function em(e){return new sr(e.sql,e.values)}function ur(e){if(typeof e!="object"||e==null||e instanceof _e||xt(e))return e;if(yt(e))return new ne(e.toFixed());if(gt(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=ur(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:ur(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=ur(e[r]);return t}xe(e,"Unknown value")}function ga(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:da(t.args??{}),__internalParams:t,query:(s,d=t)=>{let f=d.customDataProxyFetch;return d.customDataProxyFetch=ba(o,f),d.args=s,ga(e,d,r,n+1)}})})}function ya(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return ga(e,t,s)}function ha(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?wa(r,n,0,e):e(r)}}function wa(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,d=e){let f=d.customDataProxyFetch;return d.customDataProxyFetch=ba(i,f),wa(d,t,r+1,n)}})}var fa=e=>e;function ba(e=fa,t=fa){return r=>e(t(r))}l();u();c();p();m();a();var Ea=Y("prisma:client"),xa={Vercel:"vercel","Netlify CI":"netlify"};function Pa({postinstall:e,ciName:t,clientVersion:r}){if(Ea("checkPlatformCaching:postinstall",e),Ea("checkPlatformCaching:ciName",t),e===!0&&t&&t in xa){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${xa[t]}-build`;throw console.error(n),new U(n,r)}}l();u();c();p();m();a();function Ta(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}l();u();c();p();m();a();l();u();c();p();m();a();var tm=()=>globalThis.process?.release?.name==="node",rm=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,nm=()=>!!globalThis.Deno,im=()=>typeof globalThis.Netlify=="object",om=()=>typeof globalThis.EdgeRuntime=="object",sm=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function am(){return[[im,"netlify"],[om,"edge-light"],[sm,"workerd"],[nm,"deno"],[rm,"bun"],[tm,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var lm={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function Ze(){let e=am();return{id:e,prettyName:lm[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}l();u();c();p();m();a();l();u();c();p();m();a();var Ci=ke(oi());l();u();c();p();m();a();function va(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}l();u();c();p();m();a();function Aa(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}l();u();c();p();m();a();var Ca=ke(fs());function Ra({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,Ca.default)({user:t,repo:r,template:n,title:e,body:i})}function Sa({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let d=Ko(6e3-(s?.length??0)),f=Aa((0,Ci.default)(d)),T=n?`# Description
\`\`\`
${n}
\`\`\``:"",v=(0,Ci.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${g.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${T}

## Logs
\`\`\`
${f}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?va(s):""}
\`\`\`
`),A=Ra({title:r,body:v});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${Wr(A)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();a();l();u();c();p();m();a();a();function J(e,t){throw new Error(t)}function Ri(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>Ri(e[r],t[r]))}function cr(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>{if(typeof e[o]!=typeof t[o]){if(typeof e[o]=="number"||typeof t[o]=="number")return`${e[o]}`==`${t[o]}`;if(typeof e[o]=="bigint"||typeof t[o]=="bigint")return BigInt(`${e[o]}`.replace(/n$/,""))===BigInt(`${t[o]}`.replace(/n$/,""));if(e[o]instanceof Date||t[o]instanceof Date)return new Date(`${e[o]}`).getTime()===new Date(`${t[o]}`).getTime();if(ne.isDecimal(e[o])||ne.isDecimal(t[o]))return new ne(`${e[o]}`).equals(new ne(`${t[o]}`))}return Ri(e[o],t[o])})}function pr(e){return JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():r instanceof Uint8Array?y.from(r).toString("base64"):r)}var H=class extends Error{name="DataMapperError"};function ka(e,t,r){switch(t.type){case"AffectedRows":if(typeof e!="number")throw new H(`Expected an affected rows count, got: ${typeof e} (${e})`);return{count:e};case"Object":return Si(e,t.fields,r);case"Value":return Ii(e,"<result>",t.resultType,r);default:J(t,`Invalid data mapping type: '${t.type}'`)}}function Si(e,t,r){if(e===null)return null;if(Array.isArray(e))return e.map(i=>Ia(i,t,r));if(typeof e=="object")return Ia(e,t,r);if(typeof e=="string"){let n;try{n=JSON.parse(e)}catch(i){throw new H("Expected an array or object, got a string that is not valid JSON",{cause:i})}return Si(n,t,r)}throw new H(`Expected an array or an object, got: ${typeof e}`)}function Ia(e,t,r){if(typeof e!="object")throw new H(`Expected an object, but got '${typeof e}'`);let n={};for(let[i,o]of Object.entries(t))switch(o.type){case"AffectedRows":throw new H(`Unexpected 'AffectedRows' node in data mapping for field '${i}'`);case"Object":{if(!o.flattened&&!Object.hasOwn(e,i))throw new H(`Missing data field (Object): '${i}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`);let s=o.flattened?e:e[i];n[i]=Si(s,o.fields,r);break}case"Value":{let s=o.dbName;if(Object.hasOwn(e,s))n[i]=Ii(e[s],s,o.resultType,r);else throw new H(`Missing data field (Value): '${s}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`)}break;default:J(o,`DataMapper: Invalid data mapping node type: '${o.type}'`)}return n}function Ii(e,t,r,n){if(e===null)return r.type==="Array"?[]:null;switch(r.type){case"Any":return e;case"String":{if(typeof e!="string")throw new H(`Expected a string in column '${t}', got ${typeof e}: ${e}`);return e}case"Int":switch(typeof e){case"number":return Math.trunc(e);case"string":{let i=Math.trunc(Number(e));if(Number.isNaN(i)||!Number.isFinite(i))throw new H(`Expected an integer in column '${t}', got string: ${e}`);if(!Number.isSafeInteger(i))throw new H(`Integer value in column '${t}' is too large to represent as a JavaScript number without loss of precision, got: ${e}. Consider using BigInt type.`);return i}default:throw new H(`Expected an integer in column '${t}', got ${typeof e}: ${e}`)}case"BigInt":{if(typeof e!="number"&&typeof e!="string")throw new H(`Expected a bigint in column '${t}', got ${typeof e}: ${e}`);return{$type:"BigInt",value:e}}case"Float":{if(typeof e=="number")return e;if(typeof e=="string"){let i=Number(e);if(Number.isNaN(i)&&!/^[-+]?nan$/.test(e.toLowerCase()))throw new H(`Expected a float in column '${t}', got string: ${e}`);return i}throw new H(`Expected a float in column '${t}', got ${typeof e}: ${e}`)}case"Boolean":{if(typeof e=="boolean")return e;if(typeof e=="number")return e===1;if(typeof e=="string"){if(e==="true"||e==="TRUE"||e==="1")return!0;if(e==="false"||e==="FALSE"||e==="0")return!1;throw new H(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}throw new H(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}case"Decimal":if(typeof e!="number"&&typeof e!="string"&&!ne.isDecimal(e))throw new H(`Expected a decimal in column '${t}', got ${typeof e}: ${e}`);return{$type:"Decimal",value:e};case"Date":{if(typeof e=="string")return{$type:"DateTime",value:Oa(e)};if(typeof e=="number"||e instanceof Date)return{$type:"DateTime",value:e};throw new H(`Expected a date in column '${t}', got ${typeof e}: ${e}`)}case"Time":{if(typeof e=="string")return{$type:"DateTime",value:`1970-01-01T${Oa(e)}`};throw new H(`Expected a time in column '${t}', got ${typeof e}: ${e}`)}case"Array":return e.map((o,s)=>Ii(o,`${t}[${s}]`,r.inner,n));case"Object":return{$type:"Json",value:typeof e=="string"?e:pr(e)};case"Bytes":{if(typeof e=="string"&&e.startsWith("\\x"))return{$type:"Bytes",value:y.from(e.slice(2),"hex").toString("base64")};if(Array.isArray(e))return{$type:"Bytes",value:y.from(e).toString("base64")};throw new H(`Expected a byte array in column '${t}', got ${typeof e}: ${e}`)}case"Enum":{let i=n[r.inner];if(i===void 0)throw new H(`Unknown enum '${r.inner}'`);let o=i[`${e}`];if(o===void 0)throw new H(`Unknown enum value '${e}' for enum '${r.inner}'`);return o}default:J(r,`DataMapper: Unknown result type: ${r.type}`)}}var um=/Z$|(?<!\d{4}-\d{2})[+-]\d{2}(:?\d{2})?$/;function Oa(e){let t=um.exec(e);return t===null?`${e}Z`:t[0]!=="Z"&&t[1]===void 0?`${e}:00`:e}l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();var mr;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(mr||(mr={}));function cm(e){switch(e){case"postgres":return"postgresql";case"mysql":return"mysql";case"sqlite":return"sqlite";default:J(e,`Unknown provider: ${e}`)}}async function An({query:e,queryable:t,tracingHelper:r,onQuery:n,execute:i}){return await r.runInChildSpan({name:"db_query",kind:mr.CLIENT,attributes:{"db.query.text":e.sql,"db.system.name":cm(t.provider)}},async()=>{let o=new Date,s=w.now(),d=await i(),f=w.now();return n?.({timestamp:o,duration:f-s,query:e.sql,params:e.args}),d})}l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();function Oi(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}l();u();c();p();m();a();var _={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Character:6,Text:7,Date:8,Time:9,DateTime:10,Json:11,Enum:12,Bytes:13,Set:14,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,EnumArray:76,BytesArray:77,UuidArray:78,UnknownNumber:128};var Oe=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n??{}}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function Da(e){if(!Oi(e))throw e;let t=pm(e),r=mm(e);throw!t||!r?e:new Oe(r,t,{driverAdapterError:e})}function pm(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":return"P2023";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":return;default:J(e.cause,`Unknown error: ${e.cause}`)}}function mm(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${ki({fields:e.cause.fields})}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${ki(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${ki({fields:e.cause.fields})}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Invalid isolation level \`${e.cause.level}\``;case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":return;default:J(e.cause,`Unknown error: ${e.cause}`)}}function ki(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();l();u();c();p();m();a();function Xe(e,t){var r="000000000"+e;return r.substr(r.length-t)}var _a=ke(es(),1);function dm(){try{return _a.default.hostname()}catch{return g.env._CLUSTER_NETWORK_NAME_||g.env.COMPUTERNAME||"hostname"}}var Ma=2,fm=Xe(g.pid.toString(36),Ma),Na=dm(),gm=Na.length,ym=Xe(Na.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+gm+36).toString(36),Ma);function Di(){return fm+ym}l();u();c();p();m();a();l();u();c();p();m();a();function Cn(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function _i(e){let n=Math.pow(36,4),i=0;function o(){return Xe((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function d(){var f="c",T=new Date().getTime().toString(36),v=Xe(s().toString(36),4),A=e(),R=o()+o();return f+T+v+A+R}return d.fingerprint=e,d.isCuid=Cn,d}var hm=_i(Di);var La=hm;var _l=ke(Cl());l();u();c();p();m();a();Ke();l();u();c();p();m();a();var Rl="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var nd=128,tt,It;function id(e){!tt||tt.length<e?(tt=y.allocUnsafe(e*nd),Qt.getRandomValues(tt),It=0):It+e>tt.length&&(Qt.getRandomValues(tt),It=0),It+=e}function Bi(e=21){id(e|=0);let t="";for(let r=It-e;r<It;r++)t+=Rl[tt[r]&63];return t}l();u();c();p();m();a();Ke();var Il="0123456789ABCDEFGHJKMNPQRSTVWXYZ",wr=32;var od=16,Ol=10,Sl=0xffffffffffff;var rt;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(rt||(rt={}));var nt=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function sd(e){let t=Math.floor(e()*wr);return t===wr&&(t=wr-1),Il.charAt(t)}function ad(e){let t=ld(),r=t&&(t.crypto||t.msCrypto)||(typeof dt<"u"?dt:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(dt?.randomBytes)return()=>dt.randomBytes(1).readUInt8()/255;throw new nt(rt.PRNGDetectFailure,"Failed to find a reliable PRNG")}function ld(){return pd()?self:typeof window<"u"?window:typeof globalThis<"u"||typeof globalThis<"u"?globalThis:null}function ud(e,t){let r="";for(;e>0;e--)r=sd(t)+r;return r}function cd(e,t=Ol){if(isNaN(e))throw new nt(rt.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>Sl)throw new nt(rt.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${Sl}: ${e}`);if(e<0)throw new nt(rt.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new nt(rt.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%wr,n=Il.charAt(r)+n,e=(e-r)/wr;return n}function pd(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function kl(e,t){let r=t||ad(),n=!e||isNaN(e)?Date.now():e;return cd(n,Ol)+ud(od,r)}l();u();c();p();m();a();l();u();c();p();m();a();var re=[];for(let e=0;e<256;++e)re.push((e+256).toString(16).slice(1));function On(e,t=0){return(re[e[t+0]]+re[e[t+1]]+re[e[t+2]]+re[e[t+3]]+"-"+re[e[t+4]]+re[e[t+5]]+"-"+re[e[t+6]]+re[e[t+7]]+"-"+re[e[t+8]]+re[e[t+9]]+"-"+re[e[t+10]]+re[e[t+11]]+re[e[t+12]]+re[e[t+13]]+re[e[t+14]]+re[e[t+15]]).toLowerCase()}l();u();c();p();m();a();Ke();var Dn=new Uint8Array(256),kn=Dn.length;function Ot(){return kn>Dn.length-16&&(Yr(Dn),kn=0),Dn.slice(kn,kn+=16)}l();u();c();p();m();a();l();u();c();p();m();a();Ke();var ji={randomUUID:zr};function md(e,t,r){if(ji.randomUUID&&!t&&!e)return ji.randomUUID();e=e||{};let n=e.random??e.rng?.()??Ot();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return On(n)}var Qi=md;l();u();c();p();m();a();var Gi={};function dd(e,t,r){let n;if(e)n=Dl(e.random??e.rng?.()??Ot(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=Ot();fd(Gi,i,o),n=Dl(o,Gi.msecs,Gi.seq,t,r)}return t??On(n)}function fd(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function Dl(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var Hi=dd;var _n=class{#e={};constructor(){this.register("uuid",new Ki),this.register("cuid",new zi),this.register("ulid",new Yi),this.register("nanoid",new Zi),this.register("product",new Xi)}snapshot(t){return Object.create(this.#e,{now:{value:t==="mysql"?new Wi:new Ji}})}register(t,r){this.#e[t]=r}},Ji=class{#e=new Date;generate(){return this.#e.toISOString()}},Wi=class{#e=new Date;generate(){return this.#e.toISOString().replace("T"," ").replace("Z","")}},Ki=class{generate(t){if(t===4)return Qi();if(t===7)return Hi();throw new Error("Invalid UUID generator arguments")}},zi=class{generate(t){if(t===1)return La();if(t===2)return(0,_l.createId)();throw new Error("Invalid CUID generator arguments")}},Yi=class{generate(){return kl()}},Zi=class{generate(t){if(typeof t=="number")return Bi(t);if(t===void 0)return Bi();throw new Error("Invalid Nanoid generator arguments")}},Xi=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};l();u();c();p();m();a();l();u();c();p();m();a();function eo(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function to(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function Ml(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bytes"}function Nl(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bigint"}function no(e,t,r){let n=e.type;switch(n){case"rawSql":return Ul(e.sql,Ll(e.params,t,r));case"templateSql":return gd(e.fragments,e.placeholderFormat,Ll(e.params,t,r));default:J(n,"Invalid query type")}}function Ll(e,t,r){return e.map(n=>br(n,t,r))}function br(e,t,r){let n=e;for(;hd(n);)if(eo(n)){let i=t[n.prisma__value.name];if(i===void 0)throw new Error(`Missing value for query variable ${n.prisma__value.name}`);n=i}else if(to(n)){let{name:i,args:o}=n.prisma__value,s=r[i];if(!s)throw new Error(`Encountered an unknown generator '${i}'`);n=s.generate(...o.map(d=>br(d,t,r)))}else J(n,`Unexpected unevaluated value type: ${n}`);return Array.isArray(n)?n=n.map(i=>br(i,t,r)):Ml(n)?n=y.from(n.prisma__value,"base64"):Nl(n)&&(n=BigInt(n.prisma__value)),n}function gd(e,t,r){let n=0,i=1,o=[],s=e.map(d=>{let f=d.type;switch(f){case"parameter":if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);return o.push(r[n++]),ro(t,i++);case"stringChunk":return d.value;case"parameterTuple":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let T=r[n++],v=Array.isArray(T)?T:[T];return`(${v.length==0?"NULL":v.map(R=>(o.push(R),ro(t,i++))).join(",")})`}case"parameterTupleList":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let T=r[n++];if(!Array.isArray(T))throw new Error("Malformed query template. Tuple list expected.");if(T.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");return T.map(A=>{if(!Array.isArray(A))throw new Error("Malformed query template. Tuple expected.");return`(${A.map(C=>(o.push(C),ro(t,i++))).join(",")})`}).join(",")}default:J(f,"Invalid fragment type")}}).join("");return Ul(s,o)}function ro(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function Ul(e,t){let r=t.map(n=>yd(n));return{sql:e,args:t,argTypes:r}}function yd(e){return typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":y.isBuffer(e)?"Bytes":"Unknown"}function hd(e){return eo(e)||to(e)}l();u();c();p();m();a();function $l(e){return e.rows.map(t=>t.reduce((r,n,i)=>{let o=e.columnNames[i].split("."),s=r;for(let d=0;d<o.length;d++){let f=o[d];d===o.length-1?s[f]=n:(s[f]===void 0&&(s[f]={}),s=s[f])}return r},{}))}function Vl(e){let r=e.columnTypes.map(n=>Fl(n)).map(n=>{switch(n){case"int":return i=>i===null?null:typeof i=="number"?i:parseInt(`${i}`,10);case"bigint":return i=>i===null?null:typeof i=="bigint"?i:BigInt(`${i}`);case"json":return i=>typeof i=="string"?JSON.parse(i):i;case"bool":return i=>typeof i=="string"?i==="true"||i==="1":typeof i=="number"?i===1:i;default:return i=>i}});return{columns:e.columnNames,types:e.columnTypes.map(n=>Fl(n)),rows:e.rows.map(n=>n.map((i,o)=>r[o](i)))}}function Fl(e){switch(e){case _.Int32:return"int";case _.Int64:return"bigint";case _.Float:return"float";case _.Double:return"double";case _.Text:return"string";case _.Enum:return"enum";case _.Bytes:return"bytes";case _.Boolean:return"bool";case _.Character:return"char";case _.Numeric:return"decimal";case _.Json:return"json";case _.Uuid:return"uuid";case _.DateTime:return"datetime";case _.Date:return"date";case _.Time:return"time";case _.Int32Array:return"int-array";case _.Int64Array:return"bigint-array";case _.FloatArray:return"float-array";case _.DoubleArray:return"double-array";case _.TextArray:return"string-array";case _.EnumArray:return"string-array";case _.BytesArray:return"bytes-array";case _.BooleanArray:return"bool-array";case _.CharacterArray:return"char-array";case _.NumericArray:return"decimal-array";case _.JsonArray:return"json-array";case _.UuidArray:return"uuid-array";case _.DateTimeArray:return"datetime-array";case _.DateArray:return"date-array";case _.TimeArray:return"time-array";case _.UnknownNumber:return"unknown";case _.Set:return"string";default:J(e,`Unexpected column type: ${e}`)}}l();u();c();p();m();a();function ql(e,t,r){if(!t.every(n=>io(e,n))){let n=wd(e,r),i=bd(r);throw new Oe(n,i,r.context)}}function io(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"affectedRowCountEq":return e===t.args;case"never":return!1;default:J(t,`Unknown rule type: ${t.type}`)}}function wd(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:e}.`;case"INCOMPLETE_CONNECT_OUTPUT":return`The required connected records were not found. Expected ${t.context.expectedRows} records to be connected after connect operation on ${t.context.relationType} relation '${t.context.relation}', found ${Array.isArray(e)?e.length:e}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:J(t,`Unknown error identifier: ${t}`)}}function bd(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"INCOMPLETE_CONNECT_OUTPUT":return"P2018";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:J(e,`Unknown error identifier: ${e}`)}}var kt=class e{#e;#r;#t;#o=new _n;#n;#s;#i;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o,rawSerializer:s}){this.#e=t,this.#r=r,this.#t=n,this.#n=i,this.#s=o,this.#i=s??o}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:$l,rawSerializer:Vl})}async run(t,r){let{value:n}=await this.interpretNode(t,r,this.#r,this.#o.snapshot(r.provider)).catch(i=>Da(i));return n}async interpretNode(t,r,n,i){switch(t.type){case"seq":{let o;for(let s of t.args)o=await this.interpretNode(s,r,n,i);return o??{value:void 0}}case"get":return{value:n[t.args.name]};case"let":{let o=Object.create(n);for(let s of t.args.bindings){let{value:d}=await this.interpretNode(s.expr,r,o,i);o[s.name]=d}return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!Bl(s))return{value:s}}return{value:[]}}case"concat":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(d=>d.value)));return{value:o.length>0?o.reduce((s,d)=>s.concat(Er(d)),[]):[]}}case"sum":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(d=>d.value)));return{value:o.length>0?o.reduce((s,d)=>jl(s)+jl(d)):0}}case"execute":{let o=no(t.args,n,i);return this.#a(o,r,async()=>({value:await r.executeRaw(o)}))}case"query":{let o=no(t.args,n,i);return this.#a(o,r,async()=>{let s=await r.queryRaw(o);return t.args.type==="rawSql"?{value:this.#i(s),lastInsertId:s.lastInsertId}:{value:this.#s(s),lastInsertId:s.lastInsertId}})}case"reverse":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);return{value:Array.isArray(o)?o.reverse():o,lastInsertId:s}}case"unique":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return{value:o,lastInsertId:s};if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return{value:o[0]??null,lastInsertId:s}}case"required":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(Bl(o))throw new Error("Required value is empty");return{value:o,lastInsertId:s}}case"mapField":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.records,r,n,i);return{value:Wl(o,t.args.field),lastInsertId:s}}case"join":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.parent,r,n,i);if(o===null)return{value:null,lastInsertId:s};let d=await Promise.all(t.args.children.map(async f=>({joinExpr:f,childRecords:(await this.interpretNode(f.child,r,n,i)).value})));if(Array.isArray(o)){for(let f of o)Ql(xr(f),d);return{value:o,lastInsertId:s}}return{value:Ql(xr(o),d),lastInsertId:s}}case"transaction":{if(!this.#e.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#e.manager,s=await o.startTransaction(),d=o.getTransaction(s,"query");try{let f=await this.interpretNode(t.args,d,n,i);return await o.commitTransaction(s.id),f}catch(f){throw await o.rollbackTransaction(s.id),f}}case"dataMap":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:ka(o,t.args.structure,t.args.enums),lastInsertId:s}}case"validate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return ql(o,t.args.rules,t.args),{value:o,lastInsertId:s}}case"if":{let{value:o}=await this.interpretNode(t.args.value,r,n,i);return io(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return{value:void 0};case"diff":{let{value:o}=await this.interpretNode(t.args.from,r,n,i),{value:s}=await this.interpretNode(t.args.to,r,n,i),d=new Set(Er(s));return{value:Er(o).filter(f=>!d.has(f))}}case"distinctBy":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),d=new Set,f=[];for(let T of Er(o)){let v=Jl(T,t.args.fields);d.has(v)||(d.add(v),f.push(T))}return{value:f,lastInsertId:s}}case"paginate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),d=Er(o),f=t.args.pagination.linkingFields;if(f!==null){let T=new Map;for(let A of d){let R=Jl(A,f);T.has(R)||T.set(R,[]),T.get(R).push(A)}let v=Array.from(T.entries());return v.sort(([A],[R])=>A<R?-1:A>R?1:0),{value:v.flatMap(([,A])=>Hl(A,t.args.pagination)),lastInsertId:s}}return{value:Hl(d,t.args.pagination),lastInsertId:s}}case"extendRecord":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),d=o===null?{}:xr(o);for(let[f,T]of Object.entries(t.args.values))T.type==="lastInsertId"?d[f]=s:d[f]=br(T.value,n,i);return{value:d,lastInsertId:s}}default:J(t,`Unexpected node type: ${t.type}`)}}#a(t,r,n){return An({query:t,queryable:r,execute:n,tracingHelper:this.#n,onQuery:this.#t})}};function Bl(e){return Array.isArray(e)?e.length===0:e==null}function Er(e){return Array.isArray(e)?e:[e]}function jl(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function xr(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function Wl(e,t){return Array.isArray(e)?e.map(r=>Wl(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function Ql(e,t){for(let{joinExpr:r,childRecords:n}of t)e[r.parentField]=Ed(n,e,r);return e}function Ed(e,t,r){if(Array.isArray(e)){let n=e.filter(i=>Gl(xr(i),t,r));return r.isRelationUnique?n.length>0?n[0]:null:n}else{if(e===null)return null;{let n=xr(e);return Gl(n,t,r)?n:null}}}function Gl(e,t,r){for(let[n,i]of r.on)if(t[n]!==e[i])return!1;return!0}function Hl(e,{cursor:t,skip:r,take:n}){let i=t!==null?e.findIndex(d=>cr(d,t)):0;if(i===-1)return[];let o=i+(r??0),s=n!==null?o+n:e.length;return e.slice(o,s)}function Jl(e,t){return JSON.stringify(t.map(r=>e[r]))}l();u();c();p();m();a();l();u();c();p();m();a();async function xd(){return globalThis.crypto??await Promise.resolve().then(()=>(Ke(),ri))}async function Kl(){return(await xd()).randomUUID()}l();u();c();p();m();a();var we=class extends Oe{name="TransactionManagerError";constructor(t,r){super("Transaction API error: "+t,"P2028",r)}},Pr=class extends we{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},Mn=class extends we{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction.`)}},Nn=class extends we{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back.`)}},Ln=class extends we{constructor(){super("Unable to start a transaction in the given time.")}},Un=class extends we{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction`,{operation:t,timeout:r,timeTaken:n})}},Dt=class extends we{constructor(t){super(`Internal Consistency Error: ${t}`)}},Fn=class extends we{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var Pd=100,Tr=Y("prisma:client:transactionManager"),Td=()=>({sql:"COMMIT",args:[],argTypes:[]}),vd=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),Ad=()=>({sql:'-- Implicit "COMMIT" query via underlying driver',args:[],argTypes:[]}),Cd=()=>({sql:'-- Implicit "ROLLBACK" query via underlying driver',args:[],argTypes:[]}),vr=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;#e;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n,onQuery:i}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n,this.#e=i}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#r(t))}async#r(t){let r=t!==void 0?this.validateOptions(t):this.transactionOptions,n={id:await Kl(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n),n.timer=this.startTransactionTimeout(n.id,r.maxWait);let i=await this.driverAdapter.startTransaction(r.isolationLevel);switch(n.status){case"waiting":return n.transaction=i,clearTimeout(n.timer),n.timer=void 0,n.status="running",n.timer=this.startTransactionTimeout(n.id,r.timeout),{id:n.id};case"timed_out":throw new Ln;case"running":case"committed":case"rolled_back":throw new Dt(`Transaction in invalid state ${n.status} although it just finished startup.`);default:J(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.getActiveTransaction(t,"commit");await this.closeTransaction(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.getActiveTransaction(t,"rollback");await this.closeTransaction(r,"rolled_back")})}getTransaction(t,r){let n=this.getActiveTransaction(t.id,r);if(!n.transaction)throw new Pr;return n.transaction}getActiveTransaction(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(Tr("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"waiting":case"running":throw new Dt("Active transaction found in closed transactions list.");case"committed":throw new Mn(r);case"rolled_back":throw new Nn(r);case"timed_out":throw new Un(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw Tr("Transaction not found.",t),new Pr}if(["committed","rolled_back","timed_out"].includes(n.status))throw new Dt("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.closeTransaction(t,"rolled_back")))}startTransactionTimeout(t,r){let n=Date.now();return setTimeout(async()=>{Tr("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let i=this.transactions.get(t);i&&["running","waiting"].includes(i.status)?await this.closeTransaction(i,"timed_out"):Tr("Transaction already committed or rolled back when timeout happened.",t)},r)}async closeTransaction(t,r){if(Tr("Closing transaction.",{transactionId:t.id,status:r}),t.status=r,t.transaction&&r==="committed")if(t.transaction.options.usePhantomQuery)await this.#t(Ad(),t.transaction,()=>t.transaction.commit());else{await t.transaction.commit();let n=Td();await this.#t(n,t.transaction,()=>t.transaction.executeRaw(n))}else if(t.transaction)if(t.transaction.options.usePhantomQuery)await this.#t(Cd(),t.transaction,()=>t.transaction.rollback());else{await t.transaction.rollback();let n=vd();await this.#t(n,t.transaction,()=>t.transaction.executeRaw(n))}clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>Pd&&this.closedTransactions.shift()}validateOptions(t){if(!t.timeout)throw new we("timeout is required");if(!t.maxWait)throw new we("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new Fn(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}#t(t,r,n){return An({query:t,queryable:r,execute:n,tracingHelper:this.tracingHelper,onQuery:this.#e})}};var $n="6.9.0";l();u();c();p();m();a();var oo,zl={async loadQueryCompiler(e){let{clientVersion:t,adapter:r,compilerWasm:n}=e;if(r===void 0)throw new U(`The \`adapter\` option for \`PrismaClient\` is required in this context (${Ze().prettyName})`,t);if(n===void 0)throw new U("WASM query compiler was unexpectedly `undefined`",t);return oo===void 0&&(oo=(async()=>{let i=await n.getRuntime(),o=await n.getQueryCompilerWasmModule();if(o==null)throw new U("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let s={"./query_compiler_bg.js":i},d=new WebAssembly.Instance(o,s),f=d.exports.__wbindgen_start;return i.__wbg_set_wasm(d.exports),f(),i.QueryCompiler})()),await oo}};var Yl="P2038",Vn=Y("prisma:client:clientEngine"),Xl=globalThis;Xl.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new pe(e,$n)}};var Ar=class{name="ClientEngine";queryCompiler;instantiateQueryCompilerPromise;QueryCompilerConstructor;queryCompilerLoader;adapterPromise;transactionManagerPromise;config;provider;datamodel;logEmitter;logQueries;logLevel;lastStartedQuery;tracingHelper;#e;constructor(t,r){if(!t.previewFeatures?.includes("driverAdapters"))throw new U("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,Yl);if(t.adapter)this.adapterPromise=t.adapter.connect(),this.provider=t.adapter.provider,Vn("Using driver adapter: %O",t.adapter);else throw new U("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,Yl);this.queryCompilerLoader=r??zl,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#e=n=>{this.logEmitter.emit("query",{...n,params:pr(n.params),target:"ClientEngine"})}),this.transactionManagerPromise=this.adapterPromise.then(n=>new vr({driverAdapter:n,transactionOptions:{...this.config.transactionOptions,isolationLevel:this.#i(this.config.transactionOptions.isolationLevel)},tracingHelper:this.tracingHelper,onQuery:this.#e})),this.instantiateQueryCompilerPromise=this.instantiateQueryCompiler()}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async instantiateQueryCompiler(){if(this.queryCompiler)return;this.QueryCompilerConstructor||(this.QueryCompilerConstructor=await this.queryCompilerLoader.loadQueryCompiler(this.config));let r=(await this.adapterPromise)?.getConnectionInfo?.()??{};try{this.#n(()=>{this.queryCompiler=new this.QueryCompilerConstructor({datamodel:this.datamodel,provider:this.provider,connectionInfo:r})})}catch(n){throw this.#r(n)}}#r(t){if(t instanceof pe)return t;try{let r=JSON.parse(t.message);return new U(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#t(t){if(t instanceof U)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new pe(Zl(this,t.message),this.config.clientVersion);if(t instanceof Oe)return new ee(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let r=JSON.parse(t);return new se(`${r.message}
${r.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#o(t){return t instanceof pe?t:typeof t.message=="string"&&typeof t.code=="string"?new ee(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):t}#n(t){let r=Xl.PRISMA_WASM_PANIC_REGISTRY.set_message,n;globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=i=>{n=i};try{return t()}finally{if(globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=r,n)throw new pe(Zl(this,n),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.tracingHelper.runInChildSpan("connect",()=>this.ensureStarted())}async stop(){await this.tracingHelper.runInChildSpan("disconnect",async()=>{await this.instantiateQueryCompilerPromise,await(await this.transactionManagerPromise)?.cancelAllTransactions(),await(await this.adapterPromise).dispose()})}async ensureStarted(){let t=await this.adapterPromise,r=await this.transactionManagerPromise;return await this.instantiateQueryCompilerPromise,[t,r]}version(){return"unknown"}async transaction(t,r,n){let i,o=await this.transactionManagerPromise;try{if(t==="start"){let s=n;i=await o.startTransaction({...s,isolationLevel:this.#i(s.isolationLevel)})}else if(t==="commit"){let s=n;await o.commitTransaction(s.id)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s.id)}else xe(t,"Invalid transaction action.")}catch(s){throw this.#t(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{traceparent:r,interactiveTransaction:n}){Vn("sending request");let i=JSON.stringify(t);this.lastStartedQuery=i;let[o,s]=await this.ensureStarted().catch(f=>{throw this.#t(f)}),d;try{d=this.#n(()=>this.queryCompiler.compile(i))}catch(f){throw this.#o(f)}try{let f=JSON.parse(d);Vn("query plan created",d);let T=n?s.getTransaction(n,"query"):o,v=n?{enabled:!1}:{enabled:!0,manager:s},A={},C=await kt.forSql({transactionManager:v,placeholderValues:A,onQuery:this.#e,tracingHelper:this.tracingHelper}).run(f,T);return Vn("query plan executed"),{data:{[t.action]:C}}}catch(f){throw this.#t(f)}}async requestBatch(t,{transaction:r,traceparent:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(At(t,r));this.lastStartedQuery=o;let[,s]=await this.ensureStarted().catch(f=>{throw this.#t(f)}),d;try{d=this.queryCompiler.compileBatch(o)}catch(f){throw this.#o(f)}try{let f;if(r?.kind==="itx")f=r.options;else{let C=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;f=await this.transaction("start",{},C)}let T={},v=kt.forSql({transactionManager:{enabled:!1},placeholderValues:T,onQuery:this.#e,tracingHelper:this.tracingHelper}),A=s.getTransaction(f,"batch query"),R=[];switch(d.type){case"multi":{R=await Promise.all(d.plans.map((C,D)=>v.run(C,A).then(I=>({data:{[t[D].action]:I}}),I=>I)));break}case"compacted":{if(!t.every(D=>D.action===i))throw new Error("All queries in a batch must have the same action");let C=await v.run(d.plan,A);R=this.#s(C,d,i);break}}return r?.kind!=="itx"&&await this.transaction("commit",{},f),R}catch(f){throw this.#t(f)}}metrics(t){throw new Error("Method not implemented.")}#s(t,r,n){let i=t.map(s=>r.keys.reduce((d,f)=>(d[f]=ze(s[f]),d),{})),o=new Set(r.nestedSelection);return r.arguments.map(s=>{let d=i.findIndex(f=>cr(f,s));if(d===-1)return r.expectNonEmpty?new ee("An operation failed because it depends on one or more records that were required but not found",{code:"P2025",clientVersion:this.config.clientVersion}):{data:{[n]:null}};{let f=Object.entries(t[d]).filter(([T])=>o.has(T));return{data:{[n]:Object.fromEntries(f)}}}})}#i(t){switch(t){case void 0:return;case"ReadUncommitted":return"READ UNCOMMITTED";case"ReadCommitted":return"READ COMMITTED";case"RepeatableRead":return"REPEATABLE READ";case"Serializable":return"SERIALIZABLE";case"Snapshot":return"SNAPSHOT";default:throw new ee(`Inconsistent column data: Conversion failed: Invalid isolation level \`${t}\``,{code:"P2023",clientVersion:this.config.clientVersion,meta:{providedIsolationLevel:t}})}}};function Zl(e,t){return Sa({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:e.lastStartedQuery})}l();u();c();p();m();a();l();u();c();p();m();a();function _t({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,d=t[o]?.url;if(o===void 0?i=void 0:d?i=d:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw Ze().id==="workerd"?new U(`error: Environment variable not found: ${s.fromEnvVar}.

In Cloudflare module Workers, environment variables are available only in the Worker's \`env\` parameter of \`fetch\`.
To solve this, provide the connection string directly: https://pris.ly/d/cloudflare-datasource-url`,n):new U(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new U("error: Missing URL environment variable, value, or override.",n);return i}l();u();c();p();m();a();l();u();c();p();m();a();var qn=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var me=class extends qn{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};l();u();c();p();m();a();l();u();c();p();m();a();function N(e,t){return{...e,isRetryable:t}}var Mt=class extends me{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",N(t,!0))}};O(Mt,"ForcedRetryError");l();u();c();p();m();a();var it=class extends me{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,N(r,!1))}};O(it,"InvalidDatasourceError");l();u();c();p();m();a();var ot=class extends me{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,N(r,!1))}};O(ot,"NotImplementedYetError");l();u();c();p();m();a();l();u();c();p();m();a();var B=class extends me{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var st=class extends B{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",N(t,!0))}};O(st,"SchemaMissingError");l();u();c();p();m();a();l();u();c();p();m();a();var so="This request could not be understood by the server",Cr=class extends B{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||so,N(t,!1)),n&&(this.code=n)}};O(Cr,"BadRequestError");l();u();c();p();m();a();var Rr=class extends B{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",N(t,!0)),this.logs=r}};O(Rr,"HealthcheckTimeoutError");l();u();c();p();m();a();var Sr=class extends B{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,N(t,!0)),this.logs=n}};O(Sr,"EngineStartupError");l();u();c();p();m();a();var Ir=class extends B{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",N(t,!1))}};O(Ir,"EngineVersionNotSupportedError");l();u();c();p();m();a();var ao="Request timed out",Or=class extends B{name="GatewayTimeoutError";code="P5009";constructor(t,r=ao){super(r,N(t,!1))}};O(Or,"GatewayTimeoutError");l();u();c();p();m();a();var Sd="Interactive transaction error",kr=class extends B{name="InteractiveTransactionError";code="P5015";constructor(t,r=Sd){super(r,N(t,!1))}};O(kr,"InteractiveTransactionError");l();u();c();p();m();a();var Id="Request parameters are invalid",Dr=class extends B{name="InvalidRequestError";code="P5011";constructor(t,r=Id){super(r,N(t,!1))}};O(Dr,"InvalidRequestError");l();u();c();p();m();a();var lo="Requested resource does not exist",_r=class extends B{name="NotFoundError";code="P5003";constructor(t,r=lo){super(r,N(t,!1))}};O(_r,"NotFoundError");l();u();c();p();m();a();var uo="Unknown server error",Nt=class extends B{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||uo,N(t,!0)),this.logs=n}};O(Nt,"ServerError");l();u();c();p();m();a();var co="Unauthorized, check your connection string",Mr=class extends B{name="UnauthorizedError";code="P5007";constructor(t,r=co){super(r,N(t,!1))}};O(Mr,"UnauthorizedError");l();u();c();p();m();a();var po="Usage exceeded, retry again later",Nr=class extends B{name="UsageExceededError";code="P5008";constructor(t,r=po){super(r,N(t,!0))}};O(Nr,"UsageExceededError");async function Od(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function Lr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await Od(e);if(n.type==="QueryEngineError")throw new ee(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new Nt(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new st(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Ir(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Sr(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new U(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new Rr(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new kr(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Dr(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Mr(r,Lt(co,n));if(e.status===404)return new _r(r,Lt(lo,n));if(e.status===429)throw new Nr(r,Lt(po,n));if(e.status===504)throw new Or(r,Lt(ao,n));if(e.status>=500)throw new Nt(r,Lt(uo,n));if(e.status>=400)throw new Cr(r,Lt(so,n))}function Lt(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}l();u();c();p();m();a();function eu(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}l();u();c();p();m();a();var Le="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function tu(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,d,f,T,v;for(let A=0;A<o;A=A+3)v=t[A]<<16|t[A+1]<<8|t[A+2],s=(v&16515072)>>18,d=(v&258048)>>12,f=(v&4032)>>6,T=v&63,r+=Le[s]+Le[d]+Le[f]+Le[T];return i==1?(v=t[o],s=(v&252)>>2,d=(v&3)<<4,r+=Le[s]+Le[d]+"=="):i==2&&(v=t[o]<<8|t[o+1],s=(v&64512)>>10,d=(v&1008)>>4,f=(v&15)<<2,r+=Le[s]+Le[d]+Le[f]+"="),r}l();u();c();p();m();a();function ru(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new U("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}l();u();c();p();m();a();function kd(e){return e[0]*1e3+e[1]/1e6}function mo(e){return new Date(kd(e))}l();u();c();p();m();a();var nu={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.9.0-10.81e4af48011447c3cc503a190e86995b66d2a28e","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};l();u();c();p();m();a();l();u();c();p();m();a();var Ur=class extends me{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,N(r,!0))}};O(Ur,"RequestError");async function at(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let d=s.message??"Unknown error";throw new Ur(d,{clientVersion:n,cause:s})}}var _d=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,iu=Y("prisma:client:dataproxyEngine");async function Md(e,t){let r=nu["@prisma/engines-version"],n=t.clientVersion??"unknown";if(g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&_d.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[d,f,T]=s.split("."),v=Nd(`<=${d}.${f}.${T}`),A=await at(v,{clientVersion:n});if(!A.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${A.status} ${A.statusText}, response body: ${await A.text()||"<empty body>"}`);let R=await A.text();iu("length of body fetched from unpkg.com",R.length);let C;try{C=JSON.parse(R)}catch(D){throw console.error("JSON.parse error: body fetched from unpkg.com: ",R),D}return C.version}throw new ot("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function ou(e,t){let r=await Md(e,t);return iu("version",r),r}function Nd(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var su=3,Fr=Y("prisma:client:dataproxyEngine"),fo=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,interactiveTransaction:r}={}){let n={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-transaction-id"]=r.id);let i=this.buildCaptureSettings();return i.length>0&&(n["X-capture-telemetry"]=i.join(", ")),n}buildCaptureSettings(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}},$r=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){ru(t),this.config=t,this.env={...t.env,...typeof g<"u"?g.env:{}},this.inlineSchema=tu(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.headerBuilder=new fo({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.protocol=ni(r)?"http":"https",this.remoteClientVersion=await ou(this.host,this.config),Fr("host",this.host),Fr("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":Fr(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:mo(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:mo(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await at(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||Fr("schema response status",r.status);let n=await Lr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=At(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(f=>(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f?this.convertProtocolErrorsToClientError(f.errors):f))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let d=await at(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,interactiveTransaction:i}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);d.ok||Fr("graphql response status",d.status),await this.handleError(await Lr(d,this.clientVersion));let f=await d.json();if(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f)throw this.convertProtocolErrorsToClientError(f.errors);return"batchResult"in f?f.batchResult:f}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),d=await this.url("transaction/start");o(d);let f=await at(d,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await Lr(f,this.clientVersion));let T=await f.json(),{extensions:v}=T;v&&this.propagateResponseExtensions(v);let A=T.id,R=T["data-proxy"].endpoint;return{id:A,payload:{endpoint:R}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let d=await at(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await Lr(d,this.clientVersion));let f=await d.json(),{extensions:T}=f;T&&this.propagateResponseExtensions(T);return}}})}getURLAndAPIKey(){let t={clientVersion:this.clientVersion},r=Object.keys(this.inlineDatasources)[0],n=_t({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),i;try{i=new URL(n)}catch{throw new it(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==Zr)throw new it(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let d=s.get("api_key");if(d===null||d.length<1)throw new it(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);return{apiKey:d,url:i}}metrics(){throw new ot("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof me)||!i.isRetryable)throw i;if(r>=su)throw i instanceof Mt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${su} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await eu(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof st)throw await this.uploadSchema(),new Mt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?Pn(t[0],this.config.clientVersion,this.config.activeProvider):new se(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};function au({copyEngine:e=!0},t){let r;try{r=_t({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...g.env},clientVersion:t.clientVersion})}catch{}let n=!!(r?.startsWith("prisma://")||Xr(r));e&&n&&tn("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)");let i=mt(t.generator),o=n||!e,s=!!t.adapter,d=i==="library",f=i==="binary",T=i==="client";if(o&&s||s&&!1){let v;throw e?r?.startsWith("prisma://")?v=["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:v=["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:v=["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."],new ie(v.join(`
`),{clientVersion:t.clientVersion})}if(o)return new $r(t);if(T)return new Ar(t);{let v=[`PrismaClient failed to initialize because it wasn't configured to run in this environment (${Ze().prettyName}).`,"In order to run Prisma Client in an edge runtime, you will need to configure one of the following options:","- Enable Driver Adapters: https://pris.ly/d/driver-adapters","- Enable Accelerate: https://pris.ly/d/accelerate"];throw new ie(v.join(`
`),{clientVersion:t.clientVersion})}return"wasm-compiler-edge"}l();u();c();p();m();a();function Bn({generator:e}){return e?.previewFeatures??[]}l();u();c();p();m();a();var lu=e=>({command:e});l();u();c();p();m();a();l();u();c();p();m();a();var uu=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);l();u();c();p();m();a();a();function Ut(e){try{return cu(e,"fast")}catch{return cu(e,"slow")}}function cu(e,t){return JSON.stringify(e.map(r=>mu(r,t)))}function mu(e,t){if(Array.isArray(e))return e.map(r=>mu(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(gt(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(ne.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(y.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(Ld(e))return{prisma__type:"bytes",prisma__value:y.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:y.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?du(e):e}function Ld(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function du(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(pu);let t={};for(let r of Object.keys(e))t[r]=pu(e[r]);return t}function pu(e){return typeof e=="bigint"?e.toString():du(e)}var Ud=/^(\s*alter\s)/i,fu=Y("prisma:client");function go(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&Ud.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var yo=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(wn(r))n=r.sql,i={values:Ut(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:Ut(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:Ut(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:Ut(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=uu(r),i={values:Ut(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?fu(`prisma.${e}(${n}, ${i.values})`):fu(`prisma.${e}(${n})`),{query:n,parameters:i}},gu={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new ye(t,r)}},yu={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};l();u();c();p();m();a();function ho(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=hu(r(s)):hu(r(s))}catch(d){return Promise.reject(d)}};return{get spec(){return n},then(s,d){return o().then(s,d)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let d=o(s);return d.requestTransaction?d.requestTransaction(s):d},[Symbol.toStringTag]:"PrismaPromise"}}}function hu(e){return typeof e.then=="function"?e:Promise.resolve(e)}l();u();c();p();m();a();var Fd=ti.split(".")[0],$d={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},wo=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${Fd}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??$d}};function wu(){return new wo}l();u();c();p();m();a();function bu(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}l();u();c();p();m();a();function Eu(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}l();u();c();p();m();a();var jn=class{_middlewares=[];use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};l();u();c();p();m();a();var Pu=ke(oi());l();u();c();p();m();a();function Qn(e){return typeof e.batchRequestIdx=="number"}l();u();c();p();m();a();function xu(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(bo(e.query.arguments)),t.push(bo(e.query.selection)),t.join("")}function bo(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${bo(n)})`:r}).join(" ")})`}l();u();c();p();m();a();var Vd={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Eo(e){return Vd[e]}l();u();c();p();m();a();var Gn=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,g.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};l();u();c();p();m();a();a();function lt(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new ne(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>lt("bigint",r));case"bytes-array":return t.map(r=>lt("bytes",r));case"decimal-array":return t.map(r=>lt("decimal",r));case"datetime-array":return t.map(r=>lt("datetime",r));case"date-array":return t.map(r=>lt("date",r));case"time-array":return t.map(r=>lt("time",r));default:return t}}function xo(e){let t=[],r=qd(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=lt(e.types[s],i[s]);t.push(o)}return t}function qd(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var Bd=Y("prisma:client:request_handler"),Hn=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new Gn({batchLoader:ha(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],d=n.map(A=>A.protocolQuery),f=this.client._tracingHelper.getTraceParent(s),T=n.some(A=>Eo(A.protocolQuery.action));return(await this.client._engine.requestBatch(d,{traceparent:f,transaction:jd(o),containsWrite:T,customDataProxyFetch:i})).map((A,R)=>{if(A instanceof Error)return A;try{return this.mapQueryEngineResult(n[R],A)}catch(C){return C}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Tu(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Eo(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:xu(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:d}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:d,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return g.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:d}){if(Bd(t),Qd(t,i))throw t;if(t instanceof ee&&Gd(t)){let T=vu(t.meta);fn({args:o,errors:[T],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:d})}let f=t.message;if(n&&(f=on({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:f})),f=this.sanitizeMessage(f),t.code){let T=s?{modelName:s,...t.meta}:t.meta;throw new ee(f,{code:t.code,clientVersion:this.client._clientVersion,meta:T,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new pe(f,this.client._clientVersion);if(t instanceof se)throw new se(f,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof U)throw new U(f,this.client._clientVersion);if(t instanceof pe)throw new pe(f,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,Pu.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(T=>T!=="select"&&T!=="include"),d=Pi(o,s),f=i==="queryRaw"?xo(d):ze(d);return n?n(f):f}get[Symbol.toStringTag](){return"RequestHandler"}};function jd(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Tu(e)};xe(e,"Unknown transaction kind")}}function Tu(e){return{id:e.id,payload:e.payload}}function Qd(e,t){return Qn(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function Gd(e){return e.code==="P2009"||e.code==="P2012"}function vu(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(vu)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}l();u();c();p();m();a();var Au=$n;l();u();c();p();m();a();var Ou=ke(ui());l();u();c();p();m();a();var F=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};O(F,"PrismaClientConstructorValidationError");var Cu=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],Ru=["pretty","colorless","minimal"],Su=["info","query","warn","error"],Hd={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new F(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=Ft(r,t)||` Available datasources: ${t.join(", ")}`;throw new F(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new F(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new F(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new F(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&mt(t.generator)==="client")throw new F('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new F('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!Bn(t).includes("driverAdapters"))throw new F('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(mt(t.generator)==="binary")throw new F('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new F(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new F(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!Ru.includes(e)){let t=Ft(e,Ru);throw new F(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new F(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!Su.includes(r)){let n=Ft(r,Su);throw new F(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Ft(i,o);throw new F(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new F(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new F(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new F(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new F('"omit" option is expected to be an object.');if(e===null)throw new F('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=Wd(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,d]of Object.entries(i)){let f=o.fields.find(T=>T.name===s);if(!f){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(f.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof d!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new F(Kd(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new F(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=Ft(r,t);throw new F(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function ku(e,t){for(let[r,n]of Object.entries(e)){if(!Cu.includes(r)){let i=Ft(r,Cu);throw new F(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}Hd[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new F('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function Ft(e,t){if(t.length===0||typeof e!="string")return"";let r=Jd(e,t);return r?` Did you mean "${r}"?`:""}function Jd(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,Ou.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function Wd(e,t){return Iu(t.models,e)??Iu(t.types,e)}function Iu(e,t){let r=Object.keys(e).find(n=>Fe(n)===t);if(r)return e[r]}function Kd(e,t){let r=Pt(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=dn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}l();u();c();p();m();a();function Du(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,d=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},f=T=>{o||(o=!0,r(T))};for(let T=0;T<e.length;T++)e[T].then(v=>{n[T]=v,d()},v=>{if(!Qn(v)){f(v);return}v.batchRequestIdx===T?f(v):(i||(i=v),d())})})}var Qe=Y("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var zd={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},Yd=Symbol.for("prisma.client.transaction.id"),Zd={id:0,nextId(){return++this.id}};function Xd(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_middlewares=new jn;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=ho();constructor(n){e=n?.__internal?.configOverride?.(e)??e,Pa(e),n&&ku(n,e);let i=new bn().on("error",()=>{});this._extensions=Tt.empty(),this._previewFeatures=Bn(e),this._clientVersion=e.clientVersion??Au,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=wu();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&Kr.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&Kr.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let f=e.activeProvider==="postgresql"?"postgres":e.activeProvider;if(s.provider!==f)throw new U(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${f}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new U("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let d=e.injectableEdgeEnv?.();try{let f=n??{},T=f.__internal??{},v=T.debug===!0;v&&Y.enable("prisma:client");let A=Kr.resolve(e.dirname,e.relativePath);Zo.existsSync(A)||(A=e.dirname),Qe("dirname",e.dirname),Qe("relativePath",e.relativePath),Qe("cwd",A);let R=T.engine||{};if(f.errorFormat?this._errorFormat=f.errorFormat:g.env.NODE_ENV==="production"?this._errorFormat="minimal":g.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:A,dirname:e.dirname,enableDebugLogs:v,allowTriggerPanic:R.allowTriggerPanic,prismaPath:R.binaryPath??void 0,engineEndpoint:R.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:f.log&&Eu(f.log),logQueries:f.log&&!!(typeof f.log=="string"?f.log==="query":f.log.find(C=>typeof C=="string"?C==="query":C.level==="query")),env:d?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:Ta(f,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:f.transactionOptions?.maxWait??2e3,timeout:f.transactionOptions?.timeout??5e3,isolationLevel:f.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:_t,getBatchRequestPayload:At,prismaGraphQLToJSError:Pn,PrismaClientUnknownRequestError:se,PrismaClientInitializationError:U,PrismaClientKnownRequestError:ee,debug:Y("prisma:client:accelerateEngine"),engineVersion:Mu.version,clientVersion:e.clientVersion}},Qe("clientVersion",e.clientVersion),this._engine=au(e,this._engineConfig),this._requestHandler=new Hn(this,i),f.log)for(let C of f.log){let D=typeof C=="string"?C:C.emit==="stdout"?C.level:null;D&&this.$on(D,I=>{Ht.log(`${Ht.tags[D]??""}`,I.message||I.query)})}}catch(f){throw f.clientVersion=this._clientVersion,f}return this._appliedParent=lr(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{zo()}}$executeRawInternal(n,i,o,s){let d=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:yo({clientMethod:i,activeProvider:d}),callsite:Ve(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,d]=_u(n,i);return go(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,d)}throw new ie("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(go(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new ie(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:lu,callsite:Ve(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let d=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:yo({clientMethod:i,activeProvider:d}),callsite:Ve(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",..._u(n,i));throw new ie("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new ie("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=Zd.nextId(),s=bu(n.length),d=n.map((f,T)=>{if(f?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let v=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,A={kind:"batch",id:o,index:T,isolationLevel:v,lock:s};return f.requestTransaction?.(A)??f});return Du(d)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},d=await this._engine.transaction("start",o,s),f;try{let T={kind:"itx",...d};f=await n(this._createItxClient(T)),await this._engine.transaction("commit",o,d)}catch(T){throw await this._engine.transaction("rollback",o,d).catch(()=>{}),T}return f}_createItxClient(n){return Pe(lr(Pe(aa(this),[ae("_appliedParent",()=>this._appliedParent._createItxClient(n)),ae("_createPrismaPromise",()=>ho(n)),ae(Yd,()=>n.id)])),[vt(ma)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??zd,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},d=-1,f=async T=>{let v=this._middlewares.get(++d);if(v)return this._tracingHelper.runInChildSpan(s.middleware,M=>v(T,be=>(M?.end(),f(be))));let{runInTransaction:A,args:R,...C}=T,D={...n,...C};R&&(D.args=i.middlewareArgsToRequestArgs(R)),n.transaction!==void 0&&A===!1&&delete D.transaction;let I=await ya(this,D);return D.model?pa({result:I,modelName:D.model,args:D.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):I};return this._tracingHelper.runInChildSpan(s.operation,()=>f(o))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:d,model:f,argsMapper:T,transaction:v,unpacker:A,otelParentCtx:R,customDataProxyFetch:C}){try{n=T?T(n):n;let D={name:"serialize"},I=this._tracingHelper.runInChildSpan(D,()=>hi({modelName:f,runtimeDataModel:this._runtimeDataModel,action:d,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return Y.enabled("prisma:client")&&(Qe("Prisma Client call:"),Qe(`prisma.${i}(${Ys(n)})`),Qe("Generated request:"),Qe(JSON.stringify(I,null,2)+`
`)),v?.kind==="batch"&&await v.lock,this._requestHandler.request({protocolQuery:I,modelName:f,action:d,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:v,unpacker:A,otelParentCtx:R,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:C})}catch(D){throw D.clientVersion=this._clientVersion,D}}$metrics=new or(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=la}return t}function _u(e,t){return ef(e)?[new ye(e,t),gu]:[e,yu]}function ef(e){return Array.isArray(e)&&Array.isArray(e.raw)}l();u();c();p();m();a();var tf=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function rf(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!tf.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}l();u();c();p();m();a();a();var export_warnEnvConflicts=void 0;export{nn as DMMF,Y as Debug,ne as Decimal,No as Extensions,or as MetricsClient,U as PrismaClientInitializationError,ee as PrismaClientKnownRequestError,pe as PrismaClientRustPanicError,se as PrismaClientUnknownRequestError,ie as PrismaClientValidationError,Uo as Public,ye as Sql,gp as createParam,Ap as defineDmmfProperty,ze as deserializeJsonResponse,xo as deserializeRawResult,Mc as dmmfToRuntimeDataModel,Op as empty,Xd as getPrismaClient,Ze as getRuntime,Ip as join,rf as makeStrictEnum,Rp as makeTypedQueryFactory,mi as objectEnumValues,Js as raw,hi as serializeJsonQuery,gi as skip,Ws as sqltag,export_warnEnvConflicts as warnEnvConflicts,tn as warnOnce};
//# sourceMappingURL=wasm-compiler-edge.mjs.map
