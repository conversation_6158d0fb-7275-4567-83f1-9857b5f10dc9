# 📝 Form Validation Debug Guide (React Hook Form + Zod)

## 🔧 Tools
- React Hook Form Devtools
- Console logging errors
- Zod safeParse debugging

## 🛠 Zod Debugging
```typescript
const result = schema.safeParse(data);
if (!result.success) {
  console.log('🛑 Zod Errors:', result.error.format());
}
```

## 🎯 Hook Form Debugging
```tsx
const { register, handleSubmit, formState: { errors } } = useForm();
console.log('🎯 Form Errors:', errors);
```

## 🐛 Common Issues
- Zod schema mismatch
- Field name mismatch
- Default values not set
- Custom resolver logic error
