/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [["path", { d: "M9 18v-6H5l7-7 7 7h-4v6H9z", key: "1x06kx" }]];
const ArrowBigUp = createLucideIcon("arrow-big-up", __iconNode);

export { __iconNode, ArrowBigUp as default };
//# sourceMappingURL=arrow-big-up.js.map
