{"name": "pirates", "description": "Properly hijack require, i.e., properly define require hooks and customizations", "main": "lib/index.js", "types": "index.d.ts", "scripts": {"test": "ava"}, "files": ["lib", "index.d.ts"], "repository": {"type": "git", "url": "https://github.com/danez/pirates.git"}, "engines": {"node": ">= 6"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ariporad.com"}, "devDependencies": {"ava": "1.4.1", "decache": "4.6.2"}, "license": "MIT", "bugs": {"url": "https://github.com/danez/pirates/issues"}, "homepage": "https://github.com/danez/pirates#readme", "ava": {"files": ["test/*.js"], "sources": ["lib/**/*.js"]}, "version": "4.0.7"}