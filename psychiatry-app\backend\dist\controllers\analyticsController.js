"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const zod_1 = require("zod");
const analyticsServiceSimple_1 = require("@/services/analyticsServiceSimple");
const dateRangeSchema = zod_1.z.object({
    from: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid from date'),
    to: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid to date'),
});
class AnalyticsController {
    static async getDashboardAnalytics(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const result = await analyticsServiceSimple_1.AnalyticsServiceSimple.getDashboardAnalytics(req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getPatientAnalytics(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const from = req.query.from || '2024-01-01';
            const to = req.query.to || '2025-12-31';
            const result = await analyticsServiceSimple_1.AnalyticsServiceSimple.getPatientAnalytics({ from, to }, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getAppointmentAnalytics(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const from = req.query.from || '2024-01-01';
            const to = req.query.to || '2025-12-31';
            const result = await analyticsServiceSimple_1.AnalyticsServiceSimple.getAppointmentAnalytics({ from, to }, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getLabResultAnalytics(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const from = req.query.from || '2024-01-01';
            const to = req.query.to || '2025-12-31';
            const result = await analyticsServiceSimple_1.AnalyticsServiceSimple.getLabResultAnalytics({ from, to }, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
    static async getSystemAnalytics(req, res, next) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                });
            }
            const from = req.query.from || '2024-01-01';
            const to = req.query.to || '2025-12-31';
            const result = await analyticsServiceSimple_1.AnalyticsServiceSimple.getSystemAnalytics({ from, to }, req.user.id, req.user.role);
            res.status(200).json(result);
        }
        catch (error) {
            next(error);
        }
    }
}
exports.AnalyticsController = AnalyticsController;
//# sourceMappingURL=analyticsController.js.map