"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LabResultService = void 0;
const client_1 = require("@prisma/client");
const errors_1 = require("@/utils/errors");
const prisma = new client_1.PrismaClient();
class LabResultService {
    static async createLabResult(data, createdBy, auditData) {
        const patient = await prisma.patient.findFirst({
            where: {
                id: data.patientId,
                isDeleted: false,
            },
        });
        if (!patient) {
            throw new errors_1.NotFoundError('Patient not found');
        }
        if (!data.testType || !data.testDate || !data.orderedBy || !data.results) {
            throw new errors_1.ValidationError('Test type, test date, ordered by, and results are required');
        }
        const testDate = new Date(data.testDate);
        if (isNaN(testDate.getTime()) || testDate > new Date()) {
            throw new errors_1.ValidationError('Invalid test date');
        }
        const labResult = await prisma.labResult.create({
            data: {
                patientId: data.patientId,
                testName: data.testType,
                testCategory: 'chemistry',
                testType: 'routine',
                value: JSON.stringify(data.results),
                referenceRange: data.normalRanges ? JSON.stringify(data.normalRanges) : 'Normal',
                status: data.status || 'normal',
                flagged: data.flags ? Object.keys(data.flags).length > 0 : false,
                testDate: testDate,
                orderedBy: data.orderedBy.trim(),
                notes: data.notes?.trim() || null,
                createdBy,
            },
            include: {
                patient: {
                    select: {
                        id: true,
                        patientId: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        await this.createAuditLog({
            userId: createdBy,
            action: 'CREATE',
            entityType: 'LAB_RESULT',
            entityId: labResult.id,
            patientId: data.patientId,
            labResultId: labResult.id,
            newValues: {
                testType: labResult.testType,
                testDate: labResult.testDate,
                orderedBy: labResult.orderedBy,
                status: labResult.status,
            },
            ipAddress: auditData?.ipAddress,
            userAgent: auditData?.userAgent,
        });
        return {
            success: true,
            data: { labResult },
            message: 'Lab result created successfully',
        };
    }
    static async getLabResults(query, userId, userRole) {
        const page = parseInt(query.page || '1', 10);
        const limit = Math.min(parseInt(query.limit || '10', 10), 100);
        const skip = (page - 1) * limit;
        const where = {
            isDeleted: false,
        };
        if (userRole !== 'ADMIN') {
            where.patient = {
                createdBy: userId,
            };
        }
        if (query.patientId) {
            where.patientId = query.patientId;
        }
        if (query.testType) {
            where.testType = query.testType;
        }
        if (query.status) {
            where.status = query.status;
        }
        if (query.dateFrom || query.dateTo) {
            where.testDate = {};
            if (query.dateFrom) {
                where.testDate.gte = new Date(query.dateFrom);
            }
            if (query.dateTo) {
                where.testDate.lte = new Date(query.dateTo);
            }
        }
        const orderBy = {};
        if (query.sortBy) {
            const direction = query.sortOrder === 'desc' ? 'desc' : 'asc';
            orderBy[query.sortBy] = direction;
        }
        else {
            orderBy.testDate = 'desc';
        }
        const [labResults, total] = await Promise.all([
            prisma.labResult.findMany({
                where,
                skip,
                take: limit,
                orderBy,
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    creator: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            username: true,
                        },
                    },
                },
            }),
            prisma.labResult.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: labResults,
            pagination: {
                page,
                limit,
                total,
                pages: totalPages,
            },
        };
    }
    static async getLabResultById(id, userId, userRole) {
        const labResult = await prisma.labResult.findFirst({
            where: {
                id,
                isDeleted: false,
                ...(userRole !== 'ADMIN' && {
                    patient: {
                        createdBy: userId,
                    },
                }),
            },
            include: {
                patient: {
                    select: {
                        id: true,
                        patientId: true,
                        firstName: true,
                        lastName: true,
                        dateOfBirth: true,
                        gender: true,
                    },
                },
                creator: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        username: true,
                    },
                },
            },
        });
        if (!labResult) {
            throw new errors_1.NotFoundError('Lab result not found');
        }
        await this.createAuditLog({
            userId,
            action: 'VIEW',
            entityType: 'LAB_RESULT',
            entityId: labResult.id,
            patientId: labResult.patientId,
            labResultId: labResult.id,
        });
        return {
            success: true,
            data: { labResult },
        };
    }
    static async getPatientLabResults(patientId, userId, userRole, query = {}) {
        const patient = await prisma.patient.findFirst({
            where: {
                id: patientId,
                isDeleted: false,
                ...(userRole !== 'ADMIN' && { createdBy: userId }),
            },
        });
        if (!patient) {
            throw new errors_1.NotFoundError('Patient not found');
        }
        const where = {
            patientId,
            isDeleted: false,
        };
        if (query.testType) {
            where.testType = query.testType;
        }
        if (query.dateFrom || query.dateTo) {
            where.testDate = {};
            if (query.dateFrom) {
                where.testDate.gte = new Date(query.dateFrom);
            }
            if (query.dateTo) {
                where.testDate.lte = new Date(query.dateTo);
            }
        }
        const labResults = await prisma.labResult.findMany({
            where,
            take: query.limit || 50,
            orderBy: { testDate: 'desc' },
            include: {
                creator: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        username: true,
                    },
                },
            },
        });
        return {
            success: true,
            data: { labResults },
        };
    }
    static async getLabResultTrends(patientId, testType, userId, userRole, dateFrom, dateTo) {
        const patient = await prisma.patient.findFirst({
            where: {
                id: patientId,
                isDeleted: false,
                ...(userRole !== 'ADMIN' && { createdBy: userId }),
            },
        });
        if (!patient) {
            throw new errors_1.NotFoundError('Patient not found');
        }
        const where = {
            patientId,
            testType,
            isDeleted: false,
            status: 'COMPLETED',
        };
        if (dateFrom || dateTo) {
            where.testDate = {};
            if (dateFrom) {
                where.testDate.gte = new Date(dateFrom);
            }
            if (dateTo) {
                where.testDate.lte = new Date(dateTo);
            }
        }
        const labResults = await prisma.labResult.findMany({
            where,
            orderBy: { testDate: 'asc' },
            select: {
                id: true,
                testDate: true,
                value: true,
                referenceRange: true,
                flagged: true,
                testName: true,
                numericValue: true,
            },
        });
        return {
            success: true,
            data: { trends: labResults },
        };
    }
    static async updateLabResult(id, data, updatedBy, auditData) {
        const existingLabResult = await prisma.labResult.findFirst({
            where: {
                id,
                isDeleted: false,
            },
            include: {
                patient: {
                    select: {
                        patientId: true,
                        firstName: true,
                        lastName: true,
                        createdBy: true,
                    },
                },
            },
        });
        if (!existingLabResult) {
            throw new errors_1.NotFoundError('Lab result not found');
        }
        const user = await prisma.user.findUnique({
            where: { id: updatedBy },
            select: { role: true },
        });
        if (!user) {
            throw new errors_1.AuthorizationError('User not found');
        }
        if (user.role !== 'ADMIN' && existingLabResult.patient.createdBy !== updatedBy) {
            throw new errors_1.AuthorizationError('Not authorized to update this lab result');
        }
        const updatedLabResult = await prisma.labResult.update({
            where: { id },
            data: {
                ...(data.testType && { testType: data.testType }),
                ...(data.testDate && { testDate: new Date(data.testDate) }),
                ...(data.orderedBy && { orderedBy: data.orderedBy }),
                ...(data.results && { results: data.results }),
                ...(data.normalRanges !== undefined && { normalRanges: data.normalRanges }),
                ...(data.flags !== undefined && { flags: data.flags }),
                ...(data.notes !== undefined && { notes: data.notes }),
                ...(data.status && { status: data.status }),
            },
            include: {
                patient: {
                    select: {
                        patientId: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                creator: {
                    select: {
                        firstName: true,
                        lastName: true,
                        username: true,
                    },
                },
            },
        });
        await prisma.auditLog.create({
            data: {
                userId: updatedBy,
                action: 'UPDATE',
                entityType: 'LAB_RESULT',
                entityId: id,
                oldValues: JSON.stringify({
                    testName: existingLabResult.testName,
                    testDate: existingLabResult.testDate,
                    orderedBy: existingLabResult.orderedBy,
                    value: existingLabResult.value,
                    referenceRange: existingLabResult.referenceRange,
                    flagged: existingLabResult.flagged,
                    notes: existingLabResult.notes,
                    status: existingLabResult.status,
                }),
                newValues: JSON.stringify({
                    testName: updatedLabResult.testName,
                    testDate: updatedLabResult.testDate,
                    orderedBy: updatedLabResult.orderedBy,
                    value: updatedLabResult.value,
                    referenceRange: updatedLabResult.referenceRange,
                    flagged: updatedLabResult.flagged,
                    notes: updatedLabResult.notes,
                    status: updatedLabResult.status,
                }),
                patientId: existingLabResult.patientId,
                labResultId: id,
                ipAddress: auditData?.ipAddress,
                userAgent: auditData?.userAgent,
            },
        });
        return {
            success: true,
            data: { labResult: updatedLabResult },
        };
    }
    static async deleteLabResult(id, userId, userRole, auditData) {
        const labResult = await prisma.labResult.findFirst({
            where: {
                id,
                isDeleted: false,
                ...(userRole !== 'ADMIN' && {
                    patient: {
                        createdBy: userId,
                    },
                }),
            },
            include: {
                patient: {
                    select: {
                        patientId: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        if (!labResult) {
            throw new errors_1.NotFoundError('Lab result not found');
        }
        await prisma.labResult.update({
            where: { id },
            data: {
                isDeleted: true,
                deletedAt: new Date(),
            },
        });
        await this.createAuditLog({
            userId,
            action: 'DELETE',
            entityType: 'LAB_RESULT',
            entityId: id,
            patientId: labResult.patientId,
            labResultId: id,
            oldValues: {
                testType: labResult.testType,
                testDate: labResult.testDate,
                patient: `${labResult.patient.firstName} ${labResult.patient.lastName}`,
            },
            ipAddress: auditData?.ipAddress,
            userAgent: auditData?.userAgent,
        });
        return {
            success: true,
            data: null,
            message: 'Lab result deleted successfully',
        };
    }
    static async createAuditLog(data) {
        try {
            await prisma.auditLog.create({
                data: {
                    userId: data.userId,
                    action: data.action,
                    entityType: data.entityType,
                    entityId: data.entityId,
                    oldValues: data.oldValues ? JSON.stringify(data.oldValues) : null,
                    newValues: data.newValues ? JSON.stringify(data.newValues) : null,
                    ipAddress: data.ipAddress,
                    userAgent: data.userAgent,
                    patientId: data.patientId,
                    labResultId: data.labResultId,
                    appointmentId: data.appointmentId,
                },
            });
        }
        catch (error) {
            console.error('Failed to create audit log:', error);
        }
    }
}
exports.LabResultService = LabResultService;
//# sourceMappingURL=labResultService.js.map