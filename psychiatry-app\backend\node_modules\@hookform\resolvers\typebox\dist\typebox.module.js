import{validateFieldsNatively as r,toNestErrors as e}from"@hookform/resolvers";import{TypeCheck as o}from"@sinclair/typebox/compiler";import{Value as t}from"@sinclair/typebox/value";import{appendErrors as s}from"react-hook-form";function a(r,e){for(var o={};r.length;){var t=r[0],a=t.type,i=t.message,n=t.path.substring(1).replace(/\//g,".");if(o[n]||(o[n]={message:i,type:""+a}),e){var m=o[n].types,l=m&&m[""+a];o[n]=s(n,e,o,""+a,l?[].concat(l,t.message):t.message)}r.shift()}return o}function i(s){return function(i,n,m){try{var l=Array.from(s instanceof o?s.Errors(i):t.Errors(s,i));return m.shouldUseNativeValidation&&r({},m),Promise.resolve(l.length?{values:{},errors:e(a(l,!m.shouldUseNativeValidation&&"all"===m.criteriaMode),m)}:{errors:{},values:i})}catch(r){return Promise.reject(r)}}}export{i as typeboxResolver};
//# sourceMappingURL=typebox.module.js.map
