{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,2CAA8C;AAC9C,qCAAwE;AACxE,2CAAyE;AAEzE,2CAAwC;AAExC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAM3B,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAgB,EAChB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACpC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACjC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,IAAA,4BAAsB,EAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAEhE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,eAAM,CAAC,IAAI,CAAC,0CAA0C,EAAE;gBACtD,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,EAAE,EAAE,GAAG,CAAC,EAAE;aACX,CAAC,CAAC;YACH,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,CAAC,CAAC;QACzD,CAAC;QAGD,MAAM,OAAO,GAAG,IAAA,uBAAiB,EAAC,KAAK,CAAC,CAAC;QACzC,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAGvE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;YAC7B,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,QAAQ,EAAE,IAAI;gBACd,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;aAClB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACjF,MAAM,IAAI,4BAAmB,CAAC,gBAAgB,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,eAAM,CAAC,IAAI,CAAC,4CAA4C,EAAE;gBACxD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC,CAAC;YACH,MAAM,IAAI,4BAAmB,CAAC,wBAAwB,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YACtD,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;gBACnD,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;YACH,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,CAAC,CAAC;QACjE,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAuC;YAClD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACvC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAC;QAEH,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;YACnC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;YAC/D,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,EAAE,EAAE,GAAG,CAAC,EAAE;SACX,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AA3FW,QAAA,YAAY,gBA2FvB;AAMK,MAAM,SAAS,GAAG,CAAC,YAAiD,EAAE,EAAE;IAC7E,OAAO,CAAC,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QACnE,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBACjC,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,aAAa,EAAE,YAAY;gBAC3B,QAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI;gBACxB,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;aACrB,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACd,eAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE;oBACzD,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,GAAG,EAAE,GAAG,CAAC,GAAG;oBACZ,aAAa,EAAE,YAAY;iBAC5B,CAAC,CAAC;gBACH,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1C,eAAM,CAAC,IAAI,CAAC,gDAAgD,EAAE;oBAC5D,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,GAAG,EAAE,GAAG,CAAC,GAAG;oBACZ,aAAa,EAAE,YAAY;oBAC3B,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;oBACvB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;oBACnB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;iBAC5B,CAAC,CAAC;gBACH,MAAM,IAAI,2BAAkB,CAC1B,kCAAkC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC5D,CAAC;YACJ,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBACtC,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;gBACvB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;gBACnB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;aAC5B,CAAC,CAAC;YAEH,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;gBAClC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,GAAG,EAAE,GAAG,CAAC,GAAG;gBACZ,aAAa,EAAE,YAAY;gBAC3B,QAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI;gBACxB,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;aACrB,CAAC,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAvDW,QAAA,SAAS,aAuDpB;AAMK,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAgB,EAChB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,IAAA,4BAAsB,EAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAEhE,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAA,uBAAiB,EAAC,KAAK,CAAC,CAAC;gBAEzC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;oBAC7B,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,QAAQ,EAAE,IAAI;wBACd,KAAK,EAAE,IAAI;wBACX,IAAI,EAAE,IAAI;wBACV,SAAS,EAAE,IAAI;wBACf,QAAQ,EAAE,IAAI;wBACd,QAAQ,EAAE,IAAI;wBACd,WAAW,EAAE,IAAI;qBAClB;iBACF,CAAC,CAAC;gBAEH,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC;oBACnF,GAAG,CAAC,IAAI,GAAG;wBACT,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,IAAI,EAAE,IAAI,CAAC,IAAuC;wBAClD,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;qBACxB,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,MAAM,CAAC;YAET,CAAC;QACH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AA7CW,QAAA,YAAY,gBA6CvB;AAMK,MAAM,sBAAsB,GAAG,KAAK,EACzC,GAAgB,EAChB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,4BAAmB,CAAC,yBAAyB,CAAC,CAAC;QAC3D,CAAC;QAGD,IAAI,GAAG,CAAC,IAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC/B,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;QAE9E,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,2BAAkB,CAAC,qBAAqB,CAAC,CAAC;QACtD,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;SAC5B,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,2BAAkB,CAAC,mBAAmB,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,CAAC,IAAK,CAAC,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,2BAAkB,CAAC,sCAAsC,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAvCW,QAAA,sBAAsB,0BAuCjC"}