"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const client_1 = require("@prisma/client");
const date_fns_1 = require("date-fns");
const errors_1 = require("@/utils/errors");
const prisma = new client_1.PrismaClient();
class NotificationService {
    static async createNotification(data, createdBy) {
        if (!data.recipientId || !data.type || !data.title || !data.message) {
            throw new errors_1.ValidationError('Recipient ID, type, title, and message are required');
        }
        const recipient = await prisma.user.findFirst({
            where: {
                id: data.recipientId,
                isActive: true,
            },
        });
        if (!recipient) {
            throw new errors_1.NotFoundError('Recipient not found');
        }
        const notification = await prisma.notification.create({
            data: {
                recipientId: data.recipientId,
                type: data.type,
                title: data.title.trim(),
                message: data.message.trim(),
                priority: data.priority || 'MEDIUM',
                channel: data.channel || 'IN_APP',
                scheduledFor: data.scheduledFor ? new Date(data.scheduledFor) : new Date(),
                patientId: data.patientId || null,
                appointmentId: data.appointmentId || null,
                labResultId: data.labResultId || null,
            },
            include: {
                recipient: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                    },
                },
                patient: data.patientId ? {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        patientId: true,
                    },
                } : false,
                appointment: data.appointmentId ? {
                    select: {
                        id: true,
                        date: true,
                        type: true,
                        status: true,
                    },
                } : false,
            },
        });
        return {
            success: true,
            data: { notification },
            message: 'Notification created successfully',
        };
    }
    static async getNotifications(query, userId, userRole) {
        const page = parseInt(query.page || '1', 10);
        const limit = Math.min(parseInt(query.limit || '10', 10), 100);
        const skip = (page - 1) * limit;
        const where = {};
        if (userRole !== 'ADMIN') {
            where.recipientId = userId;
        }
        if (query.recipientId && userRole === 'ADMIN') {
            where.recipientId = query.recipientId;
        }
        if (query.type) {
            where.type = query.type;
        }
        if (query.status) {
            where.status = query.status;
        }
        if (query.priority) {
            where.priority = query.priority;
        }
        if (query.channel) {
            where.channel = query.channel;
        }
        if (query.dateFrom || query.dateTo) {
            where.createdAt = {};
            if (query.dateFrom) {
                where.createdAt.gte = new Date(query.dateFrom);
            }
            if (query.dateTo) {
                where.createdAt.lte = new Date(query.dateTo);
            }
        }
        const [notifications, total] = await Promise.all([
            prisma.notification.findMany({
                where,
                skip,
                take: limit,
                orderBy: { createdAt: 'desc' },
                include: {
                    recipient: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                    patient: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            patientId: true,
                        },
                    },
                    appointment: {
                        select: {
                            id: true,
                            date: true,
                            type: true,
                            status: true,
                        },
                    },
                },
            }),
            prisma.notification.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: notifications,
            pagination: {
                page,
                limit,
                total,
                pages: totalPages,
            },
        };
    }
    static async markAsRead(id, userId, userRole) {
        const where = {
            id,
        };
        if (userRole !== 'ADMIN') {
            where.recipientId = userId;
        }
        const notification = await prisma.notification.findFirst({ where });
        if (!notification) {
            throw new errors_1.NotFoundError('Notification not found');
        }
        await prisma.notification.update({
            where: { id },
            data: {
                isRead: true,
                status: 'READ',
            },
        });
        return {
            success: true,
            data: null,
            message: 'Notification marked as read',
        };
    }
    static async getNotificationById(id, userId) {
        const notification = await prisma.notification.findFirst({
            where: {
                id,
                recipientId: userId,
            },
            include: {
                patient: {
                    select: {
                        patientId: true,
                        firstName: true,
                        lastName: true,
                    },
                },
                appointment: {
                    select: {
                        id: true,
                        date: true,
                        type: true,
                    },
                },
                labResult: {
                    select: {
                        id: true,
                        testType: true,
                        testDate: true,
                    },
                },
            },
        });
        if (!notification) {
            throw new errors_1.NotFoundError('Notification not found');
        }
        return {
            success: true,
            data: notification,
        };
    }
    static async markAllAsRead(userId) {
        const result = await prisma.notification.updateMany({
            where: {
                recipientId: userId,
                isRead: false,
            },
            data: {
                isRead: true,
                status: 'READ',
            },
        });
        return {
            success: true,
            data: { updatedCount: result.count },
            message: `Marked ${result.count} notifications as read`,
        };
    }
    static async deleteNotification(id, userId) {
        const notification = await prisma.notification.findFirst({
            where: {
                id,
                recipientId: userId,
            },
        });
        if (!notification) {
            throw new errors_1.NotFoundError('Notification not found');
        }
        await prisma.notification.delete({
            where: { id },
        });
        return {
            success: true,
            data: null,
            message: 'Notification deleted successfully',
        };
    }
    static async createAppointmentReminders(appointmentId, createdBy) {
        const appointment = await prisma.appointment.findFirst({
            where: {
                id: appointmentId,
                isDeleted: false,
            },
            include: {
                patient: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                    },
                },
                provider: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
            },
        });
        if (!appointment) {
            throw new errors_1.NotFoundError('Appointment not found');
        }
        const appointmentDate = new Date(appointment.date);
        const reminders = [];
        const reminderSchedules = [
            { hours: 24, title: '24-Hour Reminder' },
            { hours: 2, title: '2-Hour Reminder' },
            { minutes: 15, title: '15-Minute Reminder' },
        ];
        for (const schedule of reminderSchedules) {
            let scheduledFor;
            if (schedule.hours) {
                scheduledFor = (0, date_fns_1.addHours)(appointmentDate, -schedule.hours);
            }
            else {
                scheduledFor = (0, date_fns_1.addMinutes)(appointmentDate, -schedule.minutes);
            }
            if ((0, date_fns_1.isAfter)(scheduledFor, new Date())) {
                const patientReminder = await this.createNotification({
                    recipientId: appointment.patient.id,
                    type: 'APPOINTMENT_REMINDER',
                    title: `${schedule.title}: Upcoming Appointment`,
                    message: `You have an appointment scheduled for ${appointmentDate.toLocaleString()} with Dr. ${appointment.provider.firstName} ${appointment.provider.lastName}.`,
                    priority: schedule.minutes ? 'HIGH' : 'MEDIUM',
                    channel: 'EMAIL',
                    scheduledFor: scheduledFor.toISOString(),
                    appointmentId: appointment.id,
                    patientId: appointment.patient.id,
                    metadata: {
                        reminderType: schedule.hours ? `${schedule.hours}h` : `${schedule.minutes}m`,
                        appointmentType: appointment.type,
                    },
                }, createdBy);
                reminders.push(patientReminder.data?.notification);
                if (schedule.hours === 2 || schedule.minutes === 15) {
                    const providerReminder = await this.createNotification({
                        recipientId: appointment.provider.id,
                        type: 'APPOINTMENT_REMINDER',
                        title: `${schedule.title}: Patient Appointment`,
                        message: `Upcoming appointment with ${appointment.patient.firstName} ${appointment.patient.lastName} at ${appointmentDate.toLocaleString()}.`,
                        priority: schedule.minutes ? 'HIGH' : 'MEDIUM',
                        channel: 'IN_APP',
                        scheduledFor: scheduledFor.toISOString(),
                        appointmentId: appointment.id,
                        patientId: appointment.patient.id,
                        metadata: {
                            reminderType: schedule.hours ? `${schedule.hours}h` : `${schedule.minutes}m`,
                            appointmentType: appointment.type,
                        },
                    }, createdBy);
                    reminders.push(providerReminder.data?.notification);
                }
            }
        }
        return {
            success: true,
            data: { reminders },
            message: `Created ${reminders.length} appointment reminders`,
        };
    }
    static async sendLabResultNotification(labResultId, createdBy) {
        const labResult = await prisma.labResult.findFirst({
            where: {
                id: labResultId,
                isDeleted: false,
            },
            include: {
                patient: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                    },
                },
                creator: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        if (!labResult) {
            throw new errors_1.NotFoundError('Lab result not found');
        }
        let priority = 'MEDIUM';
        if (labResult.flagged) {
            if (labResult.criticalValue) {
                priority = 'CRITICAL';
            }
            else {
                priority = 'HIGH';
            }
        }
        const notification = await this.createNotification({
            recipientId: labResult.patient.id,
            type: 'LAB_RESULT_AVAILABLE',
            title: 'New Lab Results Available',
            message: `Your ${labResult.testName} lab results from ${labResult.testDate.toLocaleDateString()} are now available for review.`,
            priority,
            channel: 'EMAIL',
            labResultId: labResult.id,
            patientId: labResult.patient.id,
            metadata: {
                testType: labResult.testType,
                hasFlags: labResult.flagged,
                orderedBy: labResult.orderedBy,
            },
        }, createdBy);
        return notification;
    }
    static async processScheduledNotifications() {
        const now = new Date();
        const scheduledNotifications = await prisma.notification.findMany({
            where: {
                status: 'PENDING',
                scheduledFor: {
                    lte: now,
                },
            },
            include: {
                recipient: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true,
                    },
                },
            },
            take: 100,
        });
        let processed = 0;
        for (const notification of scheduledNotifications) {
            try {
                await this.sendNotification(notification);
                await prisma.notification.update({
                    where: { id: notification.id },
                    data: {
                        status: 'SENT',
                    },
                });
                processed++;
            }
            catch (error) {
                console.error(`Failed to send notification ${notification.id}:`, error);
                await prisma.notification.update({
                    where: { id: notification.id },
                    data: {
                        status: 'FAILED',
                    },
                });
            }
        }
        return {
            success: true,
            data: { processed },
            message: `Processed ${processed} scheduled notifications`,
        };
    }
    static async sendNotification(notification) {
        switch (notification.channel) {
            case 'EMAIL':
                await this.sendEmailNotification(notification);
                break;
            case 'SMS':
                await this.sendSMSNotification(notification);
                break;
            case 'IN_APP':
                break;
            default:
                throw new Error(`Unsupported notification channel: ${notification.channel}`);
        }
    }
    static async sendEmailNotification(notification) {
        console.log(`Sending email to ${notification.recipient.email}:`);
        console.log(`Subject: ${notification.title}`);
        console.log(`Message: ${notification.message}`);
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    static async sendSMSNotification(notification) {
        console.log(`Sending SMS to ${notification.recipient.phone}:`);
        console.log(`Message: ${notification.message}`);
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    static async getNotificationStats(userId, userRole) {
        const where = {};
        if (userRole !== 'ADMIN') {
            where.recipientId = userId;
        }
        const [total, unread, byType, byStatus, byPriority,] = await Promise.all([
            prisma.notification.count({ where }),
            prisma.notification.count({
                where: { ...where, status: 'UNREAD' }
            }),
            prisma.notification.groupBy({
                by: ['type'],
                where,
                _count: { type: true },
            }),
            prisma.notification.groupBy({
                by: ['status'],
                where,
                _count: { status: true },
            }),
            prisma.notification.groupBy({
                by: ['priority'],
                where,
                _count: { priority: true },
            }),
        ]);
        const stats = {
            total,
            unread,
            typeDistribution: byType.reduce((acc, item) => {
                acc[item.type] = item._count.type;
                return acc;
            }, {}),
            statusDistribution: byStatus.reduce((acc, item) => {
                acc[item.status] = item._count.status;
                return acc;
            }, {}),
            priorityDistribution: byPriority.reduce((acc, item) => {
                acc[item.priority] = item._count.priority;
                return acc;
            }, {}),
        };
        return {
            success: true,
            data: { stats },
        };
    }
}
exports.NotificationService = NotificationService;
//# sourceMappingURL=notificationService.js.map