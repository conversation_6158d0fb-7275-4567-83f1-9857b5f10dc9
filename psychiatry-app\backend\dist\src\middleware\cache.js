"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCacheHealth = exports.cacheManager = exports.invalidateAppointmentCache = exports.invalidateLabResultCache = exports.invalidatePatientCache = exports.statsCache = exports.analyticsCache = exports.appointmentCache = exports.labResultCache = exports.patientCache = exports.invalidateCacheMiddleware = exports.cacheMiddleware = void 0;
const crypto_1 = require("crypto");
class MemoryCache {
    constructor(maxSize, defaultTTL) {
        this.cache = new Map();
        this.maxSize = 1000;
        this.defaultTTL = 5 * 60 * 1000;
        if (maxSize)
            this.maxSize = maxSize;
        if (defaultTTL)
            this.defaultTTL = defaultTTL;
        setInterval(() => this.cleanup(), 60 * 1000);
    }
    generateKey(req) {
        const keyData = {
            method: req.method,
            url: req.originalUrl,
            query: req.query,
            userId: req.user?.id || 'anonymous'
        };
        return (0, crypto_1.createHash)('md5').update(JSON.stringify(keyData)).digest('hex');
    }
    get(key) {
        const entry = this.cache.get(key);
        if (!entry)
            return null;
        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    set(key, data, ttl) {
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl: ttl || this.defaultTTL
        });
    }
    delete(key) {
        this.cache.delete(key);
    }
    clear() {
        this.cache.clear();
    }
    cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > entry.ttl) {
                this.cache.delete(key);
            }
        }
    }
    getStats() {
        return {
            size: this.cache.size,
            maxSize: this.maxSize
        };
    }
    invalidatePattern(pattern) {
        for (const key of this.cache.keys()) {
            if (key.includes(pattern)) {
                this.cache.delete(key);
            }
        }
    }
}
const cache = new MemoryCache();
const cacheMiddleware = (ttl) => {
    return (req, res, next) => {
        if (req.method !== 'GET') {
            return next();
        }
        if (!req.user) {
            return next();
        }
        const key = cache['generateKey'](req);
        const cachedData = cache.get(key);
        if (cachedData) {
            res.set({
                'X-Cache': 'HIT',
                'X-Cache-Key': key
            });
            return res.json(cachedData);
        }
        const originalJson = res.json;
        res.json = function (data) {
            if (res.statusCode === 200 && data?.success) {
                cache.set(key, data, ttl);
            }
            res.set({
                'X-Cache': 'MISS',
                'X-Cache-Key': key
            });
            return originalJson.call(this, data);
        };
        next();
    };
};
exports.cacheMiddleware = cacheMiddleware;
const invalidateCacheMiddleware = (patterns) => {
    return (req, res, next) => {
        const originalJson = res.json;
        res.json = function (data) {
            if (res.statusCode < 400 && data?.success) {
                patterns.forEach(pattern => {
                    cache.invalidatePattern(pattern);
                });
            }
            return originalJson.call(this, data);
        };
        next();
    };
};
exports.invalidateCacheMiddleware = invalidateCacheMiddleware;
exports.patientCache = (0, exports.cacheMiddleware)(5 * 60 * 1000);
exports.labResultCache = (0, exports.cacheMiddleware)(10 * 60 * 1000);
exports.appointmentCache = (0, exports.cacheMiddleware)(2 * 60 * 1000);
exports.analyticsCache = (0, exports.cacheMiddleware)(30 * 60 * 1000);
exports.statsCache = (0, exports.cacheMiddleware)(15 * 60 * 1000);
exports.invalidatePatientCache = (0, exports.invalidateCacheMiddleware)([
    'patients',
    'analytics',
    'stats'
]);
exports.invalidateLabResultCache = (0, exports.invalidateCacheMiddleware)([
    'lab-results',
    'patients',
    'analytics'
]);
exports.invalidateAppointmentCache = (0, exports.invalidateCacheMiddleware)([
    'appointments',
    'analytics',
    'stats'
]);
exports.cacheManager = {
    getStats: () => cache.getStats(),
    clearAll: () => cache.clear(),
    invalidate: (patterns) => {
        patterns.forEach(pattern => cache.invalidatePattern(pattern));
    },
    warmUp: async () => {
        console.log('Cache warm-up not implemented yet');
    }
};
const getCacheHealth = () => {
    const stats = cache.getStats();
    const memoryUsage = process.memoryUsage();
    return {
        cache: {
            size: stats.size,
            maxSize: stats.maxSize,
            utilization: Math.round((stats.size / stats.maxSize) * 100),
            status: stats.size < stats.maxSize * 0.9 ? 'healthy' : 'near_full'
        },
        memory: {
            heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
            heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
            external: Math.round(memoryUsage.external / 1024 / 1024)
        }
    };
};
exports.getCacheHealth = getCacheHealth;
exports.default = cache;
//# sourceMappingURL=cache.js.map