/*
  Warnings:

  - You are about to drop the column `flags` on the `lab_results` table. All the data in the column will be lost.
  - You are about to drop the column `labName` on the `lab_results` table. All the data in the column will be lost.
  - You are about to drop the column `normalRanges` on the `lab_results` table. All the data in the column will be lost.
  - You are about to drop the column `results` on the `lab_results` table. All the data in the column will be lost.
  - Added the required column `createdBy` to the `lab_results` table without a default value. This is not possible if the table is not empty.
  - Added the required column `referenceRange` to the `lab_results` table without a default value. This is not possible if the table is not empty.
  - Added the required column `testCategory` to the `lab_results` table without a default value. This is not possible if the table is not empty.
  - Added the required column `testName` to the `lab_results` table without a default value. This is not possible if the table is not empty.
  - Added the required column `value` to the `lab_results` table without a default value. This is not possible if the table is not empty.
  - Added the required column `providerId` to the `recurring_appointments` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "users" ADD COLUMN "phone" TEXT;

-- CreateTable
CREATE TABLE "psych_tests" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "patientId" TEXT NOT NULL,
    "testName" TEXT NOT NULL,
    "testCategory" TEXT NOT NULL,
    "version" TEXT,
    "administeredBy" TEXT NOT NULL,
    "administeredDate" DATETIME NOT NULL,
    "completionTime" INTEGER,
    "location" TEXT,
    "rawScore" INTEGER,
    "totalScore" INTEGER,
    "subscaleScores" TEXT,
    "scaledScore" INTEGER,
    "percentile" INTEGER,
    "tScore" INTEGER,
    "zScore" REAL,
    "severity" TEXT,
    "clinicalRange" TEXT,
    "interpretation" TEXT,
    "recommendations" TEXT,
    "responses" TEXT NOT NULL,
    "validity" TEXT,
    "validityIndices" TEXT,
    "notes" TEXT,
    "followUpDate" DATETIME,
    "followUpRequired" BOOLEAN NOT NULL DEFAULT false,
    "batteryId" TEXT,
    "sessionNumber" INTEGER,
    "baselineTest" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "psych_tests_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "patients" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "mental_status_exams" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "patientId" TEXT NOT NULL,
    "examDate" DATETIME NOT NULL,
    "examinerId" TEXT NOT NULL,
    "appearance_grooming" TEXT,
    "appearance_dress" TEXT,
    "appearance_hygiene" TEXT,
    "behavior_eye_contact" TEXT,
    "behavior_motor" TEXT,
    "behavior_cooperation" TEXT,
    "speech_rate" TEXT,
    "speech_volume" TEXT,
    "speech_tone" TEXT,
    "speech_fluency" TEXT,
    "mood_reported" TEXT,
    "mood_observed" TEXT,
    "affect_type" TEXT,
    "affect_range" TEXT,
    "affect_appropriateness" TEXT,
    "thought_process" TEXT,
    "thought_organization" TEXT,
    "thought_flow" TEXT,
    "thought_content" TEXT,
    "delusions" BOOLEAN NOT NULL DEFAULT false,
    "delusion_type" TEXT,
    "obsessions" BOOLEAN NOT NULL DEFAULT false,
    "compulsions" BOOLEAN NOT NULL DEFAULT false,
    "phobias" BOOLEAN NOT NULL DEFAULT false,
    "hallucinations" BOOLEAN NOT NULL DEFAULT false,
    "hallucination_type" TEXT,
    "illusions" BOOLEAN NOT NULL DEFAULT false,
    "depersonalization" BOOLEAN NOT NULL DEFAULT false,
    "derealization" BOOLEAN NOT NULL DEFAULT false,
    "orientation_person" BOOLEAN NOT NULL DEFAULT true,
    "orientation_place" BOOLEAN NOT NULL DEFAULT true,
    "orientation_time" BOOLEAN NOT NULL DEFAULT true,
    "orientation_situation" BOOLEAN NOT NULL DEFAULT true,
    "attention_span" TEXT,
    "concentration" TEXT,
    "memory_immediate" TEXT,
    "memory_recent" TEXT,
    "memory_remote" TEXT,
    "abstract_thinking" TEXT,
    "insight_level" TEXT,
    "insight_description" TEXT,
    "judgment_level" TEXT,
    "judgment_description" TEXT,
    "suicidal_ideation" TEXT,
    "suicidal_risk" TEXT,
    "homicidal_ideation" TEXT,
    "homicidal_risk" TEXT,
    "clinical_notes" TEXT,
    "recommendations" TEXT,
    "followup_needed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "mental_status_exams_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "patients" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "mental_status_exams_examinerId_fkey" FOREIGN KEY ("examinerId") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "medication_history" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "patientId" TEXT NOT NULL,
    "medicationName" TEXT NOT NULL,
    "genericName" TEXT,
    "brandName" TEXT,
    "strength" TEXT NOT NULL,
    "dosage" TEXT NOT NULL,
    "frequency" TEXT NOT NULL,
    "route" TEXT NOT NULL,
    "indication" TEXT NOT NULL,
    "startDate" DATETIME NOT NULL,
    "endDate" DATETIME,
    "duration" TEXT,
    "prescribedBy" TEXT NOT NULL,
    "prescriberId" TEXT,
    "pharmacy" TEXT,
    "sideEffects" TEXT,
    "effectiveness" TEXT,
    "adherence" TEXT,
    "adherenceNotes" TEXT,
    "discontinuedReason" TEXT,
    "allergicReaction" BOOLEAN NOT NULL DEFAULT false,
    "interactions" TEXT,
    "monitoring" TEXT,
    "prn" BOOLEAN NOT NULL DEFAULT false,
    "notes" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "medication_history_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "patients" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "medication_history_prescriberId_fkey" FOREIGN KEY ("prescriberId") REFERENCES "users" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_lab_results" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "patientId" TEXT NOT NULL,
    "testName" TEXT NOT NULL,
    "testCategory" TEXT NOT NULL,
    "testType" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "numericValue" REAL,
    "unit" TEXT,
    "referenceRange" TEXT NOT NULL,
    "referenceMin" REAL,
    "referenceMax" REAL,
    "status" TEXT NOT NULL,
    "flagged" BOOLEAN NOT NULL DEFAULT false,
    "testDate" DATETIME NOT NULL,
    "resultDate" DATETIME,
    "orderedBy" TEXT NOT NULL,
    "reviewedBy" TEXT,
    "laboratoryId" TEXT,
    "specimenType" TEXT,
    "notes" TEXT,
    "criticalValue" BOOLEAN NOT NULL DEFAULT false,
    "deltaCheck" BOOLEAN NOT NULL DEFAULT false,
    "createdBy" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "lab_results_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "lab_results_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "patients" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_lab_results" ("createdAt", "deletedAt", "id", "isDeleted", "notes", "orderedBy", "patientId", "status", "testDate", "testType", "updatedAt") SELECT "createdAt", "deletedAt", "id", "isDeleted", "notes", "orderedBy", "patientId", "status", "testDate", "testType", "updatedAt" FROM "lab_results";
DROP TABLE "lab_results";
ALTER TABLE "new_lab_results" RENAME TO "lab_results";
CREATE INDEX "lab_results_patientId_idx" ON "lab_results"("patientId");
CREATE INDEX "lab_results_testName_idx" ON "lab_results"("testName");
CREATE INDEX "lab_results_testCategory_idx" ON "lab_results"("testCategory");
CREATE INDEX "lab_results_testType_idx" ON "lab_results"("testType");
CREATE INDEX "lab_results_testDate_idx" ON "lab_results"("testDate");
CREATE INDEX "lab_results_resultDate_idx" ON "lab_results"("resultDate");
CREATE INDEX "lab_results_status_idx" ON "lab_results"("status");
CREATE INDEX "lab_results_flagged_idx" ON "lab_results"("flagged");
CREATE INDEX "lab_results_criticalValue_idx" ON "lab_results"("criticalValue");
CREATE INDEX "lab_results_createdBy_idx" ON "lab_results"("createdBy");
CREATE INDEX "lab_results_isDeleted_idx" ON "lab_results"("isDeleted");
CREATE INDEX "lab_results_createdAt_idx" ON "lab_results"("createdAt");
CREATE INDEX "lab_results_patientId_testDate_idx" ON "lab_results"("patientId", "testDate");
CREATE INDEX "lab_results_patientId_testName_idx" ON "lab_results"("patientId", "testName");
CREATE TABLE "new_notifications" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "recipientId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'MEDIUM',
    "channel" TEXT NOT NULL DEFAULT 'IN_APP',
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "isProcessed" BOOLEAN NOT NULL DEFAULT false,
    "scheduledFor" DATETIME,
    "processedAt" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "patientId" TEXT,
    "appointmentId" TEXT,
    "labResultId" TEXT,
    CONSTRAINT "notifications_labResultId_fkey" FOREIGN KEY ("labResultId") REFERENCES "lab_results" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "notifications_appointmentId_fkey" FOREIGN KEY ("appointmentId") REFERENCES "appointments" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "notifications_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "patients" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "notifications_recipientId_fkey" FOREIGN KEY ("recipientId") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_notifications" ("appointmentId", "createdAt", "id", "isProcessed", "isRead", "labResultId", "message", "patientId", "priority", "processedAt", "recipientId", "scheduledFor", "title", "type", "updatedAt") SELECT "appointmentId", "createdAt", "id", "isProcessed", "isRead", "labResultId", "message", "patientId", "priority", "processedAt", "recipientId", "scheduledFor", "title", "type", "updatedAt" FROM "notifications";
DROP TABLE "notifications";
ALTER TABLE "new_notifications" RENAME TO "notifications";
CREATE INDEX "notifications_recipientId_idx" ON "notifications"("recipientId");
CREATE INDEX "notifications_type_idx" ON "notifications"("type");
CREATE INDEX "notifications_channel_idx" ON "notifications"("channel");
CREATE INDEX "notifications_status_idx" ON "notifications"("status");
CREATE INDEX "notifications_isRead_idx" ON "notifications"("isRead");
CREATE INDEX "notifications_isProcessed_idx" ON "notifications"("isProcessed");
CREATE INDEX "notifications_scheduledFor_idx" ON "notifications"("scheduledFor");
CREATE INDEX "notifications_createdAt_idx" ON "notifications"("createdAt");
CREATE INDEX "notifications_recipientId_isRead_idx" ON "notifications"("recipientId", "isRead");
CREATE TABLE "new_recurring_appointments" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "patientId" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "startDate" DATETIME NOT NULL,
    "endDate" DATETIME,
    "duration" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "frequency" TEXT NOT NULL,
    "interval" INTEGER NOT NULL DEFAULT 1,
    "dayOfWeek" INTEGER,
    "dayOfMonth" INTEGER,
    "timeSlot" TEXT NOT NULL,
    "notes" TEXT,
    "maxOccurrences" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "recurring_appointments_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "recurring_appointments_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "patients" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_recurring_appointments" ("createdAt", "dayOfMonth", "dayOfWeek", "deletedAt", "duration", "endDate", "frequency", "id", "isActive", "isDeleted", "maxOccurrences", "notes", "patientId", "startDate", "timeSlot", "type", "updatedAt") SELECT "createdAt", "dayOfMonth", "dayOfWeek", "deletedAt", "duration", "endDate", "frequency", "id", "isActive", "isDeleted", "maxOccurrences", "notes", "patientId", "startDate", "timeSlot", "type", "updatedAt" FROM "recurring_appointments";
DROP TABLE "recurring_appointments";
ALTER TABLE "new_recurring_appointments" RENAME TO "recurring_appointments";
CREATE INDEX "recurring_appointments_patientId_idx" ON "recurring_appointments"("patientId");
CREATE INDEX "recurring_appointments_providerId_idx" ON "recurring_appointments"("providerId");
CREATE INDEX "recurring_appointments_startDate_idx" ON "recurring_appointments"("startDate");
CREATE INDEX "recurring_appointments_isActive_idx" ON "recurring_appointments"("isActive");
CREATE INDEX "recurring_appointments_isDeleted_idx" ON "recurring_appointments"("isDeleted");
CREATE INDEX "recurring_appointments_frequency_idx" ON "recurring_appointments"("frequency");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE INDEX "psych_tests_patientId_idx" ON "psych_tests"("patientId");

-- CreateIndex
CREATE INDEX "psych_tests_testName_idx" ON "psych_tests"("testName");

-- CreateIndex
CREATE INDEX "psych_tests_testCategory_idx" ON "psych_tests"("testCategory");

-- CreateIndex
CREATE INDEX "psych_tests_administeredDate_idx" ON "psych_tests"("administeredDate");

-- CreateIndex
CREATE INDEX "psych_tests_administeredBy_idx" ON "psych_tests"("administeredBy");

-- CreateIndex
CREATE INDEX "psych_tests_severity_idx" ON "psych_tests"("severity");

-- CreateIndex
CREATE INDEX "psych_tests_patientId_administeredDate_idx" ON "psych_tests"("patientId", "administeredDate");

-- CreateIndex
CREATE INDEX "mental_status_exams_patientId_idx" ON "mental_status_exams"("patientId");

-- CreateIndex
CREATE INDEX "mental_status_exams_examinerId_idx" ON "mental_status_exams"("examinerId");

-- CreateIndex
CREATE INDEX "mental_status_exams_examDate_idx" ON "mental_status_exams"("examDate");

-- CreateIndex
CREATE INDEX "mental_status_exams_suicidal_risk_idx" ON "mental_status_exams"("suicidal_risk");

-- CreateIndex
CREATE INDEX "mental_status_exams_homicidal_risk_idx" ON "mental_status_exams"("homicidal_risk");

-- CreateIndex
CREATE INDEX "mental_status_exams_patientId_examDate_idx" ON "mental_status_exams"("patientId", "examDate");

-- CreateIndex
CREATE INDEX "medication_history_patientId_idx" ON "medication_history"("patientId");

-- CreateIndex
CREATE INDEX "medication_history_prescriberId_idx" ON "medication_history"("prescriberId");

-- CreateIndex
CREATE INDEX "medication_history_medicationName_idx" ON "medication_history"("medicationName");

-- CreateIndex
CREATE INDEX "medication_history_startDate_idx" ON "medication_history"("startDate");

-- CreateIndex
CREATE INDEX "medication_history_endDate_idx" ON "medication_history"("endDate");

-- CreateIndex
CREATE INDEX "medication_history_isActive_idx" ON "medication_history"("isActive");

-- CreateIndex
CREATE INDEX "medication_history_allergicReaction_idx" ON "medication_history"("allergicReaction");

-- CreateIndex
CREATE INDEX "medication_history_patientId_startDate_idx" ON "medication_history"("patientId", "startDate");
