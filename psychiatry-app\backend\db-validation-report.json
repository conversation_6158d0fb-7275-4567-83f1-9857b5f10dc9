{"timestamp": "2025-07-22T00:35:44.385Z", "errors": [{"type": "QUERY_ERROR", "message": "Failed to check orphans for medication_history.patientId: \nInvalid `prisma.$queryRawUnsafe()` invocation:\n\n\nRaw query failed. Code: `1`. Message: `no such table: medication_history`", "details": []}, {"type": "QUERY_ERROR", "message": "Failed to check orphans for psych_tests.patientId: \nInvalid `prisma.$queryRawUnsafe()` invocation:\n\n\nRaw query failed. Code: `1`. Message: `no such table: psych_tests`", "details": []}, {"type": "QUERY_ERROR", "message": "Failed to check orphans for mental_status_exams.patientId: \nInvalid `prisma.$queryRawUnsafe()` invocation:\n\n\nRaw query failed. Code: `1`. Message: `no such table: mental_status_exams`", "details": []}], "summary": "Found 3 data integrity issues"}