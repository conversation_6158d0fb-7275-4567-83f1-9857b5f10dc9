/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v8.4", key: "1d3kfm" }],
  ["path", { d: "M8 18v-7.7L16 9v7", key: "1oie6o" }],
  ["circle", { cx: "14", cy: "16", r: "2", key: "1bzzi3" }],
  ["circle", { cx: "6", cy: "18", r: "2", key: "1fncim" }]
];
const FileMusic = createLucideIcon("file-music", __iconNode);

export { __iconNode, FileMusic as default };
//# sourceMappingURL=file-music.js.map
