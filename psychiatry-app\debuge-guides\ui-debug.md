# 🎨 UI Debug Guide (Tailwind CSS + Recharts)

## 🌀 Tailwind CSS Debugging
- Ensure paths are correct in `tailwind.config.js`
- Use `class="border border-red-500"` to test styles
- VSCode Tailwind extension for intellisense

## 📈 Recharts Debugging
```tsx
<LineChart data={data}>
  <Line type="monotone" dataKey="value" stroke="#8884d8" />
  <Tooltip />
</LineChart>
```

### 🐛 Issues
- Wrong data shape
- Missing keys (e.g. `dataKey`)
- SVG sizing issues
