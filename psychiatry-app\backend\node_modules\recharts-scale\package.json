{"name": "recharts-scale", "version": "0.4.5", "description": "Scale of Cartesian Coordinates", "main": "lib/index", "module": "es6/index.js", "jsnext:main": "es6/index", "keywords": ["Scale", "Cartesian Coordinates"], "files": ["*.md", "demo", "es6", "lib", "umd", "src"], "scripts": {"build": "npm run build-cjs && npm run build-es6 && rimraf umd && npm run build-umd && npm run build-min", "build-cjs": "rimraf lib && cross-env BABEL_ENV=commonjs babel ./src -d lib", "build-es6": "rimraf es6 && cross-env babel ./src -d es6", "build-umd": "cross-env NODE_ENV=development BABEL_ENV=commonjs webpack --entry ./src/index.js -o umd", "build-min": "cross-env NODE_ENV=production BABEL_ENV=commonjs webpack --entry ./src/index.js -o umd", "test": "nyc ava --verbose", "autofix": "eslint src test --fix", "lint": "eslint src test"}, "pre-commit": ["lint"], "repository": {"type": "git", "url": "https://github.com/recharts/recharts-scale.git"}, "author": "recharts group", "bugs": {"url": "https://github.com/recharts/recharts-scale/issues"}, "homepage": "https://github.com/recharts/recharts-scale", "dependencies": {"decimal.js-light": "^2.4.1"}, "devDependencies": {"@ava/babel": "^1.0.1", "@babel/cli": "^7.1.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/register": "^7.0.0", "ava": "^3.15.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "cross-env": "^7.0.3", "eslint": "^7.17.0", "eslint-config-airbnb": "^18.2.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-react": "^7.11.1", "esm": "^3.2.25", "nyc": "^15.1.0", "pre-commit": "^1.1.3", "webpack": "^5.15.0", "webpack-cli": "^4.3.1"}, "ava": {"files": ["test/**/*.test.js"], "ignoredByWatcher": ["umd/**/*"], "failFast": true, "tap": true, "require": ["esm", "@babel/register"], "babel": {"testOptions": {"presets": ["@babel/preset-env"]}}}, "license": "MIT"}