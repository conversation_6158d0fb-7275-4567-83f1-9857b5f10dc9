{"author": "<EMAIL>", "browser": "dist/umd/index.js", "bugs": {"url": "https://github.com/planttheidea/fast-equals/issues"}, "description": "A blazing fast equality comparison, either shallow or deep", "devDependencies": {"@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-node-resolve": "^15.3.1", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.1.6", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.184", "@types/node": "^18.19.69", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "decircularize": "^1.0.0", "deep-eql": "^4.1.4", "deep-equal": "^2.0.5", "dequal": "^2.0.3", "eslint": "^8.57.1", "eslint-friendly-formatter": "^4.0.1", "eslint-webpack-plugin": "^4.0.0", "fast-deep-equal": "^3.1.3", "fast-glob": "^3.3.3", "html-webpack-plugin": "^5.5.0", "in-publish": "^2.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-expect-message": "^1.1.3", "lodash": "^4.17.21", "nano-equal": "^2.0.2", "prettier": "^2.8.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-fast-compare": "^3.2.1", "release-it": "^17.11.0", "rollup": "^3.29.5", "shallow-equal-fuzzy": "^0.0.2", "tinybench": "^2.9.0", "ts-jest": "^29.0.3", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "underscore": "^1.13.4", "webpack": "^5.76.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2"}, "engines": {"node": ">=6.0.0"}, "exports": {".": {"import": {"types": "./dist/esm/types/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/cjs/types/index.d.ts", "default": "./dist/cjs/index.cjs"}, "default": {"types": "./dist/umd/types/index.d.ts", "default": "./dist/umd/index.js"}}}, "homepage": "https://github.com/planttheidea/fast-equals#readme", "keywords": ["fast", "equal", "equals", "deep-equal", "equivalent"], "license": "MIT", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "name": "fast-equals", "repository": {"type": "git", "url": "git+https://github.com/planttheidea/fast-equals.git"}, "scripts": {"benchmark": "npm run build:esm && node benchmark/index.js", "build": "npm run build:esm && npm run build:cjs && npm run build:umd && npm run build:min && npm run build:types", "build:cjs": "rm -rf dist/cjs && NODE_ENV=production rollup -c config/rollup/config.cjs.js && tsc -p ./config/tsconfig/cjs.json", "build:esm": "rm -rf dist/esm && NODE_ENV=production rollup -c config/rollup/config.esm.js && tsc -p ./config/tsconfig/esm.json", "build:min": "rm -rf dist/min && NODE_ENV=production rollup -c config/rollup/config.min.js && tsc -p ./config/tsconfig/min.json", "build:types": "node scripts/fallback-types.mjs && node scripts/apply-type-file-extensions.mjs", "build:umd": "rm -rf dist/umd && NODE_ENV=production rollup -c config/rollup/config.umd.js && tsc -p ./config/tsconfig/umd.json", "dev": "NODE_ENV=development webpack serve --progress --config=config/webpack.config.js", "format": "prettier **/*.ts --write", "lint": "eslint src/*.ts", "lint:fix": "npm run lint -- --fix", "start": "npm run dev", "release": "release-it", "release:beta": "release-it --config=.release-it.beta.json", "release:scripts": "npm run typecheck && npm run lint && npm run test:coverage && npm run build", "test": "NODE_PATH=. jest", "test:coverage": "rm -rf coverage && npm test -- --coverage", "test:watch": "npm test -- --watch", "typecheck": "tsc --noEmit"}, "sideEffects": false, "type": "module", "types": "./index.d.ts", "version": "5.2.2"}