"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const patientController_1 = require("@/controllers/patientController");
const labResultController_1 = require("@/controllers/labResultController");
const assessmentController_1 = require("@/controllers/assessmentController");
const auth_1 = require("@/middleware/auth");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/stats', patientController_1.PatientController.getPatientStats);
router.get('/', patientController_1.PatientController.getPatients);
router.post('/', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN']), patientController_1.PatientController.createPatient);
router.get('/:id', patientController_1.PatientController.getPatientById);
router.put('/:id', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN']), patientController_1.PatientController.updatePatient);
router.delete('/:id', (0, auth_1.authorize)(['ADMIN']), patientController_1.PatientController.deletePatient);
router.get('/:patientId/lab-results', labResultController_1.LabResultController.getPatientLabResults);
router.get('/:patientId/lab-results/trends', labResultController_1.LabResultController.getLabResultTrends);
router.post('/:patientId/assessment-sessions', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN']), assessmentController_1.AssessmentController.createAssessmentSession);
router.get('/:patientId/assessment-sessions', assessmentController_1.AssessmentController.getPatientAssessmentSessions);
router.put('/:patientId/assessment-sessions/:id', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN']), assessmentController_1.AssessmentController.updateAssessmentSession);
router.delete('/:patientId/assessment-sessions/:id', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN']), assessmentController_1.AssessmentController.deleteAssessmentSession);
exports.default = router;
//# sourceMappingURL=patients.js.map