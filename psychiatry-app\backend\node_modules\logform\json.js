'use strict';

const format = require('./format');
const { MESSAGE } = require('triple-beam');
const stringify = require('safe-stable-stringify');

/*
 * function replacer (key, value)
 * Handles proper stringification of Buffer and bigint output.
 */
function replacer(key, value) {
  // safe-stable-stringify does support BigInt, however, it doesn't wrap the value in quotes.
  // Leading to a loss in fidelity if the resulting string is parsed.
  // It would also be a breaking change for logform.
  if (typeof value === 'bigint')
    return value.toString();
  return value;
}

/*
 * function json (info)
 * Returns a new instance of the JSON format that turns a log `info`
 * object into pure JSON. This was previously exposed as { json: true }
 * to transports in `winston < 3.0.0`.
 */
module.exports = format((info, opts) => {
  const jsonStringify = stringify.configure(opts);
  info[MESSAGE] = jsonStringify(info, opts.replacer || replacer, opts.space);
  return info;
});
