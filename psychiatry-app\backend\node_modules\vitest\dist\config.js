export { d as defaultBrowserPort, e as extraInlineDeps } from './chunks/constants.DnKduX2e.js';
export { c as configDefaults, a as coverageConfigDefaults, d as defaultExclude, b as defaultInclude } from './chunks/defaults.B7q_naMc.js';
export { mergeConfig } from 'vite';
import 'node:os';
import './chunks/env.D4Lgay0q.js';
import 'std-env';

function defineConfig(config) {
	return config;
}
function defineProject(config) {
	return config;
}
/**
* @deprecated use the `projects` field in the root config instead
*/
function defineWorkspace(config) {
	return config;
}

export { defineConfig, defineProject, defineWorkspace };
