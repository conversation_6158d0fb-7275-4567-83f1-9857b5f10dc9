# 🧬 Database Debug Guide (Prisma + SQLite/PostgreSQL)

## 🔧 Essential Commands
```bash
npx prisma studio          # Visual DB browser
npx prisma db push         # Push schema changes
npx prisma generate        # Update Prisma client
npx prisma migrate dev     # Create migration
```

## 📊 Query Debugging
```typescript
// Enable query logging
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

// Debug specific queries
const users = await prisma.user.findMany();
console.log('🎯 Query result:', users);
```

## ⚡ Common Issues

### Schema Sync
```bash
# After schema changes
npx prisma generate
npm run dev # Restart server
```

### SQLite → PostgreSQL Migration
```typescript
// Update DATABASE_URL in .env
DATABASE_URL="postgresql://user:pass@localhost:5432/db"

// Reset and migrate
npx prisma migrate reset
npx prisma migrate dev
```

### Relation Issues
```typescript
// Include vs select
const userWithPosts = await prisma.user.findUnique({
  where: { id },
  include: { posts: true }, // vs select: { posts: true }
});
```

## 🐛 Debug Workflow
1. Check Prisma Studio for data
2. Verify schema.prisma syntax
3. Test queries in isolation
4. Check migration status
5. Validate environment variables

## 🚨 Red Flags
- "Client not initialized" errors
- Relation constraint failures
- Migration conflicts
- Connection pool exhaustion
