/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M11 10v4h4", key: "172dkj" }],
  ["path", { d: "m11 14 1.535-1.605a5 5 0 0 1 8 1.5", key: "vu0qm5" }],
  ["path", { d: "M16 2v4", key: "4m81vk" }],
  ["path", { d: "m21 18-1.535 1.605a5 5 0 0 1-8-1.5", key: "1qgeyt" }],
  ["path", { d: "M21 22v-4h-4", key: "hrummi" }],
  ["path", { d: "M21 8.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h4.3", key: "mctw84" }],
  ["path", { d: "M3 10h4", key: "1el30a" }],
  ["path", { d: "M8 2v4", key: "1cmpym" }]
];
const CalendarSync = createLucideIcon("calendar-sync", __iconNode);

export { __iconNode, CalendarSync as default };
//# sourceMappingURL=calendar-sync.js.map
