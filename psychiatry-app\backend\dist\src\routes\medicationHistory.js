"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const medicationHistoryController_1 = require("../controllers/medicationHistoryController");
const auth_1 = require("../middleware/auth");
const auth_2 = require("../middleware/auth");
const asyncHandler_1 = require("../utils/asyncHandler");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', (0, asyncHandler_1.asyncHandler)(medicationHistoryController_1.MedicationHistoryController.getAllMedicationHistory));
router.post('/', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(medicationHistoryController_1.MedicationHistoryController.createMedicationHistory));
router.get('/:id', (0, asyncHandler_1.asyncHandler)(medicationHistoryController_1.MedicationHistoryController.getMedicationHistoryById));
router.put('/:id', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(medicationHistoryController_1.MedicationHistoryController.updateMedicationHistory));
router.delete('/:id', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(medicationHistoryController_1.MedicationHistoryController.deleteMedicationHistory));
router.get('/patient/:patientId', (0, asyncHandler_1.asyncHandler)(medicationHistoryController_1.MedicationHistoryController.getMedicationHistoryByPatient));
router.get('/patient/:patientId/active', (0, asyncHandler_1.asyncHandler)(medicationHistoryController_1.MedicationHistoryController.getActiveMedications));
router.put('/:id/discontinue', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(medicationHistoryController_1.MedicationHistoryController.discontinueMedication));
exports.default = router;
//# sourceMappingURL=medicationHistory.js.map