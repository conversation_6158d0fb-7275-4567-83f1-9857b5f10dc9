# 🛠️ Backend Debug Guide (Node.js + Express + TypeScript)

## 🔧 Debug Mode
```bash
# Start with debugging
npm run dev -- --inspect
node --inspect-brk dist/server.js
```

## 📊 Logging Setup
```typescript
import debug from 'debug';
const log = debug('app:server');
log('🚀 Server started on port %d', PORT);
```

## ⚡ Common Issues

### Middleware Order
```typescript
// Correct order matters
app.use(cors());
app.use(express.json());
app.use('/api/auth', authRoutes);
app.use(authMiddleware); // After auth routes
app.use('/api', protectedRoutes);
```

### Async Error Handling
```typescript
// Always wrap async routes
app.get('/api/data', async (req, res, next) => {
  try {
    const data = await fetchData();
    res.json(data);
  } catch (error) {
    next(error); // Don't forget this!
  }
});
```

### TypeScript Compilation
```bash
# Watch mode for development
npx tsx watch src/server.ts
# Check compilation
tsc --noEmit
```

## 🐛 Debug Checklist
1. Check server logs for errors
2. Verify middleware execution order
3. Test API routes with Postman
4. Inspect database connections
5. Validate environment variables

## 🚨 Common Pain Points
- Unhandled promise rejections
- Middleware not executing
- CORS configuration errors
- TypeScript import/export issues
