/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",
      key: "1r0f0z"
    }
  ],
  ["path", { d: "M9 10h6", key: "9gxzsh" }]
];
const MapPinMinusInside = createLucideIcon("map-pin-minus-inside", __iconNode);

export { __iconNode, MapPinMinusInside as default };
//# sourceMappingURL=map-pin-minus-inside.js.map
