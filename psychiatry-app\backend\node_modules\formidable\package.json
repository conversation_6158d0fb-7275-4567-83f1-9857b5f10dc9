{"name": "formidable", "version": "3.5.4", "license": "MIT", "description": "A node.js module for parsing form data, especially file uploads.", "homepage": "https://github.com/node-formidable/formidable", "funding": "https://ko-fi.com/tunnckoCore/commissions", "repository": "node-formidable/formidable", "type": "module", "main": "./dist/index.cjs", "exports": {".": {"import": {"default": "./src/index.js"}, "require": {"default": "./dist/index.cjs"}, "default": "./dist/index.cjs"}, "./src/helpers/*.js": {"import": {"default": "./src/helpers/*.js"}, "require": {"default": "./dist/helpers/*.cjs"}}, "./src/parsers/*.js": {"import": {"default": "./src/parsers/*.js"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["src", "./dist", "./CHANGELOG", "./README.md", "./README_pt_BR.md"], "publishConfig": {"access": "public", "tag": "latest"}, "scripts": {"build-package": "rollup --config ./tool/rollup.config.js", "prepublishOnly": "pnpm run build-package", "bench": "node benchmark", "bench2prep": "node benchmark/server.js", "bench2": "bombardier --body-file=\"./README.md\" --method=POST --duration=10s --connections=100 http://localhost:3000/api/upload", "fmt": "pnpm run fmt:prepare '**/*'", "fmt:prepare": "prettier --write", "lint": "pnpm run lint:prepare .", "lint:prepare": "eslint --cache --fix --quiet --format codeframe", "fresh": "rm -rf ./node_modules", "test-specific": "node --disable-warning=ExperimentalWarning --experimental-vm-modules ./node_modules/jest/bin/jest.js --testPathPattern=test/standalone/keep-alive-error.test.js", "test-jest": "node --disable-warning=ExperimentalWarning --experimental-vm-modules ./node_modules/jest/bin/jest.js --testPathPattern=test/ --coverage", "test-node": "node --disable-warning=ExperimentalWarning --test ./test-node/**/*.test.js", "test-jest:ci": "node --experimental-vm-modules ./node_modules/jest/bin/jest.js --testPathPattern=test/ --coverage", "test:local": "pnpm run test-node && pnpm run test-jest", "audit": "pnpm audit --prod --fix", "pretest": "rm -rf ./test/tmp && mkdir ./test/tmp", "test": "pnpm run audit && node --test ./test-node/**/*.test.js && pnpm run test-jest:ci"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "dezalgo": "^1.0.4", "once": "^1.4.0"}, "packageManager": "pnpm@10.8.1", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.2", "@rollup/plugin-node-resolve": "^15.1.0", "@sindresorhus/slugify": "^2.1.0", "@tunnckocore/prettier-config": "1.3.8", "eslint": "6.8.0", "eslint-config-airbnb-base": "14.1.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-import": "2.20.2", "eslint-plugin-prettier": "3.1.3", "express": "^4.21.1", "formdata-polyfill": "^4.0.10", "jest": "27.2.4", "koa": "2.16.1", "nyc": "15.1.0", "prettier": "2.0.5", "prettier-plugin-pkgjson": "0.2.8", "rollup": "^3.25.3", "supertest": "6.1.6"}, "engines": {"node": ">=14.0.0"}, "jest": {"verbose": true}, "keywords": ["multipart", "form", "data", "querystring", "www", "json", "ulpoad", "file"]}