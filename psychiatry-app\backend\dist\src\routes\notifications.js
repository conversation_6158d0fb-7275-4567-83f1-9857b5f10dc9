"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const notificationController_1 = require("@/controllers/notificationController");
const auth_1 = require("@/middleware/auth");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/stats', notificationController_1.NotificationController.getNotificationStats);
router.post('/process-scheduled', (0, auth_1.authorize)(['ADMIN']), notificationController_1.NotificationController.processScheduledNotifications);
router.post('/appointment-reminders', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN', 'STAFF']), notificationController_1.NotificationController.createAppointmentReminders);
router.post('/lab-result', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN']), notificationController_1.NotificationController.sendLabResultNotification);
router.get('/', notificationController_1.NotificationController.getNotifications);
router.post('/', (0, auth_1.authorize)(['ADMIN', 'CLINICIAN', 'STAFF']), notificationController_1.NotificationController.createNotification);
router.put('/:id/read', notificationController_1.NotificationController.markAsRead);
exports.default = router;
//# sourceMappingURL=notifications.js.map