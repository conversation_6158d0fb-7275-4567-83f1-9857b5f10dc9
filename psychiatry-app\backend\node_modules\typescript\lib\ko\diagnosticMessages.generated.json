{"ALL_COMPILER_OPTIONS_6917": "모든 컴파일러 옵션", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "'{0}' 한정자는 가져오기 선언에서 사용할 수 없습니다.", "A_0_parameter_must_be_the_first_parameter_2680": "'{0}' 매개 변수는 첫 번째 매개 변수여야 합니다.", "A_JSDoc_template_tag_may_not_follow_a_typedef_callback_or_overload_tag_8039": "JSDoc '@template' 태그는 '@typedef', '@callback' 또는 '@overload' 태그 다음에 올 수 없습니다.", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "JSDoc '@typedef' 주석에 여러 '@type' 태그를 포함하지 못할 수 있습니다.", "A_bigint_literal_cannot_be_used_as_a_property_name_1539": "'bigint' 리터럴은 속성 이름으로 사용할 수 없습니다.", "A_bigint_literal_cannot_use_exponential_notation_1352": "bigint 리터럴에는 지수 표기법을 사용할 수 없습니다.", "A_bigint_literal_must_be_an_integer_1353": "bigint 리터럴은 정수여야 합니다.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "바인딩 패턴 매개 변수는 구현 서명에서 선택 사항이 될 수 없습니다.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "'break' 문은 이 문을 둘러싼 반복문 또는 switch 문 내에서만 사용할 수 있습니다.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "'break' 문은 이 문을 둘러싼 문의 레이블로만 이동할 수 있습니다.", "A_character_class_must_not_contain_a_reserved_double_punctuator_Did_you_mean_to_escape_it_with_backs_1522": "문자 클래스는 예약된 이중 문장 부호를 포함해서는 안됩니다. 백슬래시로 이스케이프하려고 하셨나요?", "A_character_class_range_must_not_be_bounded_by_another_character_class_1516": "문자 클래스 범위는 다른 문자 클래스에 의해 바인딩되어서는 안 됩니다.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "클래스는 선택적 형식 인수가 포함된 식별자/정규화된 이름만 구현할 수 있습니다.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "클래스는 개체 형식 또는 정적으로 알려진 멤버가 포함된 개체 형식의 교집합만 구현할 수 있습니다.", "A_class_cannot_extend_a_primitive_type_like_0_Classes_can_only_extend_constructable_values_2863": "클래스는 '{0}' 같은 기본 형식을 확장할 수 없습니다. 클래스는 생성 가능한 값만 확장할 수 있습니다.", "A_class_cannot_implement_a_primitive_type_like_0_It_can_only_implement_other_named_object_types_2864": "클래스는 '{0}' 같은 기본 형식을 구현할 수 없습니다. 다른 명명된 개체 형식만 구현할 수 있습니다.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "'default' 한정자를 사용하지 않는 클래스 선언에는 이름이 있어야 합니다.", "A_class_member_cannot_have_the_0_keyword_1248": "클래스 멤버에는 '{0}' 키워드를 사용할 수 없습니다.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "쉼표 식은 컴퓨팅된 속성 이름에 사용할 수 없습니다.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "계산된 속성 이름에서는 포함하는 형식의 형식 매개 변수를 참조할 수 없습니다.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "클래스 속성 선언의 계산된 속성 이름에는 간단한 리터럴 형식 또는 '고유 기호' 형식이 있어야 합니다.", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "메서드 오버로드의 계산된 속성 이름은 형식이 리터럴 형식이거나 'unique symbol' 형식인 식을 참조해야 합니다.", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "리터럴 형식의 계산된 속성 이름은 형식이 리터럴 형식이거나 'unique symbol' 형식인 식을 참조해야 합니다.", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "앰비언트 컨텍스트의 계산된 속성 이름은 형식이 리터럴 형식이거나 'unique symbol' 형식인 식을 참조해야 합니다.", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "인터페이스의 계산된 속성 이름은 형식이 리터럴 형식이거나 'unique symbol' 형식인 식을 참조해야 합니다.", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "계산된 속성 이름은 'string', 'number', 'symbol' 또는 'any' 형식이어야 합니다.", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "'const' 어설션은 열거형 멤버나 문자열, 숫자, 부울, 배열 또는 개체 리터럴에 대한 참조에만 적용할 수 있습니다.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "const 열거형 멤버는 문자열 리터럴을 통해서만 액세스할 수 있습니다.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "앰비언트 컨텍스트의 'const' 이니셜라이저는 문자열, 숫자 리터럴 또는 리터럴 열거형 참조여야 합니다.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "생성자는 해당 클래스가 'null'을 확장하는 경우 'super' 호출을 포함할 수 없습니다.", "A_constructor_cannot_have_a_this_parameter_2681": "생성자에는 'this' 매개 변수를 사용할 수 없습니다.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "'continue' 문은 이 문을 둘러싼 반복문 내에서만 사용할 수 있습니다.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "'continue' 문은 이 문을 둘러싼 반복문의 레이블로만 이동할 수 있습니다.", "A_declaration_file_cannot_be_imported_without_import_type_Did_you_mean_to_import_an_implementation_f_2846": "'import type'이 없으면 선언 파일을 가져올 수 없습니다. 대신 '{0}' 구현 파일을 가져오시겠습니까?", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "'declare' 한정자는 이미 존재하는 앰비언트 컨텍스트에서 사용할 수 없습니다.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "데코레이터는 오버로드가 아니라 메서드 구현만 데코레이팅할 수 있습니다.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "'default' 절은 'switch' 문에 두 번 이상 나올 수 없습니다.", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "기본 내보내기는 ECMAScript 스타일 모듈에서만 사용할 수 있습니다.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "기본 내보내기 수준은 파일 또는 모듈 선언의 최상위 수준에 있어야 합니다.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "이 컨텍스트에서는 한정된 할당 어설션 '!'가 허용되지 않습니다.", "A_destructuring_declaration_must_have_an_initializer_1182": "구조 파괴 선언에 이니셜라이저가 있어야 합니다.", "A_dynamic_import_call_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_for_t_2712": "ES5의 동적 가져오기 호출에 'Promise' 생성자가 필요합니다.  'Promise' 생성자에 대한 선언이 있거나 '--lib' 옵션에 'ES2015'가 포함되었는지 확인하세요.", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "동적 가져오기 호출은 'Promise'를 반환합니다. 'Promise'에 대한 선언이 있거나 '--lib' 옵션에 'ES2015'가 포함되었는지 확인하세요.", "A_file_cannot_have_a_reference_to_itself_1006": "파일은 자신에 대한 참조를 포함할 수 없습니다.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "'never'를 반환하는 함수에는 연결 가능한 끝점이 있을 수 없습니다.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "'new' 키워드로 호출한 함수에는 'void'인 'this' 형식을 사용할 수 없습니다.", "A_function_whose_declared_type_is_neither_undefined_void_nor_any_must_return_a_value_2355": "선언된 형식이 'undefined', 'void' 또는 'any'가 아닌 함수는 값을 반환해야 합니다.", "A_generator_cannot_have_a_void_type_annotation_2505": "생성기에는 'void' 형식 주석을 사용할 수 없습니다.", "A_get_accessor_cannot_have_parameters_1054": "'get' 접근자에는 매개 변수를 사용할 수 없습니다.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "get 접근자는 최소한 setter의 액세스 가능 수준과 같아야 합니다.", "A_get_accessor_must_return_a_value_2378": "'get' 접근자는 값을 반환해야 합니다.", "A_label_is_not_allowed_here_1344": "여기서는 레이블을 사용할 수 없습니다.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "레이블이 지정된 튜플 요소는 형식 뒤가 아니라 이름 뒤이면서 콜론 앞에 물음표를 사용하여 optional로 선언됩니다.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "레이블이 지정된 튜플 요소는 형식 앞이 아니라 이름 앞에 '...'을 사용하여 rest로 선언됩니다.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "매핑된 형식은 속성 또는 메서드를 선언할 수 없습니다.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "열거형 선언의 멤버 이니셜라이저는 그 뒤에 선언된 멤버와 다른 열거형에 정의된 멤버를 참조할 수 없습니다.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "mixin 클래스에는 'any[]' 형식의 rest 매개 변수 하나를 사용하는 생성자가 있어야 합니다.", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "추상 구문 시그니처를 포함하는 형식 변수에서 확장되는 mixin 클래스는 'abstract'로도 선언되어야 합니다.", "A_module_cannot_have_multiple_default_exports_2528": "모듈에는 기본 내보내기가 여러 개 있을 수 없습니다.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "네임스페이스 선언은 해당 선언이 병합된 클래스나 함수와 다른 파일에 있을 수 없습니다,", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "네임스페이스 선언은 해당 선언이 병합된 클래스나 함수 앞에 있을 수 없습니다.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "네임스페이스 선언은 네임스페이스 또는 모듈의 최상위 수준에서만 허용됩니다.", "A_namespace_declaration_should_not_be_declared_using_the_module_keyword_Please_use_the_namespace_key_1540": "'module' 키워드를 사용하여 'namespace' 선언을 선언하면 안 됩니다. 대신 'namespace' 키워드를 사용하세요.", "A_non_dry_build_would_build_project_0_6357": "-dry가 아닌 빌드는 프로젝트 '{0}'을(를) 빌드합니다.", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "-dry가 아닌 빌드는 다음 파일을 삭제합니다. {0}", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "DRY가 아닌 빌드는 '{0}' 프로젝트의 출력 타임스탬프를 업데이트합니다.", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "매개 변수 이니셜라이저는 함수 또는 생성자 구현에서만 허용됩니다.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "rest 매개 변수를 사용하여 매개 변수 속성을 선언할 수 없습니다.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "매개 변수 속성은 생성자 구현에서만 허용됩니다.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "바인딩 패턴을 사용하여 매개 변수 속성을 선언할 수 없습니다.", "A_promise_must_have_a_then_method_1059": "프라미스에는 'then' 메서드가 있어야 합니다.", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "형식이 'unique symbol' 형식인 클래스의 속성은 'static'과 'readonly' 둘 다여야 합니다.", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "형식이 'unique symbol' 형식인 인터페이스 또는 형식 리터럴의 속성은 'readonly'여야 합니다.", "A_required_element_cannot_follow_an_optional_element_1257": "필수 요소는 선택적 요소 뒤에 올 수 없습니다.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "필수 매개 변수는 선택적 매개 변수 뒤에 올 수 없습니다.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "rest 요소에는 바인딩 패턴이 포함될 수 없습니다.", "A_rest_element_cannot_follow_another_rest_element_1265": "rest 요소는 다른 rest 요소 뒤에 올 수 없습니다.", "A_rest_element_cannot_have_a_property_name_2566": "rest 요소에는 속성 이름을 사용할 수 없습니다.", "A_rest_element_cannot_have_an_initializer_1186": "rest 요소에는 이니셜라이저를 사용할 수 없습니다.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "rest 요소는 배열 구조 파괴 패턴의 마지막 요소여야 합니다.", "A_rest_element_type_must_be_an_array_type_2574": "rest 요소 형식은 배열 형식이어야 합니다.", "A_rest_parameter_cannot_be_optional_1047": "rest 매개 변수는 선택 사항이 될 수 없습니다.", "A_rest_parameter_cannot_have_an_initializer_1048": "rest 매개 변수에는 이니셜라이저를 사용할 수 없습니다.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "rest 매개 변수는 매개 변수 목록 마지막에 있어야 합니다.", "A_rest_parameter_must_be_of_an_array_type_2370": "rest 매개 변수는 배열 형식이어야 합니다.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "rest 매개 변수 또는 바인딩 패턴에 후행 쉼표가 없을 수 있습니다.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "'return' 문은 함수 본문 내에서만 사용할 수 있습니다.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "'return' 문은 클래스 정적 블록 내에서 사용할 수 없습니다.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "가져오기를 'baseUrl'에 상대적인 조회 위치로 다시 매핑하는 일련의 항목입니다.", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "'set' 접근자에는 반환 형식 주석을 사용할 수 없습니다.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "'set' 접근자에는 선택적 매개 변수를 사용할 수 없습니다.", "A_set_accessor_cannot_have_rest_parameter_1053": "'set' 접근자에는 rest 매개 변수를 사용할 수 없습니다.", "A_set_accessor_must_have_exactly_one_parameter_1049": "'set' 접근자에는 매개 변수를 하나만 사용해야 합니다.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "'set' 접근자 매개 변수에는 이니셜라이저를 사용할 수 없습니다.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "확산 인수는 튜플 유형을 가지거나 나머지 매개 변수로 전달되어야 합니다.", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "'super' 호출은 초기화된 속성, 매개 변수 속성 또는 개인 식별자를 포함하는 파생 클래스의 생성자 내에서 루트 수준 문이어야 합니다.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "파생 클래스에 초기화된 속성, 매개 변수 속성 또는 개인 식별자가 포함된 경우 'super' 호출은 '수퍼' 또는 'this'를 참조하는 생성자의 첫 번째 명령문이어야 합니다.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "'this' 기반 형식 가드는 매개 변수 기반 형식 가드와 호환되지 않습니다.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "'this' 형식은 클래스 또는 인터페이스의 비정적 멤버에서만 사용할 수 있습니다.", "A_top_level_export_modifier_cannot_be_used_on_value_declarations_in_a_CommonJS_module_when_verbatimM_1287": "'verbatimModuleSyntax'를 사용하도록 설정한 경우 CommonJS 모듈의 값 선언에 최상위 'export' 한정자를 사용할 수 없습니다.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "'tsconfig.json' 파일이 이미 '{0}'에 정의되어 있습니다.", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "튜플 멤버는 optional이면서 rest일 수 없습니다.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "튜플 형식은 음수 값으로 인덱싱할 수 없습니다.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "지수 식의 왼쪽에는 type assertion expression을 사용할 수 없습니다. 식을 괄호로 묶는 것이 좋습니다.", "A_type_literal_property_cannot_have_an_initializer_1247": "형식 리터럴 속성에는 이니셜라이저를 사용할 수 없습니다.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "형식 전용 가져오기는 기본 가져오기 또는 명명된 바인딩을 지정할 수 있지만, 둘 다 지정할 수는 없습니다.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "형식 조건자는 rest 매개 변수를 참조할 수 없습니다.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "형식 조건자는 바인딩 패턴에서 '{0}' 요소를 참조할 수 없습니다.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "형식 조건자는 함수 및 메서드의 반환 형식 위치에서만 사용할 수 있습니다.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "형식 조건자의 형식을 해당 매개 변수의 형식에 할당할 수 있어야 합니다.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "데코레이팅된 서명에서 참조하는 유형은 'isolatedModules' 및 'emitDecoratorMetadata'가 활성화된 경우 '가져오기 유형' 또는 네임스페이스 가져오기를 사용하여 가져와야 합니다.", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "형식이 'unique symbol' 형식인 변수는 'const'여야 합니다.", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "'yield' 식은 생성기 본문에서만 사용할 수 있습니다.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "super 식을 통해 '{1}' 클래스의 추상 메서드 '{0}'에 액세스할 수 없습니다.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "추상 메서드는 추상 클래스 내에서만 사용할 수 있습니다.", "Abstract_properties_can_only_appear_within_an_abstract_class_1253": "추상 속성은 추상 클래스 내에만 나타날 수 있습니다.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "생성자에서 '{1}' 클래스의 추상 속성 '{0}'에 액세스할 수 없습니다.", "Accessibility_modifier_already_seen_1028": "액세스 가능성 한정자가 이미 있습니다.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "접근자는 ECMAScript 5 이상을 대상으로 지정할 때만 사용할 수 있습니다.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "접근자는 모두 추상이거나 비추상이어야 합니다.", "Add_0_to_unresolved_variable_90008": "확인되지 않은 변수에 '{0}.' 추가", "Add_a_return_statement_95111": "return 문 추가", "Add_a_return_type_to_the_function_declaration_9031": "함수 선언에 반환 형식을 추가합니다.", "Add_a_return_type_to_the_function_expression_9030": "함수 식에 반환 형식을 추가합니다.", "Add_a_return_type_to_the_get_accessor_declaration_9032": "get 접근자 선언에 반환 형식을 추가합니다.", "Add_a_return_type_to_the_method_9034": "메서드에 반환 형식 추가", "Add_a_type_annotation_to_the_parameter_0_9028": "{0} 매개 변수에 형식 주석을 추가합니다.", "Add_a_type_annotation_to_the_property_0_9029": "{0} 속성에 형식 주석을 추가합니다.", "Add_a_type_annotation_to_the_variable_0_9027": "{0} 변수에 형식 주석을 추가합니다.", "Add_a_type_to_parameter_of_the_set_accessor_declaration_9033": "set 접근자 선언의 매개 변수에 형식을 추가합니다.", "Add_all_missing_async_modifiers_95041": "누락된 모든 'async' 한정자 추가", "Add_all_missing_attributes_95168": "누락된 특성 모두 추가", "Add_all_missing_call_parentheses_95068": "누락된 호출 괄호 모두 추가", "Add_all_missing_function_declarations_95157": "누락된 함수 선언 모두 추가", "Add_all_missing_imports_95064": "누락된 모든 가져오기 추가", "Add_all_missing_members_95022": "누락된 모든 멤버 추가", "Add_all_missing_override_modifiers_95162": "누락된 모든 'override' 한정자 추가", "Add_all_missing_parameters_95190": "누락된 매개 변수 모두 추가", "Add_all_missing_properties_95166": "누락된 모든 속성 추가", "Add_all_missing_return_statement_95114": "누락된 모든 return 문 추가", "Add_all_missing_super_calls_95039": "누락된 모든 super 호출 추가", "Add_all_missing_type_annotations_90067": "누락된 모든 형식 주석 추가", "Add_all_optional_parameters_95193": "모든 선택적 매개 변수 추가", "Add_annotation_of_type_0_90062": "'{0}' 형식의 주석 추가", "Add_async_modifier_to_containing_function_90029": "포함된 함수에 async 한정자 추가", "Add_await_95083": "'await' 추가", "Add_await_to_initializer_for_0_95084": "'{0}'의 이니셜라이저에 'await' 추가", "Add_await_to_initializers_95089": "이니셜라이저에 'await' 추가", "Add_braces_to_arrow_function_95059": "화살표 함수에 중괄호 추가", "Add_const_to_all_unresolved_variables_95082": "확인되지 않은 모든 변수에 'const' 추가", "Add_const_to_unresolved_variable_95081": "확인되지 않은 변수에 'const' 추가", "Add_definite_assignment_assertion_to_property_0_95020": "'{0}' 속성에 한정된 할당 어설션 추가", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "초기화되지 않은 모든 속성에 한정된 할당 어설션 추가", "Add_export_to_make_this_file_into_a_module_95097": "'export {}'를 추가하여 이 파일을 모듈로 만들기", "Add_extends_constraint_2211": "`extends` 제약 조건을 추가합니다.", "Add_extends_constraint_to_all_type_parameters_2212": "모든 형식 매개 변수에 `extends` 제약 조건 추가", "Add_import_from_0_90057": "\"{0}\"에서 가져오기 추가", "Add_index_signature_for_property_0_90017": "'{0}' 속성에 대해 인덱스 시그니처 추가", "Add_initializer_to_property_0_95019": "'{0}' 속성에 이니셜라이저 추가", "Add_initializers_to_all_uninitialized_properties_95027": "초기화되지 않은 모든 속성에 이니셜라이저 추가", "Add_missing_attributes_95167": "누락된 특성 추가", "Add_missing_call_parentheses_95067": "누락된 호출 괄호 추가", "Add_missing_comma_for_object_member_completion_0_95187": "개체 멤버 완료 '{0}'에 대한 누락된 쉼표 추가", "Add_missing_enum_member_0_95063": "누락된 열거형 멤버 '{0}' 추가", "Add_missing_function_declaration_0_95156": "누락된 함수 선언 '{0}' 추가", "Add_missing_new_operator_to_all_calls_95072": "모든 호출에 누락된 'new' 연산자 추가", "Add_missing_new_operator_to_call_95071": "호출에 누락된 'new' 연산자 추가", "Add_missing_parameter_to_0_95188": "'{0}'에 누락된 매개 변수 추가", "Add_missing_parameters_to_0_95189": "'{0}'에 누락된 매개 변수 추가", "Add_missing_properties_95165": "누락된 속성 추가", "Add_missing_super_call_90001": "누락된 'super()' 호출 추가", "Add_missing_typeof_95052": "누락된 'typeof' 추가", "Add_names_to_all_parameters_without_names_95073": "이름이 없는 모든 매개 변수에 이름 추가", "Add_optional_parameter_to_0_95191": "'{0}'에 선택적 매개 변수 추가", "Add_optional_parameters_to_0_95192": "'{0}'에 선택적 매개 변수 추가", "Add_or_remove_braces_in_an_arrow_function_95058": "화살표 함수에 중괄호 추가 또는 제거", "Add_override_modifier_95160": "'override' 한정자 추가", "Add_parameter_name_90034": "매개 변수 이름 추가", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "멤버 이름과 일치하는 모든 확인되지 않은 변수에 한정자 추가", "Add_resolution_mode_import_attribute_95196": "'resolution-mode' 가져오기 특성 추가", "Add_resolution_mode_import_attribute_to_all_type_only_imports_that_need_it_95197": "필요한 모든 형식 전용 가져오기에 'resolution-mode' 가져오기 특성을 추가합니다.", "Add_return_type_0_90063": "반환 형식 '{0}' 추가", "Add_satisfies_and_a_type_assertion_to_this_expression_satisfies_T_as_T_to_make_the_type_explicit_9035": "이 식에 충족 및 형식 어설션을 추가하여(T를 T로 충족) 형식을 명시적으로 만듭니다.", "Add_satisfies_and_an_inline_type_assertion_with_0_90068": "'{0}'을(를) 사용하여 충족 및 인라인 형식 어설션 추가", "Add_to_all_uncalled_decorators_95044": "호출되지 않는 모든 데코레이터에 '()' 추가", "Add_ts_ignore_to_all_error_messages_95042": "모든 오류 메시지에 '@ts-ignore' 추가", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "인덱스를 사용하여 액세스할 때 유형에 'undefined'를 추가합니다.", "Add_undefined_to_optional_property_type_95169": "선택적 속성 유형에 'undefined' 추가", "Add_undefined_type_to_all_uninitialized_properties_95029": "초기화되지 않은 모든 속성에 정의되지 않은 형식 추가", "Add_undefined_type_to_property_0_95018": "'{0}' 속성에 '정의되지 않은' 형식 추가", "Add_unknown_conversion_for_non_overlapping_types_95069": "겹치지 않는 형식에 대해 'unknown' 변환 추가", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "겹치지 않는 형식의 모든 변환에 'unknown' 추가", "Add_void_to_Promise_resolved_without_a_value_95143": "값 없이 확인된 Promise에 'void' 추가", "Add_void_to_all_Promises_resolved_without_a_value_95144": "값 없이 확인된 모든 Promise에 'void' 추가", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "tsconfig.json 파일을 추가하면 TypeScript 파일과 JavaScript 파일이 둘 다 포함된 프로젝트를 정리하는 데 도움이 됩니다. 자세한 내용은 https://aka.ms/tsconfig를 참조하세요.", "All_declarations_of_0_must_have_identical_constraints_2838": "'{0}'의 모든 선언에는 동일한 제약 조건이 있어야 합니다.", "All_declarations_of_0_must_have_identical_modifiers_2687": "'{0}'의 모든 선언에는 동일한 한정자가 있어야 합니다.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "'{0}'의 모든 선언에는 동일한 형식 매개 변수가 있어야 합니다.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "추상 메서드의 모든 선언은 연속적이어야 합니다.", "All_destructured_elements_are_unused_6198": "구조 파괴된 요소가 모두 사용되지 않습니다.", "All_imports_in_import_declaration_are_unused_6192": "가져오기 선언의 모든 가져오기가 사용되지 않습니다.", "All_type_parameters_are_unused_6205": "모든 형식 매개 변수가 사용되지 않습니다.", "All_variables_are_unused_6199": "모든 변수가 사용되지 않습니다.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "JavaScript 파일이 프로그램의 일부가 되도록 허용합니다. 이러한 파일에서 오류를 가져오려면 'checkJS' 옵션을 사용하세요.", "Allow_accessing_UMD_globals_from_modules_6602": "모듈에서 UMD 전역에 대한 액세스를 허용합니다.", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "기본 내보내기가 없는 모듈에서 기본 가져오기를 허용합니다. 여기서는 코드 내보내기에는 영향을 주지 않고 형식 검사만 합니다.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "모듈에 기본 내보내기가 없을 때 'y에서 x 가져오기'를 허용합니다.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "tslib에서 도우미 함수를 파일별로 포함하는 대신 프로젝트당 한 번씩 가져오도록 허용합니다.", "Allow_imports_to_include_TypeScript_file_extensions_Requires_moduleResolution_bundler_and_either_noE_6407": "가져오기에서 TypeScript 파일 확장자를 포함하도록 허용합니다. '--moduleResolution bundler'와 '--noEmit' 또는 '--emitDeclarationOnly'를 설정해야 합니다.", "Allow_javascript_files_to_be_compiled_6102": "Javascript 파일을 컴파일하도록 허용합니다.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "모듈을 확인할 때 여러 폴더가 하나로 처리되도록 허용합니다.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "이미 포함된 '{0}' 파일 이름은 '{1}' 파일 이름과 대/소문자만 다릅니다.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "앰비언트 모듈 선언은 상대적 모듈 이름을 지정할 수 없습니다.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "앰비언트 모듈은 다른 모듈 또는 네임스페이스에 중첩될 수 없습니다.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "AMD 모듈에는 여러 이름이 할당될 수 없습니다.", "An_abstract_accessor_cannot_have_an_implementation_1318": "추상 접근자는 구현이 있을 수 없습니다.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "접근성 한정자는 프라이빗 식별자와 함께 사용할 수 없습니다.", "An_accessor_cannot_have_type_parameters_1094": "접근자에는 형식 매개 변수를 사용할 수 없습니다.", "An_accessor_property_cannot_be_declared_optional_1276": "'accessor' 속성은 선택 사항으로 선언할 수 없습니다.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "앰비언트 모듈 선언은 파일의 최상위에서만 사용할 수 있습니다.", "An_argument_for_0_was_not_provided_6210": "'{0}'의 인수가 제공되지 않았습니다.", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "이 바인딩 패턴과 일치하는 인수가 제공되지 않았습니다.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "산술 피연산자는 'any', 'number', 'bigint' 또는 열거형 형식이어야 합니다.", "An_arrow_function_cannot_have_a_this_parameter_2730": "화살표 함수에는 'this' 매개 변수를 사용할 수 없습니다.", "An_async_function_or_method_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_2705": "ES5의 비동기 함수 또는 메서드에 'Promise' 생성자가 필요합니다.  'Promise' 생성자에 대한 선언이 있거나 '--lib' 옵션에 'ES2015'가 포함되었는지 확인하세요.", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "비동기 함수 또는 메서드는 'Promise'를 반환해야 합니다. 'Promise'에 대한 선언이 있거나 '--lib' 옵션에 'ES2015'가 포함되었는지 확인하세요.", "An_async_iterator_must_have_a_next_method_2519": "비동기 반복기에는 'next()' 메서드가 있어야 합니다.", "An_element_access_expression_should_take_an_argument_1011": "요소 액세스 식은 인수를 사용해야 합니다.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "프라이빗 식별자를 사용하여 열거형 멤버 이름을 지정할 수 없습니다.", "An_enum_member_cannot_have_a_numeric_name_2452": "열거형 멤버는 숫자 이름을 가질 수 없습니다.", "An_enum_member_name_must_be_followed_by_a_or_1357": "열거형 멤버 이름 뒤에는 ',', '=' 또는 '}'가 와야 합니다.", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "가능한 모든 컴파일러 옵션을 보여 주는 이 정보의 확장된 버전", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "내보내기 할당은 다른 내보낸 요소가 있는 모듈에서 사용될 수 없습니다.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "내보내기 할당은 네임스페이스에서 사용될 수 없습니다.", "An_export_assignment_cannot_have_modifiers_1120": "내보내기 할당에는 한정자를 사용할 수 없습니다.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "내보내기 할당은 파일 또는 모듈 선언의 최상위 수준에 있어야 합니다.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "내보내기 선언은 모듈의 최상위 수준에서만 사용할 수 있습니다.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "내보내기 선언은 네임스페이스 또는 모듈의 최상위 수준에서만 사용할 수 있습니다.", "An_export_declaration_cannot_have_modifiers_1193": "내보내기 선언에는 한정자를 사용할 수 없습니다.", "An_export_declaration_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolve_1283": "'verbatimModuleSyntax'를 사용하는 경우 'export =' 선언은 실제 값을 참조해야 하지만 '{0}'은(는) 형식 전용 선언으로 확인됩니다.", "An_export_declaration_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers__1282": "'verbatimModuleSyntax'를 사용하는 경우 'export =' 선언은 값을 참조해야 하지만 '{0}'은(는) 형식만 참조합니다.", "An_export_default_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolves_to_1285": "'verbatimModuleSyntax'가 사용하도록 설정되어 있지만 '{0}'이(가) 형식 전용 선언으로 확인되는 경우 'export default'는 실제 값을 참조해야 합니다.", "An_export_default_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers_to_a_1284": "'verbatimModuleSyntax'가 사용하도록 설정된 경우 'export default'는 값을 참조해야 하지만 '{0}'은(는) 형식만 참조합니다.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "'void' 형식 식의 truthiness를 테스트할 수 없습니다.", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "확장된 유니코드 이스케이프 값은 0x0과 0x10FFFF(포함) 사이여야 합니다.", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "식별자 또는 키워드는 숫자 리터럴 바로 뒤에 올 수 없습니다.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "앰비언트 컨텍스트에서는 구현을 선언할 수 없습니다.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "가져오기 별칭은 '내보내기 형식'을 사용하여 내보낸 선언을 참조할 수 없습니다.", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "가져오기 별칭은 '가져오기 형식'을 사용하여 가져온 선언을 참조할 수 없습니다.", "An_import_alias_cannot_resolve_to_a_type_or_type_only_declaration_when_verbatimModuleSyntax_is_enabl_1288": "'verbatimModuleSyntax'를 사용하도록 설정한 경우 가져오기 별칭을 형식 또는 형식 전용 선언으로 확인할 수 없습니다.", "An_import_alias_cannot_use_import_type_1392": "가져오기 별칭은 'import type'을 사용할 수 없습니다.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "가져오기 선언은 모듈의 최상위 수준에서만 사용할 수 있습니다.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "가져오기 선언은 네임스페이스 또는 모듈의 최상위 수준에서만 사용할 수 있습니다.", "An_import_declaration_cannot_have_modifiers_1191": "가져오기 선언에는 한정자를 사용할 수 없습니다.", "An_import_path_can_only_end_with_a_0_extension_when_allowImportingTsExtensions_is_enabled_5097": "가져오기 경로는 'allowImportingTsExtensions'가 사용하도록 설정된 경우에만 '{0}' 확장으로 끝날 수 있습니다.", "An_index_signature_cannot_have_a_rest_parameter_1017": "인덱스 시그니처에는 rest 매개 변수를 사용할 수 없습니다.", "An_index_signature_cannot_have_a_trailing_comma_1025": "인덱스 시그니처에는 후행 쉼표를 사용할 수 없습니다.", "An_index_signature_must_have_a_type_annotation_1021": "인덱스 시그니처에는 형식 주석을 사용할 수 없습니다.", "An_index_signature_must_have_exactly_one_parameter_1096": "인덱스 시그니처에는 한 개의 매개 변수만 사용할 수 있습니다.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "인덱스 시그니처 매개 변수에는 물음표를 사용할 수 없습니다.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "인덱스 시그니처 매개 변수에는 액세스 가능성 한정자를 사용할 수 없습니다.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "인덱스 시그니처 매개 변수에는 이니셜라이저를 사용할 수 없습니다.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "인덱스 시그니처 매개 변수에는 형식 주석을 사용할 수 없습니다.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "인덱스 시그니처 매개 변수 형식은 리터럴 유형이나 제네릭 형식일 수 없습니다. 대신 매핑된 개체 형식을 사용하세요.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "인덱스 시그니처 매개 변수 형식은 'string', 'number', 'symbol' 또는 템플릿 리터럴 형식이어야 합니다.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "인스턴스화 식 뒤에 속성 액세스가 있을 수 없습니다.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "인터페이스는 선택적 형식 인수가 포함된 식별자/정규화된 이름만 확장할 수 있습니다.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "인터페이스는 개체 형식 또는 정적으로 알려진 멤버가 포함된 개체 형식의 교집합만 확장할 수 있습니다.", "An_interface_cannot_extend_a_primitive_type_like_0_It_can_only_extend_other_named_object_types_2840": "인터페이스는 '{0}' 같은 기본 형식을 확장할 수 없습니다. 다른 명명된 개체 형식만 확장할 수 있습니다.", "An_interface_property_cannot_have_an_initializer_1246": "인터페이스 속성에는 이니셜라이저를 사용할 수 없습니다.", "An_iterator_must_have_a_next_method_2489": "반복기에는 'next()' 메서드가 있어야 합니다.", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "@jsx pragma를 JSX 조각과 함께 사용하는 경우에는 @jsxFrag pragma가 필요합니다.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "개체 리터럴에 이름이 같은 여러 개의 get/set 접근자를 사용할 수 없습니다.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "개체 리터럴은 이름이 같은 여러 속성을 가질 수 없습니다.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "개체 리터럴에 이름이 같은 속성과 접근자를 사용할 수 없습니다.", "An_object_member_cannot_be_declared_optional_1162": "개체 멤버는 선택적으로 선언될 수 없습니다.", "An_object_s_Symbol_hasInstance_method_must_return_a_boolean_value_for_it_to_be_used_on_the_right_han_2861": "개체의 '[Symbol.hasInstance]' 메서드는 'instanceof' 식의 오른쪽에서 사용할 부울 값을 반환해야 합니다.", "An_optional_chain_cannot_contain_private_identifiers_18030": "선택적 체인에는 프라이빗 식별자를 사용할 수 없습니다.", "An_optional_element_cannot_follow_a_rest_element_1266": "선택적 요소는 rest 요소 뒤에 올 수 없습니다.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "'this'의 외부 값은 이 컨테이너에서 섀도 처리됩니다.", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "오버로드 시그니처는  생성기로 선언할 수 없습니다.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "지수 식의 왼쪽에는 '{0}' 연산자가 있는 단항 식을 사용할 수 없습니다. 식을 괄호로 묶는 것이 좋습니다.", "Annotate_everything_with_types_from_JSDoc_95043": "JSDoc의 형식을 사용하여 모든 항목에 주석 달기", "Annotate_types_of_properties_expando_function_in_a_namespace_90071": "네임스페이스의 속성 expando 함수 형식에 주석 달기", "Annotate_with_type_from_JSDoc_95009": "JSDoc의 형식을 사용하여 주석 추가", "Another_export_default_is_here_2753": "다른 내보내기 기본값은 여기에 있습니다.", "Any_Unicode_property_that_would_possibly_match_more_than_a_single_character_is_only_available_when_t_1528": "단일 문자 이상과 일치할 수 있는 유니코드 속성은 유니코드 집합(v) 플래그가 설정된 경우에만 사용할 수 있습니다.", "Anything_that_would_possibly_match_more_than_a_single_character_is_invalid_inside_a_negated_characte_1518": "단일 문자보다 더 일치할 수 있는 모든 항목은 부정된 문자 클래스 내에서 유효하지 않습니다.", "Are_you_missing_a_semicolon_2734": "세미콜론이 없습니까?", "Argument_expression_expected_1135": "인수 식이 필요합니다.", "Argument_for_0_option_must_be_Colon_1_6046": "'{0}' 옵션의 인수는 {1}이어야(여야) 합니다.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "동적 가져오기의 인수는 spread 요소일 수 없습니다.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "'{0}' 형식의 인수는 '{1}' 형식의 매개 변수에 할당될 수 없습니다.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "'{0}' 유형의 인수는 'exactOptionalPropertyTypes: true'가 있는 '{1}' 유형의 매개 변수에 할당할 수 없습니다. 대상 속성의 유형에 'undefined'를 추가하는 것을 고려하세요.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "REST 매개 변수 '{0}'에 대한 인수가 제공되지 않았습니다.", "Array_element_destructuring_pattern_expected_1181": "배열 요소 구조 파괴 패턴이 필요합니다.", "Arrays_with_spread_elements_can_t_inferred_with_isolatedDeclarations_9018": "분산 요소가 있는 배열은 --isolatedDeclarations로 유추할 수 없습니다.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "어설션에서 호출 대상의 모든 이름은 명시적 형식 주석을 사용하여 선언해야 합니다.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "어설션에서 호출 대상은 식별자 또는 정규화된 이름이어야 합니다.", "Assigning_properties_to_functions_without_declaring_them_is_not_supported_with_isolatedDeclarations__9023": "함수를 선언하지 않고 함수에 속성을 할당하는 것은 --isolatedDeclarations에서 지원되지 않습니다. 이 함수에 할당된 속성에 대한 명시적 선언을 추가합니다.", "Asterisk_Slash_expected_1010": "'*/'가 필요합니다.", "At_least_one_accessor_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9009": "하나 이상의 접근자에 --isolatedDeclarations를 사용하는 명시적 형식 주석이 있어야 합니다.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "전역 범위에 대한 확대는 외부 모듈 또는 앰비언트 모듈 선언에만 직접 중첩될 수 있습니다.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "전역 범위에 대한 확대는 이미 존재하는 앰비언트 컨텍스트에 표시되지 않는 한 'declare' 한정자를 포함해야 합니다.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "프로젝트 '{0}'에서 입력에 대한 자동 검색을 사용하도록 설정되었습니다. '{2}' 캐시 위치를 사용하여 모듈 '{1}'에 대해 추가 해결 패스를 실행합니다.", "BUILD_OPTIONS_6919": "빌드 옵션", "Backwards_Compatibility_6253": "이전 버전과의 호환성", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "기본 클래스 식에서 클래스 형식 매개 변수를 참조할 수 없습니다.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "기본 생성자 반환 형식 '{0}'은(는) 개체 형식 또는 정적으로 알려진 멤버가 포함된 개체 형식의 교집합이 아닙니다.", "Base_constructors_must_all_have_the_same_return_type_2510": "기본 생성자는 모두 반환 형식이 같아야 합니다.", "Base_directory_to_resolve_non_absolute_module_names_6083": "비추상 모듈 이름을 확인할 기본 디렉터리입니다.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "ES2020 미만을 대상으로 할 경우 bigint 리터럴을 사용할 수 없습니다.", "Binary_digit_expected_1177": "이진수가 있어야 합니다.", "Binding_element_0_implicitly_has_an_1_type_7031": "바인딩 요소 '{0}'에 암시적으로 '{1}' 형식이 있습니다.", "Binding_elements_can_t_be_exported_directly_with_isolatedDeclarations_9019": "바인딩 요소는 --isolatedDeclarations를 사용하여 직접 내보낼 수 없습니다.", "Block_scoped_variable_0_used_before_its_declaration_2448": "선언 전에 사용된 블록 범위 변수 '{0}'입니다.", "Build_a_composite_project_in_the_working_directory_6925": "작업 디렉터리에서 복합 프로젝트를 빌드합니다.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "최신으로 보이는 프로젝트를 포함하여 모든 프로젝트를 빌드합니다.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "최신 상태가 아닌 경우, 하나 이상의 프로젝트 및 해당 종속성 빌드", "Build_option_0_requires_a_value_of_type_1_5073": "빌드 옵션 '{0}'에 {1} 형식의 값이 필요합니다.", "Building_project_0_6358": "'{0}' 프로젝트를 빌드하는 중...", "Built_in_iterators_are_instantiated_with_a_TReturn_type_of_undefined_instead_of_any_6720": "기본 제공 반복기는 'any' 대신 'undefined'의 'TReturn' 형식으로 인스턴스화됩니다.", "COMMAND_LINE_FLAGS_6921": "명령줄 플래그", "COMMON_COMMANDS_6916": "일반 명령", "COMMON_COMPILER_OPTIONS_6920": "일반 컴파일러 옵션", "Call_decorator_expression_90028": "데코레이터 식 호출", "Call_signature_return_types_0_and_1_are_incompatible_2202": "호출 시그니처 반환 형식 '{0}' 및 '{1}'이(가) 호환되지 않습니다.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "반환 형식 주석이 없는 호출 시그니처에는 암시적으로 'any' 반환 형식이 포함됩니다.", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "인수가 없는 호출 시그니처의 반환 형식 '{0}' 및 '{1}'이(가) 호환되지 않습니다.", "Can_only_convert_logical_AND_access_chains_95142": "논리적 AND 액세스 체인만 변환할 수 있습니다.", "Can_only_convert_named_export_95164": "명명된 내보내기만 변환할 수 있습니다.", "Can_only_convert_property_with_modifier_95137": "한정자만 사용하여 속성을 변환할 수 있습니다.", "Can_only_convert_string_concatenations_and_string_literals_95154": "문자열 연결 및 문자열 리터럴만 변환할 수 있습니다.", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "'{0}'이(가) 네임스페이스가 아니라 형식이므로 '{0}.{1}'에 액세스할 수 없습니다. '{0}'에서 '{0}[\"{1}\"]'과(와) 함께 '{1}' 속성의 형식을 검색하려고 했나요?", "Cannot_access_0_from_another_file_without_qualification_when_1_is_enabled_Use_2_instead_1281": "'{1}'을(를) 사용하도록 설정한 경우 한정 없이 다른 파일에서 '{0}'에 액세스할 수 없습니다. 대신 '{2}'을(를) 사용하세요.", "Cannot_access_ambient_const_enums_when_0_is_enabled_2748": "'{0}'이(가) 사용하도록 설정된 경우 앰비언트 const 열거형에 액세스할 수 없습니다.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "'{0}' 생성자 형식을 '{1}' 생성자 형식에 할당할 수 없습니다.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "추상 생성자 형식을 비추상 생성자 형식에 할당할 수 없습니다.", "Cannot_assign_to_0_because_it_is_a_class_2629": "클래스이므로 '{0}'에 할당할 수 없습니다.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "상수이므로 '{0}'에 할당할 수 없습니다.", "Cannot_assign_to_0_because_it_is_a_function_2630": "함수이므로 '{0}'에 할당할 수 없습니다.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "네임스페이스이므로 '{0}'에 할당할 수 없습니다.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "읽기 전용 속성이므로 '{0}'에 할당할 수 없습니다.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "열거형이므로 '{0}'에 할당할 수 없습니다.", "Cannot_assign_to_0_because_it_is_an_import_2632": "가져오기이므로 '{0}'에 할당할 수 없습니다.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "변수가 아니므로 '{0}'에 할당할 수 없습니다.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "프라이빗 메서드 '{0}'에 할당할 수 없습니다. 프라이빗 메서드에는 쓸 수 없습니다.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "'{0}' 모듈은 모듈이 아닌 엔터티로 확인되므로 확대할 수 없습니다.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "모듈이 아닌 엔터티로 확인되기 때문에 값 내보내기로 모듈 '{0}'을(를) 확대할 수 없습니다.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "'--module' 플래그가 'amd' 또는 'system'이 아닌 경우 '{0}' 옵션을 사용하여 모듈을 컴파일할 수 없습니다.", "Cannot_create_an_instance_of_an_abstract_class_2511": "추상 클래스의 인스턴스를 만들 수 없습니다.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "반복기의 'next' 메서드에 '{1}' 형식이 필요하지만 포함 생성기는 항상 '{0}'을(를) 전송하므로 값에 반복을 위임할 수 없습니다.", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "'{0}'을(를) 내보낼 수 없습니다. 지역 선언만 모듈에서 내보낼 수 있습니다.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "'{0}' 클래스를 확장할 수 없습니다. 클래스 생성자가 private로 표시되어 있습니다.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "인터페이스 '{0}'을(를) 확장할 수 없습니다. 'implements'를 확장하시겠습니까?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "현재 디렉터리에서 tsconfig.json 파일을 찾을 수 없습니다. {0}.", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "지정된 디렉터리에서 tsconfig.json 파일을 찾을 수 없습니다. '{0}'.", "Cannot_find_global_type_0_2318": "전역 형식 '{0}'을(를) 찾을 수 없습니다.", "Cannot_find_global_value_0_2468": "전역 값 '{0}'을(를) 찾을 수 없습니다.", "Cannot_find_lib_definition_for_0_2726": "'{0}'에 대한 라이브러리 정의를 찾을 수 없습니다.", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "'{0}'에 대한 라이브러리 정의를 찾을 수 없습니다. '{1}'이(가) 아닌지 확인하세요.", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "'{0}' 모듈을 찾을 수 없습니다. '--resolveJsonModule'을 사용하여 '. json' 확장명이 포함된 모듈을 가져오는 것이 좋습니다.", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_nodenext_or_to_add_aliases_t_2792": "'{0}' 모듈을 찾을 수 없습니다. 'moduleResolution' 옵션을 'nodenext'로 설정하거나 'paths' 옵션에 별칭을 추가하려고 하셨나요?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "'{0}' 모듈 또는 해당 형식 선언을 찾을 수 없습니다.", "Cannot_find_name_0_2304": "'{0}' 이름을 찾을 수 없습니다.", "Cannot_find_name_0_Did_you_mean_1_2552": "'{0}' 이름을 찾을 수 없습니다. '{1}'을(를) 사용하시겠습니까?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "'{0}' 이름을 찾을 수 없습니다. 인스턴스 멤버 'this.{0}'을(를) 사용하시겠습니까?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "'{0}' 이름을 찾을 수 없습니다. 정적 멤버 '{1}.{0}'을(를) 사용하시겠습니까?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "이름 '{0}' 찾을 수 없습니다. 비동기 함수에 쓰려고 했나요?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "'{0}' 이름을 찾을 수 없습니다. 대상 라이브러리를 변경하려는 경우 'lib' 컴파일러 옵션을 '{1}' 이상으로 변경해 보세요.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "'{0}' 이름을 찾을 수 없습니다. 대상 라이브러리를 변경하려는 경우 'dom'을 포함하도록 'lib' 컴파일러 옵션을 변경해 보세요.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2867": "'{0}' 이름을 찾을 수 없습니다. Bun에 대한 형식 정의를 설치해야 하나요? 'npm i --save-dev @types/bun'을 사용해 보세요.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2868": "'{0}' 이름을 찾을 수 없습니다. Bun에 대한 형식 정의를 설치해야 하나요? 'npm i --save-dev @types/bun'을 시도한 다음 tsconfig의 형식 필드에 'bun'을 추가합니다.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "'{0}' 이름을 찾을 수 없습니다. 테스트 실행기의 형식 정의를 설치하려는 경우 'npm i --save-dev @types/jest' 또는 'npm i --save-dev @types/mocha'를 시도합니다.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "'{0}' 이름을 찾을 수 없습니다. 테스트 실행기의 형식 정의를 설치하려는 경우 'npm i --save-dev @types/jest' 또는 'npm i --save-dev @types/mocha'를 시도한 다음, tsconfig의 형식 필드에 'jest' 또는 'mocha'를 추가하세요.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "'{0}' 이름을 찾을 수 없습니다. jQuery의 형식 정의를 설치하려는 경우 'npm i --save-dev @types/jquery'를 시도합니다.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "'{0}' 이름을 찾을 수 없습니다. jQuery의 형식 정의를 설치하려는 경우 'npm i --save-dev @types/jquery'를 시도한 다음, tsconfig의 형식 필드에 'jquery'를 추가하세요.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "'{0}' 이름을 찾을 수 없습니다. 노드의 형식 정의를 설치하려는 경우 'npm i --save-dev @types/node'를 시도합니다.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "'{0}' 이름을 찾을 수 없습니다. 노드의 형식 정의를 설치하려는 경우 'npm i --save-dev @types/node'를 시도한 다음, tsconfig의 형식 필드에 'node'를 추가하세요.", "Cannot_find_namespace_0_2503": "'{0}' 네임스페이스를 찾을 수 없습니다.", "Cannot_find_namespace_0_Did_you_mean_1_2833": "네임스페이스 '{0}'을(를) 찾을 수 없습니다. '{1}'을(를) 의미했나요?", "Cannot_find_parameter_0_1225": "'{0}' 매개 변수를 찾을 수 없습니다.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "입력 파일의 공용 하위 디렉터리 경로를 찾을 수 없습니다.", "Cannot_find_type_definition_file_for_0_2688": "'{0}'에 대한 형식 정의 파일을 찾을 수 없습니다.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "형식 선언 파일을 가져올 수 없습니다. '{1}' 대신 '{0}'을(를) 가져오세요.", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "블록 범위 선언 '{1}'과(와) 동일한 범위 내에서 외부 범위 변수 '{0}'을(를) 초기화할 수 없습니다.", "Cannot_invoke_an_object_which_is_possibly_null_2721": "'null'일 수 있는 개체를 호출할 수 없습니다.", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "'null'이거나 '정의되지 않음'일 수 있는 개체를 호출할 수 없습니다.", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "'정의되지 않음'일 수 있는 개체를 호출할 수 없습니다.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "반복기의 'next' 메서드에 '{1}' 형식이 필요하지만 배열 구조 파괴는 항상 '{0}'을(를) 전송하므로 값을 반복할 수 없습니다.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "반복기의 'next' 메서드에 '{1}' 형식이 필요하지만 배열 spread는 항상 '{0}'을(를) 전송하므로 값을 반복할 수 없습니다.", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "반복기의 'next' 메서드에 '{1}' 형식이 필요하지만 for-of는 항상 '{0}'을(를) 전송하므로 값을 반복할 수 없습니다.", "Cannot_move_statements_to_the_selected_file_95183": "문을 선택한 파일로 이동할 수 없습니다.", "Cannot_move_to_file_selected_file_is_invalid_95179": "파일로 이동할 수 없습니다. 선택한 파일이 유효하지 않습니다.", "Cannot_read_file_0_5083": "'{0}' 파일을 읽을 수 없습니다.", "Cannot_read_file_0_Colon_1_5012": "파일 '{0}'을(를) 읽을 수 없습니다. {1}.", "Cannot_redeclare_block_scoped_variable_0_2451": "블록 범위 변수 '{0}'을(를) 다시 선언할 수 없습니다.", "Cannot_redeclare_exported_variable_0_2323": "내보낸 변수 '{0}'을(를) 다시 선언할 수 없습니다.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "catch 절에서 식별자 '{0}'을(를) 다시 선언할 수 없습니다.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "형식 주석에서 함수 호출을 시작할 수 없습니다.", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "'--jsx' 플래그를 제공하지 않으면 JSX를 사용할 수 없습니다.", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_0_is_enabled_1269": "'{0}'을(를) 사용하도록 설정한 경우 형식 또는 형식 전용 네임스페이스에서 'export import'를 사용할 수 없습니다.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "'--module'이 'none'인 경우 가져오기, 내보내기 또는 모듈 확대를 사용할 수 없습니다.", "Cannot_use_namespace_0_as_a_type_2709": "'{0}' 네임스페이스를 형식으로 사용할 수 없습니다.", "Cannot_use_namespace_0_as_a_value_2708": "'{0}' 네임스페이스를 값으로 사용할 수 없습니다.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "데코레이팅된 클래스의 정적 속성 이니셜라이저에서는 'this'를 사용할 수 없습니다.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "참조된 프로젝트 '{1}'에서 생성된 '.tsbuildinfo' 파일을 덮어쓰므로 '{0}' 파일을 쓸 수 없습니다.", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "'{0}' 파일은 여러 입력 파일로 덮어쓰이므로 쓸 수 없습니다.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "'{0}' 파일은 입력 파일을 덮어쓰므로 쓸 수 없습니다.", "Catch_clause_variable_cannot_have_an_initializer_1197": "Catch 절 변수에 이니셜라이저를 사용할 수 없습니다.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "지정한 경우 catch 절 변수 형식 주석은 'any' 또는 'unknown'이어야 합니다.", "Change_0_to_1_90014": "'{0}'을(를) '{1}'(으)로 변경", "Change_all_extended_interfaces_to_implements_95038": "확장된 모든 인터페이스를 'implements'로 변경", "Change_all_jsdoc_style_types_to_TypeScript_95030": "모든 jsdoc-style 형식을 TypeScript로 변경", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "모든 jsdoc-style 형식을 TypeScript로 변경(그리고 nullable 형식에 '| undefined' 추가)", "Change_extends_to_implements_90003": "'extends'를 'implements'로 변경", "Change_spelling_to_0_90022": "맞춤법을 '{0}'(으)로 변경", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "선언되었지만 생성자에 설정되지 않은 클래스 속성을 확인합니다.", "Check_side_effect_imports_6806": "부작용 가져오기를 확인합니다.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "'bind', 'call' 및 'apply' 메서드에 대한 인수가 원래 함수와 일치하는지 확인하세요.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "'{0}'이(가) '{1}' - '{2}'에 대해 일치하는 가장 긴 접두사인지 확인하는 중입니다.", "Circular_definition_of_import_alias_0_2303": "가져오기 별칭 '{0}'의 순환 정의입니다.", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "구성을 확인하는 동안 순환이 검색되었습니다. {0}", "Circularity_originates_in_type_at_this_location_2751": "순환이 이 위치의 형식에서 시작됩니다.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "'{0}' 클래스는 인스턴스 멤버 접근자 '{1}'을(를) 정의하지만 확장 클래스 '{2}'은(는) 이 접근자를 인스턴스 멤버 함수로 정의합니다.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "'{0}' 클래스가 인스턴스 멤버 함수 '{1}'을(를) 정의하지만 확장 클래스 '{2}'은(는) 이 함수를 인스턴스 멤버 접근자로 정의합니다.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "'{0}' 클래스가 인스턴스 멤버 속성 '{1}'을(를) 정의하지만 확장 클래스 '{2}'은(는) 이 속성을 인스턴스 멤버 함수로 정의합니다.", "Class_0_incorrectly_extends_base_class_1_2415": "'{0}' 클래스가 기본 클래스 '{1}'을(를) 잘못 확장합니다.", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "'{0}' 클래스가 '{1}' 클래스를 잘못 구현합니다. '{1}'을(를) 확장하고 이 클래스의 멤버를 하위 클래스로 상속하시겠습니까?", "Class_0_incorrectly_implements_interface_1_2420": "'{0}' 클래스가 '{1}' 인터페이스를 잘못 구현합니다.", "Class_0_used_before_its_declaration_2449": "선언 전에 사용된 '{0}' 클래스입니다.", "Class_constructor_may_not_be_a_generator_1368": "클래스 생성자는 생성기가 아닐 수 있습니다.", "Class_constructor_may_not_be_an_accessor_1341": "클래스 생성자는 접근자가 아닐 수 있습니다.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "클래스 선언에서 '{0}'에 대한 오버로드 목록을 구현할 수 없습니다.", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "클래스 선언은 '@augments' 또는 '@extends' 태그를 둘 이상 가질 수 없습니다.", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "클래스 데코레이터는 정적 프라이빗 식별자와 함께 사용할 수 없습니다. 실험적 데코레이터를 제거하는 것이 좋습니다.", "Class_field_0_defined_by_the_parent_class_is_not_accessible_in_the_child_class_via_super_2855": "부모 클래스에서 정의한 '{0}' 클래스 필드는 super를 통해 자식 클래스에서 액세스할 수 없습니다.", "Class_name_cannot_be_0_2414": "클래스 이름은 '{0}'일 수 없습니다.", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "{0} 모듈을 사용하는 ES5를 대상으로 하는 경우 클래스 이름은 'Object'일 수 없습니다.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "클래스 정적 측면 '{0}'이(가) 기본 클래스 정적 측면 '{1}'을(를) 잘못 확장합니다.", "Classes_can_only_extend_a_single_class_1174": "클래스는 단일 클래스만 확장할 수 있습니다.", "Classes_may_not_have_a_field_named_constructor_18006": "클래스에는 'constructor' 필드를 사용할 수 없습니다.", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "클래스에 포함된 코드는 '{0}'의 사용을 허용하지 않는 JavaScript의 strict 모드로 평가됩니다. 자세한 내용은 https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode를 참조하세요.", "Command_line_Options_6171": "명령줄 옵션", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "구성 파일에 대한 경로 또는 'tsconfig.json'이 포함된 폴더에 대한 경로를 고려하여 프로젝트를 컴파일합니다.", "Compiler_Diagnostics_6251": "컴파일러 진단", "Compiler_option_0_cannot_be_given_an_empty_string_18051": "'{0}' 컴파일러 옵션에는 빈 문자열을 지정할 수 없습니다.", "Compiler_option_0_expects_an_argument_6044": "컴파일러 옵션 '{0}'에는 인수가 필요합니다.", "Compiler_option_0_may_not_be_used_with_build_5094": "컴파일러 옵션 '--{0}'은(는) '-빌드'에서 사용되지 않을 수 있습니다.", "Compiler_option_0_may_only_be_used_with_build_5093": "컴파일러 옵션 '--{0}'은(는) '-빌드'에서만 사용할 수 있습니다.", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "값 '{1}'의 컴파일러 옵션 '{0}'이(가) 불안정합니다. 야간 TypeScript를 사용하여 이 오류를 차단하세요. 'npm install -D typescript@next'로 업데이트해 보세요.", "Compiler_option_0_requires_a_value_of_type_1_5024": "컴파일러 옵션 '{0}'에 {1} 형식의 값이 필요합니다.", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "컴파일러는 프라이빗 식별자 하위 수준을 내보낼 때 '{0}' 이름을 예약합니다.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "지정된 경로에 있는 TypeScript 프로젝트를 컴파일합니다.", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "현재 프로젝트(작업 디렉터리의 tsconfig.json)를 컴파일합니다.", "Compiles_the_current_project_with_additional_settings_6929": "추가 설정을 사용하여 현재 프로젝트를 컴파일합니다.", "Completeness_6257": "완성도", "Composite_projects_may_not_disable_declaration_emit_6304": "복합 프로젝트는 선언 내보내기를 비활성화할 수 없습니다.", "Composite_projects_may_not_disable_incremental_compilation_6379": "복합 프로젝트는 증분 컴파일을 비활성화할 수 없습니다.", "Computed_from_the_list_of_input_files_6911": "입력 파일 목록에서 컴퓨팅됨", "Computed_properties_must_be_number_or_string_literals_variables_or_dotted_expressions_with_isolatedD_9014": "계산된 속성은 --isolatedDeclarations가 있는 숫자 또는 문자열 리터럴, 변수 또는 점선 식이어야 합니다.", "Computed_property_names_are_not_allowed_in_enums_1164": "컴퓨팅된 속성 이름은 열거형에 사용할 수 없습니다.", "Computed_property_names_on_class_or_object_literals_cannot_be_inferred_with_isolatedDeclarations_9038": "클래스 또는 개체 리터럴의 계산된 속성 이름은 --isolatedDeclarations로 유추할 수 없습니다.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "계산된 값은 문자열 값 멤버가 포함된 열거형에서 허용되지 않습니다.", "Concatenate_and_emit_output_to_single_file_6001": "출력을 연결하고 단일 파일로 내보냅니다.", "Conditions_to_set_in_addition_to_the_resolver_specific_defaults_when_resolving_imports_6410": "가져오기를 확인할 때 확인자별 기본값 외에 설정할 조건입니다.", "Conflicts_are_in_this_file_6201": "이 파일에 충돌이 있습니다.", "Consider_adding_a_declare_modifier_to_this_class_6506": "이 클래스에 'declare' 한정자를 추가하는 것이 좋습니다.", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "구문 시그니처 반환 형식 '{0}' 및 '{1}'이(가) 호환되지 않습니다.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "반환 형식 주석이 없는 구문 시그니처에는 암시적으로 'any' 반환 형식이 포함됩니다.", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "인수가 없는 구문 시그니처의 반환 형식 '{0}' 및 '{1}'이(가) 호환되지 않습니다.", "Constructor_implementation_is_missing_2390": "생성자 구현이 없습니다.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "'{0}' 클래스의 생성자는 private이며 클래스 선언 내에서만 액세스할 수 있습니다.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "'{0}' 클래스의 생성자는 protected이며 클래스 선언 내에서만 액세스할 수 있습니다.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "공용 구조체 형식에 사용되는 경우 생성자 형식 표기법을 괄호로 묶어야 합니다.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "교집합 형식에 사용되는 경우 생성자 형식 표기법을 괄호로 묶어야 합니다.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "파생 클래스의 생성자는 'super' 호출을 포함해야 합니다.", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "포함 파일이 지정되지 않았고 루트 디렉터리를 확인할 수 없어 'node_modules' 폴더 조회를 건너뜁니다.", "Containing_function_is_not_an_arrow_function_95128": "포함 함수가 화살표 함수가 아닙니다.", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "모듈 형식의 JS 파일을 감지하는 데 사용되는 방법을 제어합니다.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "'{0}' 형식을 '{1}' 형식으로 변환한 작업은 실수일 수 있습니다. 두 형식이 서로 충분히 겹치지 않기 때문입니다. 의도적으로 변환한 경우에는 먼저 'unknown'으로 식을 변환합니다.", "Convert_0_to_1_in_0_95003": "'{0}'을(를) '{0}의 {1}'(으)로 변환", "Convert_0_to_mapped_object_type_95055": "'{0}'을(를) 매핑된 개체 형식으로 변환", "Convert_all_const_to_let_95102": "모든 'const'를 'let'으로 변환", "Convert_all_constructor_functions_to_classes_95045": "모든 생성자 함수를 클래스로 변환", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "모든 잘못된 문자를 HTML 엔터티 코드로 변환", "Convert_all_re_exported_types_to_type_only_exports_1365": "다시 내보낸 모든 형식을 형식 전용 내보내기로 변환", "Convert_all_require_to_import_95048": "모든 'require'를 'import'로 변환", "Convert_all_to_async_functions_95066": "모두 비동기 함수로 변환", "Convert_all_to_bigint_numeric_literals_95092": "모두 bigint 숫자 리터럴로 변환", "Convert_all_to_default_imports_95035": "모든 항목을 기본 가져오기로 변환", "Convert_all_type_literals_to_mapped_type_95021": "모든 형식 리터럴을 매핑된 형식으로 변환", "Convert_all_typedef_to_TypeScript_types_95177": "모든 typedef를 TypeScript 형식으로 변환합니다.", "Convert_arrow_function_or_function_expression_95122": "화살표 함수 또는 함수 식 변환", "Convert_const_to_let_95093": "'const'를 'let'으로 변환", "Convert_default_export_to_named_export_95061": "기본 내보내기를 명명된 내보내기로 변환", "Convert_function_declaration_0_to_arrow_function_95106": "함수 선언 '{0}'을(를) 화살표 함수로 변환", "Convert_function_expression_0_to_arrow_function_95105": "함수 식 '{0}'을(를) 화살표 함수로 변환", "Convert_function_to_an_ES2015_class_95001": "함수를 ES2015 클래스로 변환", "Convert_invalid_character_to_its_html_entity_code_95100": "잘못된 문자를 html 엔터티 코드로 변환", "Convert_named_export_to_default_export_95062": "명명된 내보내기를 기본 내보내기로 변환", "Convert_named_imports_to_default_import_95170": "명명된 가져오기를 기본 가져오기로 변환", "Convert_named_imports_to_namespace_import_95057": "명명된 가져오기를 네임스페이스 가져오기로 변환", "Convert_namespace_import_to_named_imports_95056": "네임스페이스 가져오기를 명명된 가져오기로 변환", "Convert_overload_list_to_single_signature_95118": "오버로드 목록을 단일 시그니처로 변환", "Convert_parameters_to_destructured_object_95075": "매개 변수를 구조 파괴 개체로 변환", "Convert_require_to_import_95047": "'require'를 'import'로 변환", "Convert_to_ES_module_95017": "ES 모듈로 변환", "Convert_to_a_bigint_numeric_literal_95091": "bigint 숫자 리터럴로 변환", "Convert_to_anonymous_function_95123": "익명 함수로 변환", "Convert_to_arrow_function_95125": "화살표 함수로 변환", "Convert_to_async_function_95065": "비동기 함수로 변환", "Convert_to_default_import_95013": "기본 가져오기로 변환", "Convert_to_named_function_95124": "명명된 함수로 변환", "Convert_to_optional_chain_expression_95139": "선택적 체인 식으로 변환합니다.", "Convert_to_template_string_95096": "템플릿 문자열로 변환", "Convert_to_type_only_export_1364": "형식 전용 내보내기로 변환", "Convert_typedef_to_TypeScript_type_95176": "typedef를 TypeScript 형식으로 변환합니다.", "Corrupted_locale_file_0_6051": "로캘 파일 {0}이(가) 손상되었습니다.", "Could_not_convert_to_anonymous_function_95153": "익명 함수로 변환할 수 없습니다.", "Could_not_convert_to_arrow_function_95151": "화살표 함수로 변환할 수 없습니다.", "Could_not_convert_to_named_function_95152": "명명된 함수로 변환할 수 없습니다.", "Could_not_determine_function_return_type_95150": "함수 반환 형식을 확인할 수 없습니다.", "Could_not_find_a_containing_arrow_function_95127": "포함하는 화살표 함수를 찾을 수 없습니다.", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "모듈 '{0}'에 대한 선언 파일을 찾을 수 없습니다. '{1}'에는 암시적으로 'any' 형식이 포함됩니다.", "Could_not_find_convertible_access_expression_95140": "변환 가능한 액세스 식을 찾을 수 없습니다.", "Could_not_find_export_statement_95129": "export 문을 찾을 수 없습니다.", "Could_not_find_import_clause_95131": "import 절을 찾을 수 없습니다.", "Could_not_find_matching_access_expressions_95141": "일치하는 액세스 식을 찾을 수 없습니다.", "Could_not_find_name_0_Did_you_mean_1_2570": "'{0}' 이름을 찾을 수 없습니다. '{1}'을(를) 사용하시겠습니까?", "Could_not_find_namespace_import_or_named_imports_95132": "네임스페이스 가져오기 또는 명명된 가져오기를 찾을 수 없습니다.", "Could_not_find_property_for_which_to_generate_accessor_95135": "접근자를 생성할 속성을 찾을 수 없습니다.", "Could_not_find_variable_to_inline_95185": "인라인할 변수를 찾을 수 없습니다.", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "{1} 확장이 포함된 '{0}' 경로를 확인할 수 없습니다.", "Could_not_write_file_0_Colon_1_5033": "'{0}' 파일을 쓸 수 없습니다. '{1}'.", "Create_source_map_files_for_emitted_JavaScript_files_6694": "내보낸 JavaScript 파일의 소스 맵 파일을 만듭니다.", "Create_sourcemaps_for_d_ts_files_6614": "d.ts 파일의 sourcemap을 만듭니다.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "작업 디렉터리에 권장되는 설정을 사용하여 tsconfig.json을 만듭니다.", "DIRECTORY_6038": "디렉터리", "Decimal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_1537": "문자 클래스에서는 10진수 이스케이프 시퀀스 및 역참조를 사용할 수 없습니다.", "Decimals_with_leading_zeros_are_not_allowed_1489": "앞에 0이 있는 10진수는 허용되지 않습니다.", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "선언이 다른 파일의 선언을 확대합니다. 이 작업은 직렬화할 수 없습니다.", "Declaration_emit_for_this_file_requires_preserving_this_import_for_augmentations_This_is_not_support_9026": "이 파일에 대한 선언 내보내기에서는 확대를 위해 이 가져오기를 유지해야 합니다. 이는 --isolatedDeclarations에서는 지원되지 않습니다.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "이 파일의 선언 내보내기에는 프라이빗 이름 '{0}'을(를) 사용해야 합니다. 명시적 형식 주석은 선언 내보내기를 차단 해제할 수 있습니다.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "이 파일의 선언 내보내기에는 '{1}' 모듈의 프라이빗 이름 '{0}'을(를) 사용해야 합니다. 명시적 형식 주석은 선언 내보내기를 차단 해제할 수 있습니다.", "Declaration_emit_for_this_parameter_requires_implicitly_adding_undefined_to_its_type_This_is_not_sup_9025": "이 매개 변수에 대한 선언 내보내기를 사용하려면 정의되지 않은 형식을 암시적으로 추가해야 합니다. 이는 --isolatedDeclarations에서는 지원되지 않습니다.", "Declaration_expected_1146": "선언이 필요합니다.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "선언 이름이 기본 제공 전역 ID '{0}'과(와) 충돌합니다.", "Declaration_or_statement_expected_1128": "선언 또는 문이 필요합니다.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "선언 또는 문이 필요합니다. 이 '='은 문 블록을 따르므로 구조 파괴 할당을 작성하려는 경우 전체 할당을 괄호로 묶어야 할 수 있습니다.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "한정된 할당 어설션이 포함된 선언에는 형식 주석도 있어야 합니다.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "이니셜라이저가 포함된 선언에는 한정적 할당 어설션을 사용할 수 없습니다.", "Declare_a_private_field_named_0_90053": "'{0}'(이)라는 프라이빗 필드를 선언합니다.", "Declare_method_0_90023": "'{0}' 메서드 선언", "Declare_private_method_0_90038": "프라이빗 메서드 '{0}' 선언", "Declare_private_property_0_90035": "'{0}' 프라이빗 속성 선언", "Declare_property_0_90016": "'{0}' 속성 선언", "Declare_static_method_0_90024": "'{0}' 정적 메서드 선언", "Declare_static_property_0_90027": "'{0}' 정적 속성 선언", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "데코레이터 함수 반환 유형 '{0}'은(는) '{1}' 유형에 할당할 수 없습니다.", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "데코레이터 함수 반환 유형은 '{0}'이지만 'void' 또는 'any'여야 합니다.", "Decorator_used_before_export_here_1486": "여기에서 'export' 앞에 데코레이터를 사용했습니다.", "Decorators_are_not_valid_here_1206": "데코레이터는 여기에 사용할 수 없습니다.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "동일한 이름의 여러 get/set 접근자에 데코레이터를 적용할 수 없습니다.", "Decorators_may_not_appear_after_export_or_export_default_if_they_also_appear_before_export_8038": "'export' 앞에도 데코레이터가 나타나면 'export' 또는 'export default' 뒤에 나타나지 않을 수 있습니다.", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "데코레이터는 속성 선언의 이름 및 모든 키워드 앞에 와야 합니다.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "기본 catch 절 변수는 'any' 대신 'unknown'입니다.", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "모듈의 기본 내보내기에서 프라이빗 이름 '{0}'을(를) 가지고 있거나 사용 중입니다.", "Default_exports_can_t_be_inferred_with_isolatedDeclarations_9037": "기본 내보내기는 --isolatedDeclarations로 유추할 수 없습니다.", "Default_library_1424": "기본 라이브러리", "Default_library_for_target_0_1425": "대상 '{0}'의 기본 라이브러리", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "{0} 식별자의 정의가 다른 파일의 정의와 충돌합니다.", "Delete_all_unused_declarations_95024": "사용하지 않는 선언 모두 삭제", "Delete_all_unused_imports_95147": "사용하지 않는 가져오기 모두 삭제", "Delete_all_unused_param_tags_95172": "사용하지 않은 '@param' 태그 모두 삭제", "Delete_the_outputs_of_all_projects_6365": "모든 프로젝트의 출력을 삭제합니다.", "Delete_unused_param_tag_0_95171": "사용하지 않는 '@param' 태그 '{0}' 삭제", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[사용되지 않음] 대신 '--jsxFactory'를 사용합니다. 'react' JSX 내보내기를 대상으로 할 경우 createElement에 대해 호출되는 개체를 지정합니다.", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[사용되지 않음] 대신 '--outFile'을 사용합니다. 출력을 연결하고 단일 파일로 내보냅니다.", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[사용되지 않음] 대신 '--skipLibChe<PERSON>'를 사용합니다. 기본 라이브러리 선언 파일의 형식 검사를 건너뜁니다.", "Deprecated_setting_Use_outFile_instead_6677": "더 이상 사용되지 않는 설정입니다. 대신 'outFile'을 사용하세요.", "Did_you_forget_to_use_await_2773": "'await' 사용을 잊으셨습니까?", "Did_you_mean_0_1369": "'{0}'을(를) 사용하시겠습니까?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "'{0}'을(를) 'new (...args: any[]) => {1}' 형식으로 제한하시겠습니까?", "Did_you_mean_to_call_this_expression_6212": "이 식을 호출하시겠습니까?", "Did_you_mean_to_mark_this_function_as_async_1356": "이 함수를 'async'로 표시하시겠습니까?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "':'을 사용하려고 하셨습니까? '='은 포함하는 개체 리터럴이 구조 파괴 패턴에 속하는 경우에만 속성 이름 뒤에 올 수 있습니다.", "Did_you_mean_to_use_new_with_this_expression_6213": "이 식에서 'new'를 사용하시겠습니까?", "Digit_expected_1124": "숫자가 필요합니다.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "'{0}' 디렉터리가 없으므로 이 디렉터리에서 모든 조회를 건너뜁니다.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "디렉터리 '{0}'에는 포함하는 package.json 범위가 없습니다. 가져오기가 확인되지 않습니다.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "내보낸 JavaScript 파일에서 'use strict' 지시문을 추가하지 않도록 설정합니다.", "Disable_checking_for_this_file_90018": "이 파일 확인을 사용하지 않도록 설정", "Disable_emitting_comments_6688": "주석 내보내기 사용 안 함", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "JSDoc 주석에 '@internal'이 있는 선언을 내보내는 것을 비활성화합니다.", "Disable_emitting_files_from_a_compilation_6660": "컴파일에서 파일을 내보내지 않도록 설정합니다.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "형식 검사 오류가 보고되면 파일을 내보내지 않도록 설정합니다.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "생성된 코드에서 'const enum' 선언 지우기를 비활성화합니다.", "Disable_error_reporting_for_unreachable_code_6603": "연결할 수 없는 코드에 대한 오류 보고를 사용하지 않습니다.", "Disable_error_reporting_for_unused_labels_6604": "사용하지 않은 레이블에 대한 오류 보고를 사용하지 않습니다.", "Disable_full_type_checking_only_critical_parse_and_emit_errors_will_be_reported_6805": "전체 형식 검사를 사용하지 않도록 설정합니다(중요한 구문 분석 및 내보내기 오류만 보고됨).", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "컴파일된 출력에서 ​​'__extents'와 같은 사용자 지정 도우미 함수 생성을 비활성화합니다.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "기본 lib.d.ts를 비롯하여 모든 라이브러리 파일을 포함하지 않도록 설정합니다.", "Disable_loading_referenced_projects_6235": "참조된 프로젝트 로드를 사용하지 않습니다.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "복합 프로젝트를 참조할 때 선언 파일 대신 선호하는 소스 파일을 비활성화합니다.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "개체 리터럴을 만드는 동안에는 초과 속성 오류에 대한 보고를 사용하지 않습니다.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "실제 경로의 symlink를 확인하지 않도록 설정합니다. 이는 노드의 동일한 플래그와 상관 관계가 있습니다.", "Disable_size_limitations_on_JavaScript_projects_6162": "JavaScript 프로젝트에 대한 크기 제한을 사용하지 않도록 설정합니다.", "Disable_solution_searching_for_this_project_6224": "이 프로젝트를 검색하는 솔루션을 사용하지 않도록 설정합니다.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "함수 형식의 제네릭 시그니처에 대한 엄격한 검사를 사용하지 않도록 설정합니다.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "JavaScript 프로젝트의 형식 인식을 사용하지 않습니다.", "Disable_truncating_types_in_error_messages_6663": "오류 메시지에서 잘림 유형을 사용하지 않도록 설정합니다.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "참조된 프로젝트의 선언 파일 대신 소스 파일을 사용하지 않도록 설정합니다.", "Disable_wiping_the_console_in_watch_mode_6684": "시계 모드에서 콘솔 초기화를 비활성화합니다.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "프로젝트에서 파일 이름을 확인하여 형식 인식에 대한 유추를 사용하지 않습니다.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "TypeScript가 프로젝트에 추가해야 하는 파일 수를 확장하는 '가져오기', '요구' 또는 '<reference>'를 허용하지 않습니다.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "동일한 파일에 대해 대/소문자를 일관되지 않게 사용한 참조를 허용하지 않습니다.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "컴파일된 파일 목록에 삼중 슬래시 참조 또는 가져온 모듈을 추가하지 않습니다.", "Do_not_allow_runtime_constructs_that_are_not_part_of_ECMAScript_6721": "ECMAScript의 일부가 아닌 런타임 구문을 허용하지 않습니다.", "Do_not_emit_comments_to_output_6009": "주석을 출력에 내보내지 마세요.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "'@internal' 주석이 있는 코드에 대한 선언을 내보내지 마세요.", "Do_not_emit_outputs_6010": "출력을 내보내지 않습니다.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "오류가 보고되면 출력을 내보내지 않습니다.", "Do_not_emit_use_strict_directives_in_module_output_6112": "'use strict' 지시문을 모듈 출력에 내보내지 마세요.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "생성된 코드에서 const 열거형 선언을 지우지 마세요.", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "컴파일된 출력에서 '__extends'와 같은 사용자 지정 도우미 함수를 생성하지 않습니다.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "기본 라이브러리 파일(lib.d.ts)을 포함하지 않습니다.", "Do_not_report_errors_on_unreachable_code_6077": "접근할 수 없는 코드에 대한 오류를 보고하지 않습니다.", "Do_not_report_errors_on_unused_labels_6074": "사용되지 않는 레이블에 대한 오류를 보고하지 않습니다.", "Do_not_resolve_the_real_path_of_symlinks_6013": "symlink의 실제 경로를 확인하지 마세요.", "Do_not_transform_or_elide_any_imports_or_exports_not_marked_as_type_only_ensuring_they_are_written_i_6804": "'module' 설정에 따라 출력 파일의 형식으로 작성되도록 형식 전용으로 표시되지 않은 가져오기 또는 내보내기를 변환하거나 생략하지 마세요.", "Do_not_truncate_error_messages_6165": "오류 메시지를 자르지 않습니다.", "Duplicate_function_implementation_2393": "중복된 함수 구현입니다.", "Duplicate_identifier_0_2300": "'{0}' 식별자가 중복되었습니다.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "'{0}' 식별자가 중복되었습니다. 컴파일러는 모듈의 최상위 범위에 이름 '{1}'을(를) 예약합니다.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "'{0}' 식별자가 중복되었습니다. 컴파일러는 비동기 함수를 포함하는 모듈의 최상위 범위에 '{1}' 이름을 예약합니다.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "중복 식별자 '{0}'입니다. 컴파일러는 정적 이니셜라이저에서 'super' 참조를 내보낸 경우 '{1}' 이름을 예약합니다.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "중복 식별자 '{0}'입니다. 컴파일러는 '{1}' 선언을 사용하여 비동기 함수를 지원합니다.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "'{0}' 식별자가 중복되었습니다. 정적 요소와 인스턴스 요소는 같은 프라이빗 이름을 공유할 수 없습니다.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "중복 식별자 'arguments'입니다. 컴파일러는 'arguments'를 사용해서 rest 매개 변수를 초기화합니다.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "'_newTarget' 식별자가 중복되었습니다. 컴파일러는 변수 선언 '_newTarget'을 사용하여 'new.target' 메타 속성 참조를 캡처합니다.", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "중복 식별자 '_this'입니다. 컴파일러는 변수 선언 '_this'를 사용해서 'this' 참조를 캡처합니다.", "Duplicate_index_signature_for_type_0_2374": "'{0}' 형식에 대한 인덱스 시그니처가 중복되었습니다.", "Duplicate_label_0_1114": "중복된 레이블 '{0}'입니다.", "Duplicate_property_0_2718": "중복 속성 '{0}'입니다.", "Duplicate_regular_expression_flag_1500": "중복된 정규식 플래그입니다.", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "동적 가져오기의 지정자는 'string' 형식이어야 하지만 여기에서 형식은 '{0}'입니다.", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "동적 가져오기는 '--module' 플래그가 'es2020', 'es2022', 'esnext', 'commonjs', 'amd', 'system', 'umd', 'node16', 'node18' 또는 'nodenext'로 설정된 경우에만 지원됩니다.", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_set_of_attributes_as_arguments_1450": "동적 가져오기는 모듈 지정자와 선택적 특성 집합만 인수로 허용할 수 있습니다.", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_node18_1324": "동적 가져오기는 '--module' 옵션이 'esnext', 'node16', 'node18', 'nodenext' 또는 'preserve'로 설정된 경우에만 두 번째 인수를 지원합니다.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_module_is_set_to_preserve_1293": "'module'이 'preserve'로 설정된 경우 CommonJS 모듈에서는 ESM 구문을 사용할 수 없습니다.", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_verbatimModuleSyntax_is_enabled_1286": "'verbatimModuleSyntax'를 사용하도록 설정한 경우 CommonJS 모듈에서는 ESM 구문을 사용할 수 없습니다.", "Each_declaration_of_0_1_differs_in_its_value_where_2_was_expected_but_3_was_given_4125": "'{0}.{1}'의 각 선언 값이 다릅니다. 여기서 '{2}'이(가) 필요한데 '{3}'이(가) 제공되었습니다.", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "공용 구조체 형식 '{0}'의 각 멤버에 구문 시그니처가 있지만, 해당 시그니처는 서로 호환되지 않습니다.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "공용 구조체 형식 '{0}'의 각 멤버에 시그니처가 있지만, 해당 시그니처는 서로 호환되지 않습니다.", "Editor_Support_6249": "편집기 지원", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "'{0}' 형식의 식을 '{1}' 인덱스 형식에 사용할 수 없으므로 요소에 암시적으로 'any' 형식이 있습니다.", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "인덱스 식이 'number' 형식이 아니므로 요소에 암시적으로 'any' 형식이 있습니다.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "'{0}' 형식에 인덱스 시그니처가 없으므로 요소에 암시적으로 'any' 형식이 있습니다.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "'{0}' 형식에 인덱스 시그니처가 없으므로 요소에 암시적으로 'any' 형식이 있습니다. '{1}'을(를) 호출하시겠습니까?", "Emit_6246": "방출", "Emit_ECMAScript_standard_compliant_class_fields_6712": "ECMAScript 표준 규격 클래스 필드를 내보냅니다.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "출력 파일의 시작에서 UTF-8 BOM(바이트 순서 표시)을 내보냅니다.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "별도의 파일을 사용하는 대신 소스 맵과 함께 단일 파일을 내보냅니다.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "디버깅을 위해 실행된 컴파일러의 v8 CPU 프로필을 내보냅니다.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "CommonJS 모듈 가져오기 지원을 쉽게 하기 위해 추가 JavaScript를 내보냅니다. 이것은 유형 호환성을 위해 'allowSyntheticDefaultImports'를 활성화합니다.", "Emit_class_fields_with_Define_instead_of_Set_6222": "Set 대신 Define을 사용하여 클래스 필드를 내보냅니다.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "소스 파일에서 데코레이트된 선언의 디자인 형식 메타데이터를 내보냅니다.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "반복을 위해 규격에 더 맞지만 장황하고 성능이 떨어지는 JavaScript를 내보냅니다.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "단일 파일 내에서 소스 맵과 함께 소스를 내보냅니다. '--inlineSourceMap' 또는 '--sourceMap'을 설정해야 합니다.", "Enable_all_strict_type_checking_options_6180": "엄격한 형식 검사 옵션을 모두 사용하도록 설정합니다.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "컴파일러 오류를 더 쉽게 읽을 수 있도록 TypeScript의 출력에서 ​​색상 및 서식을 활성화합니다.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "TypeScript 프로젝트를 프로젝트 참조와 함께 사용할 수 있는 제약 조건을 사용합니다.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "함수에서 명시적으로 반환되지 않은 코드 경로에 대한 오류 보고를 사용합니다.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "암시적 '모든' 유형의 표현식 및 선언에 대한 오류 보고를 활성화합니다.", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "switch 문에서 폴스루 사례에 대한 오류 보고를 활성화합니다.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "형식이 확인된 JavaScript 파일에서 오류 보고를 사용하도록 설정합니다.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "지역 변수를 읽지 않을 때 오류 보고를 활성화합니다.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "'this'에 'any' 유형이 지정되면 오류 보고를 활성화합니다.", "Enable_experimental_support_for_legacy_experimental_decorators_6630": "레거시 실험적 데코레이터에 대해 실험적 지원을 사용하도록 설정합니다.", "Enable_importing_files_with_any_extension_provided_a_declaration_file_is_present_6264": "선언 파일이 있는 경우 확장자가 있는 파일 가져오기를 사용하도록 설정합니다.", "Enable_importing_json_files_6689": ".json 파일 가져오기를 활성화합니다.", "Enable_lib_replacement_6808": "라이브러리 바꾸기를 사용하도록 설정합니다.", "Enable_project_compilation_6302": "프로젝트 컴파일을 사용하도록 설정", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "함수에서 strict 'bind', 'call' 및 'apply' 메서드를 사용하도록 설정합니다.", "Enable_strict_checking_of_function_types_6186": "함수 형식에 대한 엄격한 검사를 사용하도록 설정합니다.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "클래스의 속성 초기화에 대해 엄격한 검사를 사용하도록 설정합니다.", "Enable_strict_null_checks_6113": "엄격한 null 검사를 사용하도록 설정하세요.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "구성 파일에서 'experimentalDecorators' 옵션을 사용하도록 설정합니다.", "Enable_the_jsx_flag_in_your_configuration_file_95088": "구성 파일에서 '--jsx' 플래그를 사용하도록 설정합니다.", "Enable_tracing_of_the_name_resolution_process_6085": "이름 확인 프로세스 추적을 사용하도록 설정하세요.", "Enable_verbose_logging_6713": "자세한 로깅을 활성화합니다.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "모든 가져오기에 대한 네임스페이스 개체를 만들어 CommonJS 및 ES 모듈 간의 내보내기 상호 운용성을 사용하도록 설정합니다. 'allowSyntheticDefaultImports'를 의미합니다.", "Enables_experimental_support_for_ES7_decorators_6065": "ES7 데코레이터에 대해 실험적 지원을 사용합니다.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "데코레이터에 대한 형식 메타데이터를 내보내기 위해 실험적 지원을 사용합니다.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "인덱싱된 형식을 사용하여 선언된 키에 대해 인덱싱된 접근자를 사용합니다.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "파생 클래스의 멤버 재정의가 재정의 한정자로 표시되어 있는지 확인합니다.", "Ensure_that_casing_is_correct_in_imports_6637": "가져오기에서 대/소문자가 올바른지 확인합니다.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "다른 가져오기를 사용하지 않고 각 파일을 안전하게 변환할 수 있는지 확인합니다.", "Ensure_use_strict_is_always_emitted_6605": "'use strict'를 항상 내보내고 있는지 확인합니다.", "Entering_conditional_exports_6413": "조건부 내보내기가 시작됩니다.", "Entry_point_for_implicit_type_library_0_1420": "암시적 형식 라이브러리 '{0}'의 진입점", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "packageId가 '{1}'인 암시적 형식 라이브러리 '{0}'의 진입점", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "compilerOptions에 지정된 형식 라이브러리 '{0}'의 진입점", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "packageId가 '{1}'인 compilerOptions에 지정된 형식 라이브러리 '{0}'의 진입점", "Enum_0_used_before_its_declaration_2450": "선언 전에 사용된 '{0}' 열거형입니다.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "열거형 선언은 네임스페이스 또는 다른 열거형 선언과만 병합할 수 있습니다.", "Enum_declarations_must_all_be_const_or_non_const_2473": "열거형 선언은 모두 const 또는 비const여야 합니다.", "Enum_member_expected_1132": "열거형 멤버가 필요합니다.", "Enum_member_following_a_non_literal_numeric_member_must_have_an_initializer_when_isolatedModules_is__18056": "'isolatedModules'를 사용하도록 설정한 경우 리터럴이 아닌 숫자 멤버 다음에 오는 열거형 멤버에는 이니셜라이저가 있어야 합니다.", "Enum_member_initializers_must_be_computable_without_references_to_external_symbols_with_isolatedDecl_9020": "열거형 멤버 이니셜라이저는 --isolatedDeclarations를 사용하여 외부 기호에 대한 참조 없이 계산할 수 있어야 합니다.", "Enum_member_must_have_initializer_1061": "열거형 멤버에는 이니셜라이저가 있어야 합니다.", "Enum_name_cannot_be_0_2431": "열거형 이름은 '{0}'일 수 없습니다.", "Errors_Files_6041": "오류 파일", "Escape_sequence_0_is_not_allowed_1488": "이스케이프 시퀀스 '{0}'은(는) 허용되지 않습니다.", "Examples_Colon_0_6026": "예: {0}", "Excessive_complexity_comparing_types_0_and_1_2859": "'{0}' 및 '{1}' 형식을 비교하는 데 과도한 복잡성이 있습니다.", "Excessive_stack_depth_comparing_types_0_and_1_2321": "'{0}' 및 '{1}' 형식을 비교하는 스택 깊이가 과도합니다.", "Exiting_conditional_exports_6416": "조건부 내보내기가 종료됩니다.", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "'@extends' 태그로 제공하는 예상되는 {0}-{1} 형식 인수입니다.", "Expected_0_arguments_but_got_1_2554": "{0}개의 인수가 필요한데 {1}개를 가져왔습니다.", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "{0}개 인수가 필요한데 {1}개를 가져왔습니다. 'void'를 'Promise'의 형식 인수에 포함하는 것을 잊으셨습니까?", "Expected_0_type_arguments_but_got_1_2558": "{0}개의 형식 인수가 필요한데 {1}개를 가져왔습니다.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "'@extends' 태그로 제공하는 예상되는 {0} 형식 인수입니다.", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "1개의 인수가 필요하지만 0이 있습니다. 'new Promise()'는 인수 없이 호출할 수 있는 'resolve'를 생성하기 위해 JSDoc 힌트가 필요합니다.", "Expected_a_Unicode_property_name_1523": "유니코드 속성 이름이 필요합니다.", "Expected_a_Unicode_property_name_or_value_1527": "유니코드 속성 이름 또는 값이 필요합니다.", "Expected_a_Unicode_property_value_1525": "유니코드 속성 값이 필요합니다.", "Expected_a_capturing_group_name_1514": "캡처 그룹 이름이 필요합니다.", "Expected_a_class_set_operand_1520": "클래스 집합 피연산자가 필요합니다.", "Expected_at_least_0_arguments_but_got_1_2555": "최소 {0}개의 인수가 필요한데 {1}개를 가져왔습니다.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "'{0}'에 해당하는 JSX 닫는 태그가 필요합니다.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "JSX 조각에 닫는 태그가 필요합니다.", "Expected_for_property_initializer_1442": "속성 이니셜라이저에는 '='가 필요합니다.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "'package.json'의 '{0}' 필드에 '{1}' 형식이 필요한데 '{2}'을(를) 얻었습니다.", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "명시적으로 지정된 모듈 확인 종류 '{0}'입니다.", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "'target' 옵션이 'es2016' 이상으로 설정되어 있지 않으면 'bigint' 값에 지수화를 수행할 수 없습니다.", "Export_0_from_module_1_90059": "'{0}'을(를) '{1}' 모듈에서 내보냅니다.", "Export_all_referenced_locals_90060": "참조된 모든 로컬 내보내기", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "ECMAScript 모듈을 대상으로 하는 경우 내보내기 할당을 사용할 수 없습니다. 대신 'export default'나 다른 모듈 형식의 사용을 고려하세요.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "'--module' 플래그가 'system'이면 내보내기 할당은 지원되지 않습니다.", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "내보내기 선언이 '{0}'의 내보낸 선언과 충돌합니다.", "Export_declarations_are_not_permitted_in_a_namespace_1194": "네임스페이스에서는 내보내기 선언이 허용되지 않습니다.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "내보내기 지정자 '{0}'이(가) '{1}' 경로의 package.json 범위에 없습니다.", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "내보낸 형식 별칭 '{0}'은(는) '{1}' 프라이빗 이름을 포함하거나 사용 중입니다.", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "내보낸 형식 별칭 '{0}'이(가) 모듈 {2}의 프라이빗 이름 '{1}'을(를) 포함하거나 사용하고 있습니다.", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "내보낸 변수 '{0}'이(가) 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "내보낸 변수 '{0}'이(가) 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Exported_variable_0_has_or_is_using_private_name_1_4025": "내보낸 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "내보내기 및 내보내기 할당는 모듈 확대에서 허용되지 않습니다.", "Expression_expected_1109": "식이 필요합니다.", "Expression_must_be_enclosed_in_parentheses_to_be_used_as_a_decorator_1497": "식을 데코레이터로 사용하려면 괄호로 묶어야 합니다.", "Expression_or_comma_expected_1137": "식 또는 쉼표가 필요합니다.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "식이 너무 커서 표시할 수 없는 튜플 형식을 생성합니다.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "식에서는 너무 복잡해서 표시할 수 없는 공용 구조체 형식을 생성합니다.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "컴파일러가 기본 클래스 참조를 캡처하기 위해 사용하는 '_super'로 식이 확인됩니다.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "컴파일러가 'new.target' 메타 속성 참조를 캡처하기 위해 사용하는 변수 선언 '_newTarget'으로 식이 확인됩니다.", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "컴파일러가 'this' 참조를 캡처하기 위해 사용하는 변수 선언 '_this'로 식이 확인됩니다.", "Expression_type_can_t_be_inferred_with_isolatedDeclarations_9013": "식 형식은 --isolatedDeclarations로 유추할 수 없습니다.", "Extends_clause_can_t_contain_an_expression_with_isolatedDeclarations_9021": "Extends 절에는 --isolatedDeclarations가 있는 식을 포함할 수 없습니다.", "Extends_clause_for_inferred_type_0_has_or_is_using_private_name_1_4085": "유추된 형식 '{0}'에 대한 extends 절이 '{1}' 비공개 이름을 가지고 있거나 사용 중입니다.", "Extract_base_class_to_variable_90064": "변수에 기본 클래스 추출", "Extract_binding_expressions_to_variable_90066": "변수에 바인딩 식 추출", "Extract_constant_95006": "상수 추출", "Extract_default_export_to_variable_90065": "변수로 기본 내보내기 추출", "Extract_function_95005": "함수 추출", "Extract_to_0_in_1_95004": "{1}의 {0}(으)로 추출", "Extract_to_0_in_1_scope_95008": "{1} 범위의 {0}(으)로 추출", "Extract_to_0_in_enclosing_scope_95007": "바깥쪽 범위의 {0}(으)로 추출", "Extract_to_interface_95090": "인터페이스로 추출", "Extract_to_type_alias_95078": "형식 별칭으로 추출", "Extract_to_typedef_95079": "Typedef로 추출", "Extract_to_variable_and_replace_with_0_as_typeof_0_90069": "변수로 추출하고 '{0} as typeof {0}'(으)로 바꾸기", "Extract_type_95077": "형식 추출", "FILE_6035": "파일", "FILE_OR_DIRECTORY_6040": "파일 또는 디렉터리", "Failed_to_find_peerDependency_0_6283": "peerDependency '{0}'을(를) 찾지 못했습니다.", "Failed_to_resolve_under_condition_0_6415": "조건 '{0}'에서 확인하지 못했습니다.", "Fallthrough_case_in_switch_7029": "switch에 Fallthrough case가 있습니다.", "File_0_does_not_exist_6096": "'{0}' 파일이 없습니다.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "이전 캐시된 검색에 따라 '{0}' 파일이 존재하지 않습니다.", "File_0_exists_according_to_earlier_cached_lookups_6239": "파일 '{0}'은(는) 이전 캐시된 검색에 따라 존재합니다.", "File_0_exists_use_it_as_a_name_resolution_result_6097": "'{0}' 파일이 있습니다. 이 파일을 이름 확인 결과로 사용하세요.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "'{0}' 파일의 확장명이 지원되지 않습니다. 지원되는 확장명은 {1}뿐입니다.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "'{0}' 파일은 JavaScript 파일입니다. 'allowJs' 옵션을 사용하도록 설정하시겠습니까?", "File_0_is_not_a_module_2306": "'{0}' 파일은 모듈이 아닙니다.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "'{0}' 파일이 '{1}' 프로젝트의 파일 목록에 나열되지 않습니다. 프로젝트는 모든 파일을 나열하거나 'include' 패턴을 사용해야 합니다.", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "'{0}' 파일이 'rootDir' '{1}' 아래에 있지 않습니다. 'rootDir'에는 모든 소스 파일이 포함되어 있어야 합니다.", "File_0_not_found_6053": "파일 '{0}'을(를) 찾을 수 없습니다.", "File_Management_6245": "파일 관리", "File_appears_to_be_binary_1490": "파일이 이진 파일인 것 같습니다.", "File_change_detected_Starting_incremental_compilation_6032": "파일 변경이 검색되었습니다. 증분 컴파일을 시작하는 중...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "'{0}'에 \"type\" 필드가 없으므로 파일이 CommonJS 모듈입니다.", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "'{0}'에 값이 \"module\"이 아닌 \"type\" 필드가 있으므로 파일이 CommonJS 모듈입니다.", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "'package.json'을 찾을 수 없으므로 파일이 CommonJS 모듈입니다.", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "'{0}'에 값이 \"module\"인 \"type\" 필드가 있으므로 파일이 ECMAScript 모듈입니다.", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "파일이 CommonJS 모듈입니다. ES 모듈로 변환될 수 있습니다.", "File_is_default_library_for_target_specified_here_1426": "파일은 여기에 지정된 대상의 기본 라이브러리입니다.", "File_is_entry_point_of_type_library_specified_here_1419": "파일은 여기에 지정된 형식 라이브러리의 진입점입니다.", "File_is_included_via_import_here_1399": "파일은 여기에 가져오기를 통해 포함됩니다.", "File_is_included_via_library_reference_here_1406": "파일은 여기에 라이브러리 참조를 통해 포함됩니다.", "File_is_included_via_reference_here_1401": "파일은 여기에 참조를 통해 포함됩니다.", "File_is_included_via_type_library_reference_here_1404": "파일은 여기에 형식 라이브러리 참조를 통해 포함됩니다.", "File_is_library_specified_here_1423": "파일은 여기에 지정된 라이브러리입니다.", "File_is_matched_by_files_list_specified_here_1410": "파일은 여기에 지정된 'files' 목록으로 일치됩니다.", "File_is_matched_by_include_pattern_specified_here_1408": "파일은 여기에 지정된 포함 패턴으로 일치됩니다.", "File_is_output_from_referenced_project_specified_here_1413": "파일은 여기에 지정된 참조 프로젝트의 출력입니다.", "File_is_output_of_project_reference_source_0_1428": "파일은 프로젝트 참조 소스 '{0}'의 출력입니다.", "File_is_source_from_referenced_project_specified_here_1416": "파일은 여기에 지정된 참조된 프로젝트의 소스입니다.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "'{0}' 파일 이름은 이미 포함된 '{1}' 파일 이름과 대/소문자만 다릅니다.", "File_name_0_has_a_1_extension_looking_up_2_instead_6262": "파일 이름 '{0}'에는 '{1}' 확장명이 있습니다. 대신 '{2}'을(를) 조회합니다.", "File_name_0_has_a_1_extension_stripping_it_6132": "파일 이름 '{0}'에 '{1}' 확장명이 있어 제거하는 중입니다.", "File_redirects_to_file_0_1429": "파일은 '{0}' 파일로 리디렉션됩니다.", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "파일 사양은 재귀 디렉터리 와일드카드('**') 뒤에 나타나는 부모 디렉터리('..')를 포함할 수 없습니다. '{0}'.", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "파일 사양은 재귀 디렉터리 와일드카드('**')로 끝날 수 없습니다. '{0}'.", "Filters_results_from_the_include_option_6627": "'include' 옵션의 결과를 필터링합니다.", "Fix_all_detected_spelling_errors_95026": "검색된 맞춤법 오류 모두 수정", "Fix_all_expressions_possibly_missing_await_95085": "'await'가 누락되었을 수 있는 모든 식 수정", "Fix_all_implicit_this_errors_95107": "모든 암시적 'this' 오류 수정", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "비동기 함수의 모든 잘못된 반환 형식 수정", "Fix_all_with_type_only_imports_95182": "형식 전용 가져오기를 사용하여 모두 수정", "Found_0_errors_6217": "{0}개 오류가 발견되었습니다.", "Found_0_errors_Watching_for_file_changes_6194": "{0}개 오류가 발견되었습니다. 파일이 변경되었는지 확인하는 중입니다.", "Found_0_errors_in_1_files_6261": "{1} 파일에서 {0} 오류를 찾았습니다.", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "{1}에서 시작하는 동일한 파일에서 {0}개의 오류를 찾았습니다.", "Found_1_error_6216": "1개 오류가 발견되었습니다.", "Found_1_error_Watching_for_file_changes_6193": "1개 오류가 발견되었습니다. 파일이 변경되었는지 확인하는 중입니다.", "Found_1_error_in_0_6259": "{0}에서 1개의 오류를 찾았습니다.", "Found_package_json_at_0_6099": "'{0}'에서 'package.json'을 찾았습니다.", "Found_peerDependency_0_with_1_version_6282": "'{1}' 버전이 있는 peerDependency '{0}'을(를) 찾았습니다.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_1250": "'ES5'를 대상으로 하는 경우 strict 모드의 블록 내에서 함수 선언을 사용할 수 없습니다.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Class_definiti_1251": "'ES5'를 대상으로 하는 경우 strict 모드의 블록 내에서 함수 선언을 사용할 수 없습니다. 클래스 정의는 자동으로 strict 모드가 됩니다.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Modules_are_au_1252": "'ES5'를 대상으로 하는 경우 strict 모드의 블록 내에서 함수 선언을 사용할 수 없습니다. 모듈은 자동으로 strict 모드가 됩니다.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "반환 형식 주석이 없는 함수 식에는 암시적으로 '{0}' 반환 형식이 포함됩니다.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "함수 구현이 없거나 선언 바로 다음에 나오지 않습니다.", "Function_implementation_name_must_be_0_2389": "함수 구현 이름이 '{0}'이어야 합니다.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "반환 형식 주석이 없고 반환 식 중 하나에서 직간접적으로 참조되므로 함수에는 암시적으로 반환 형식 'any'가 포함됩니다.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "함수에 끝 return 문이 없으며 반환 형식에 'undefined'가 포함되지 않습니다.", "Function_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9007": "함수에는 --isolatedDeclarations가 있는 명시적 반환 형식 주석이 있어야 합니다.", "Function_not_implemented_95159": "함수가 구현되지 않았습니다.", "Function_overload_must_be_static_2387": "함수 오버로드는 정적이어야 합니다.", "Function_overload_must_not_be_static_2388": "함수 오버로드는 정적이 아니어야 합니다.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "공용 구조체 형식에 사용되는 경우 함수 형식 표기법을 괄호로 묶어야 합니다.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "교집합 형식에 사용되는 경우 함수 형식 표기법을 괄호로 묶어야 합니다.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "반환 형식 주석이 없는 함수 형식에는 암시적으로 '{0}' 반환 형식이 포함됩니다.", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "본문이 있는 함수는 앰비언트 클래스하고만 병합할 수 있습니다.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "프로젝트의 TypeScript 및 JavaScript 파일에서 .d.ts 파일을 생성합니다.", "Generate_get_and_set_accessors_95046": "'get' 및 'set' 접근자 생성", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "모든 재정의 속성에 대한 'get' 및 'set' 접근자를 생성합니다.", "Generates_a_CPU_profile_6223": "CPU 프로필을 생성합니다.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "해당하는 각 '.d.ts' 파일에 sourcemap을 생성합니다.", "Generates_an_event_trace_and_a_list_of_types_6237": "이벤트 추적 및 형식 목록을 생성합니다.", "Generates_corresponding_d_ts_file_6002": "해당 '.d.ts' 파일을 생성합니다.", "Generates_corresponding_map_file_6043": "해당 '.map' 파일을 생성합니다.", "Generator_implicitly_has_yield_type_0_Consider_supplying_a_return_type_annotation_7025": "생성기에 암시적으로 '{0}' yield 형식이 있습니다. 반환 형식 주석을 제공하는 것이 좋습니다.", "Generators_are_not_allowed_in_an_ambient_context_1221": "생성기는 앰비언트 컨텍스트에서 사용할 수 없습니다.", "Generic_type_0_requires_1_type_argument_s_2314": "'{0}' 제네릭 형식에 {1} 형식 인수가 필요합니다.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "제네릭 형식 '{0}'에 {1} 및 {2} 사이의 형식 인수가 필요합니다.", "Global_module_exports_may_only_appear_at_top_level_1316": "전역 모듈 내보내기는 최상위 수준에만 나올 수 있습니다.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "전역 모듈 내보내기는 선언 파일에만 나올 수 있습니다.", "Global_module_exports_may_only_appear_in_module_files_1314": "전역 모듈 내보내기는 모듈 파일에만 나올 수 있습니다.", "Global_type_0_must_be_a_class_or_interface_type_2316": "전역 형식 '{0}'은 클래스 또는 인터페이스 형식이어야 합니다.", "Global_type_0_must_have_1_type_parameter_s_2317": "전역 형식 '{0}'에는 {1} 형식 매개 변수를 사용해야 합니다.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "'--incremental' 및 '--watch'의 다시 컴파일에서 파일 내 변경 내용은 파일에 따라 직접 파일에만 영향을 준다고 가정하도록 합니다.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "'증분' 및 '감시' 모드를 사용하는 프로젝트에서 재컴파일하면 파일 내의 변경 사항이 해당 파일에 직접적으로 영향을 미치는 파일에만 영향을 미친다고 가정합니다.", "Hexadecimal_digit_expected_1125": "16진수가 필요합니다.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "식별자가 필요합니다. '{0}'은(는) 모듈의 최상위 수준에 있는 예약어입니다.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "식별자가 필요합니다. '{0}'은(는) strict 모드의 예약어입니다.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "식별자가 필요합니다. '{0}'은(는) strict 모드의 예약어입니다. 클래스 정의는 자동으로 strict 모드가 됩니다.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "식별자가 필요합니다. '{0}'은(는) strict 모드의 예약어입니다. 모듈은 자동으로 strict 모드가 됩니다.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "식별자가 필요합니다. '{0}'은(는) 여기에서 사용할 수 없는 예약어입니다.", "Identifier_expected_1003": "식별자가 필요합니다.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "식별자가 필요합니다. '__esModule'은 ECMAScript 모듈을 변환할 때 내보낸 표식으로 예약되어 있습니다.", "Identifier_or_string_literal_expected_1478": "식별자 또는 문자열 리터럴이 필요합니다.", "Identifier_string_literal_or_number_literal_expected_1496": "식별자, 문자열 리터럴 또는 숫자 리터럴이 필요합니다.", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "'{0}' 패키지가 이 모듈을 실제로 공개하는 경우 'https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}' 수정을 위한 끌어오기 요청을 보내는 것이 좋습니다.", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "'{0}' 패키지가 실제로 이 모듈을 노출하는 경우 'declare module {1}';'이(가) 포함된 새 선언(.d.ts) 파일을 추가해 보세요.", "Ignore_this_error_message_90019": "이 오류 메시지 무시", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "tsconfig.json을 무시하고 기본 컴파일러 옵션을 사용하여 지정된 파일을 컴파일합니다.", "Implement_all_inherited_abstract_classes_95040": "상속된 추상 클래스 모두 구현", "Implement_all_unimplemented_interfaces_95032": "구현되지 않은 인터페이스 모두 구현", "Implement_inherited_abstract_class_90007": "상속된 추상 클래스 구현", "Implement_interface_0_90006": "'{0}' 인터페이스 구현", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "내보낸 클래스 '{0}'의 Implements 절이 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "런타임에는 'symbol'을 'string'으로 암시적으로 변환할 수 없습니다. 이 식을 'String(...)'으로 래핑하는 것이 좋습니다.", "Import_0_conflicts_with_global_value_used_in_this_file_so_must_be_declared_with_a_type_only_import_w_2866": "'{0}' 가져오기는 이 파일에 사용된 전역 값과 충돌하므로 'isolatedModules'를 사용하도록 설정한 경우 형식 전용 가져오기를 사용하여 선언해야 합니다.", "Import_0_conflicts_with_local_value_so_must_be_declared_with_a_type_only_import_when_isolatedModules_2865": "'{0}' 가져오기는 로컬 값과 충돌하므로 'isolatedModules'를 사용하도록 설정한 경우 형식 전용 가져오기를 사용하여 선언해야 합니다.", "Import_0_from_1_90013": "\"{1}\"에서 '{0}'을(를) 가져옵니다.", "Import_assertion_values_must_be_string_literal_expressions_2837": "가져오기 어설션 값은 문자열 리터럴 ㅁ이이어야 합니다.", "Import_assertions_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2836": "CommonJS 'require' 호출로 컴파일되는 문에서는 가져오기 어설션을 사용할 수 없습니다.", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2821": "가져오기 어설션은 '--module' 옵션이 'esnext', 'node18', 'nodenext' 또는 'preserve'로 설정된 경우에만 지원됩니다.", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "가져오기 어설션은 형식 전용 가져오기 또는 내보내기에서 사용할 수 없습니다.", "Import_assertions_have_been_replaced_by_import_attributes_Use_with_instead_of_assert_2880": "가져오기 어설션이 가져오기 특성으로 바뀌었습니다. 'assert' 대신 'with'를 사용합니다.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "ECMAScript 모듈을 대상으로 하는 경우 할당 가져오기를 사용할 수 없습니다. 대신 'import * as ns from \"mod\"', 'import {a} from \"mod\"', 'import d from \"mod\"' 또는 다른 모듈 형식 사용을 고려하세요.", "Import_attribute_values_must_be_string_literal_expressions_2858": "가져오기 특성 값은 문자열 리터럴 식이어야 합니다.", "Import_attributes_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2856": "CommonJS 'require' 호출로 컴파일되는 문에서는 가져오기 특성을 사용할 수 없습니다.", "Import_attributes_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2823": "가져오기 특성은 '--module' 옵션이 'esnext', 'node18', 'nodenext' 또는 'preserve'로 설정된 경우에만 지원됩니다.", "Import_attributes_cannot_be_used_with_type_only_imports_or_exports_2857": "가져오기 특성은 형식 전용 가져오기 또는 내보내기에서 사용할 수 없습니다.", "Import_declaration_0_is_using_private_name_1_4000": "가져오기 선언 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 사용하고 있습니다.", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "가져오기 선언이 '{0}'의 로컬 선언과 충돌합니다.", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "네임스페이스의 가져오기 선언은 모듈을 참조할 수 없습니다.", "Import_emit_helpers_from_tslib_6139": "'tslib'에서 내보내기 도우미를 가져오세요.", "Import_may_be_converted_to_a_default_import_80003": "가져오기가 기본 가져오기로 변환될 수 있습니다.", "Import_name_cannot_be_0_2438": "가져오기 이름은 '{0}'일 수 없습니다.", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "앰비언트 모듈 선언의 가져오기 또는 내보내기 선언은 상대적 모듈 이름을 통해 모듈을 참조할 수 없습니다.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "가져오기 지정자 '{0}'이(가) '{1}' 경로의 package.json 범위에 없습니다.", "Imported_via_0_from_file_1_1393": "'{1}' 파일에서 {0}을(를) 통해 가져왔습니다.", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "compilerOptions에 지정된 대로 'importHelpers'를 가져오기 위해 '{1}' 파일에서 {0}을(를) 통해 가져왔습니다.", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "'jsx' 및 'jsxs' 팩터리 함수를 가져오기 위해 '{1}' 파일에서 {0}을(를) 통해 가져왔습니다.", "Imported_via_0_from_file_1_with_packageId_2_1394": "packageId가 '{2}'인 '{1}' 파일에서 {0}을(를) 통해 가져왔습니다.", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "compilerOptions에 지정된 대로 'importHelpers'를 가져오기 위해 packageId가 '{2}'인 '{1}' 파일에서 {0}을(를) 통해 가져왔습니다.", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "'jsx' 및 'jsxs' 팩터리 함수를 가져오기 위해 packageId가 '{2}'인 '{1}' 파일에서 {0}을(를) 통해 가져왔습니다.", "Importing_a_JSON_file_into_an_ECMAScript_module_requires_a_type_Colon_json_import_attribute_when_mod_1543": "'module'이 '{0}'(으)로 설정된 경우 JSON 파일을 ECMAScript 모듈로 가져오려면 'type: \"json\"' 가져오기 특성이 필요합니다.", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "가져오기는 모듈 확대에서 허용되지 않습니다. 내보내기를 바깥쪽 외부 모듈로 이동하세요.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "앰비언트 열거형 선언에서 멤버 이니셜라이저는 상수 식이어야 합니다.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "다중 선언이 포함된 열거형에서는 하나의 선언만 첫 번째 열거형 요소에 대한 이니셜라이저를 생략할 수 있습니다.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "파일 목록을 포함합니다. 이는 'include'가 아닌 GLOB 패턴을 지원하지 않습니다.", "Include_modules_imported_with_json_extension_6197": "'.json' 확장을 사용하여 가져온 모듈을 포함합니다.", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "내보낸 JavaScript 내 sourcemap에 소스 코드를 포함합니다.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "내보낸 JavaScript 내에 sourcemap 파일을 포함합니다.", "Includes_imports_of_types_referenced_by_0_90054": "'{0}'에서 참조하는 유형의 가져오기를 포함합니다.", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "--watch를 포함하면 -w는 파일 변경 내용에 대한 현재 프로젝트 감시를 시작합니다. 설정되면 다음을 사용하여 조사식 모드를 구성할 수 있습니다.", "Incomplete_quantifier_Digit_expected_1505": "불완전한 수량자입니다. 숫자가 필요합니다.", "Index_signature_for_type_0_is_missing_in_type_1_2329": "'{1}' 형식에 인덱스 시그니처 유형 '{0}'이(가) 없습니다.", "Index_signature_in_type_0_only_permits_reading_2542": "'{0}' 형식의 인덱스 시그니처는 읽기만 허용됩니다.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "병합된 선언 '{0}'의 개별 선언은 모두 내보내 졌거나 모두 로컬이어야 합니다.", "Infer_all_types_from_usage_95023": "사용량에서 모든 형식 유추", "Infer_function_return_type_95148": "함수 반환 형식 유추", "Infer_parameter_types_from_usage_95012": "사용량에서 매개 변수 형식 유추", "Infer_this_type_of_0_from_usage_95080": "사용량에서 '{0}'의 'this' 형식 유추", "Infer_type_of_0_from_usage_95011": "사용량에서 '{0}'의 형식 유추", "Inference_from_class_expressions_is_not_supported_with_isolatedDeclarations_9022": "--isolatedDeclarations에서는 클래스 식의 유추가 지원되지 않습니다.", "Initialize_property_0_in_the_constructor_90020": "생성자에서 속성 '{0}' 초기화", "Initialize_static_property_0_90021": "정적 속성 '{0}' 초기화", "Initializer_for_property_0_2811": "'{0}' 속성에 대한 이니셜라이저", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "인스턴스 멤버 변수 '{0}'의 이니셜라이저는 생성자에 선언된 식별자 '{1}'을(를) 참조할 수 없습니다.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "앰비언트 컨텍스트에서는 이니셜라이저가 허용되지 않습니다.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "TypeScript 프로젝트를 초기화하고 tsconfig.json 파일을 만듭니다.", "Inline_variable_95184": "인라인 변수", "Insert_command_line_options_and_files_from_a_file_6030": "파일에서 명령줄 옵션 및 파일을 삽입합니다.", "Install_0_95014": "'{0}' 설치", "Install_all_missing_types_packages_95033": "누락된 형식 패키지 모두 설치", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "'{0}' 인터페이스는 '{1}' 및 '{2}' 형식을 동시에 확장할 수 없습니다.", "Interface_0_incorrectly_extends_interface_1_2430": "'{0}' 인터페이스가 '{1}' 인터페이스를 잘못 확장합니다.", "Interface_declaration_cannot_have_implements_clause_1176": "인터페이스 선언에는 'implements' 절을 사용할 수 없습니다.", "Interface_must_be_given_a_name_1438": "인터페이스에 이름을 지정해야 합니다.", "Interface_name_cannot_be_0_2427": "인터페이스 이름은 '{0}'일 수 없습니다.", "Interop_Constraints_6252": "Interop 제약 조건", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "'undefined'를 추가하는 대신 선택적 속성 형식을 작성된 것으로 해석합니다.", "Invalid_character_1127": "잘못된 문자입니다.", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "잘못된 가져오기 지정자 '{0}'에는 가능한 해결 방법이 없습니다.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "확대의 모듈 이름이 잘못되었습니다. '{1}'에서 '{0}' 모듈이 형식화되지 않은 모듈로 확인되어 확대할 수 없습니다.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "확대의 모듈 이름이 잘못되었습니다. '{0}' 모듈을 찾을 수 없습니다.", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "새 식의 선택적 체인이 잘못되었습니다. '{0}()'을 호출하시겠습니까?", "Invalid_reference_directive_syntax_1084": "'reference' 지시문 구문이 잘못되었습니다.", "Invalid_syntax_in_decorator_1498": "데코레이터의 구문이 잘못되었습니다.", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "'{0}'을(를) 잘못 사용했습니다. 해당 항목은 클래스 정적 블록 내에서 사용할 수 없습니다.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "'{0}'을(를) 잘못 사용했습니다. 모듈은 자동으로 strict 모드가 됩니다.", "Invalid_use_of_0_in_strict_mode_1100": "strict 모드에서 '{0}'을(를) 잘못 사용했습니다.", "Invalid_value_for_ignoreDeprecations_5103": "'--ignoreDeprecations'의 값이 잘못되었습니다.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "'jsxFactory'에 대한 값이 잘못되었습니다. '{0}'이(가) 올바른 식별자 또는 정규화된 이름이 아닙니다.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "'jsxFragmentFactory'의 값이 잘못되었습니다. '{0}'은(는) 올바른 식별자 또는 정규화된 이름이 아닙니다.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "'--reactNamespace'의 값이 잘못되었습니다. '{0}'은(는) 올바른 식별자가 아닙니다.", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "두 템플릿 식을 구분할 쉼표가 없는 것 같습니다. 이 두 템플릿 식은 태그가 지정된 호출 불가능한 하나의 템플릿 식을 구성합니다.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "해당 요소 형식 '{0}'은(는) 유효한 JSX 요소가 아닙니다.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "해당 인스턴스 형식 '{0}'은(는) 유효한 JSX 요소가 아닙니다.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "해당 반환 형식 '{0}'은(는) 유효한 JSX 요소가 아닙니다.", "Its_type_0_is_not_a_valid_JSX_element_type_18053": "해당 '{0}' 형식은 올바른 JSX 요소 형식이 아닙니다.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "JSDoc '@{0} {1}'이(가) 'extends {2}' 절과 일치하지 않습니다.", "JSDoc_0_is_not_attached_to_a_class_8022": "JSDoc '@{0}'이(가) 클래스에 연결되어 있지 않습니다.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc '...'은 시그니처의 마지막 매개 변수에만 나타날 수 있습니다.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "JSDoc '@param' 태그의 이름이 '{0}'인데 해당 이름의 매개 변수가 없습니다.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "JSDoc '@param' 태그에 '{0}' 이름이 있지만, 해당 이름의 매개 변수가 없습니다. 배열 형식이 있는 경우 '인수'를 일치시킵니다.", "JSDoc_typedef_may_be_converted_to_TypeScript_type_80009": "JSDoc typedef를 TypeScript 형식으로 변환할 수 있습니다.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "JSDoc '@typedef' 태그는 형식 주석을 포함하거나, '@property' 또는 '@member' 태그 앞에 와야 합니다.", "JSDoc_typedefs_may_be_converted_to_TypeScript_types_80010": "JSDoc typedef를 TypeScript 형식으로 변환할 수 있습니다.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "JSDoc 유형은 문서 주석 내에서만 사용될 수 있습니다.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "JSDoc 형식이 TypeScript 형식으로 이동될 수 있습니다.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "JSX 특성에는 비어 있지 않은 '식'만 할당할 수 있습니다.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "JSX 요소 '{0}'에 닫는 태그가 없습니다.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "JSX 요소 클래스는 '{0}' 속성이 없으므로 특성을 지원하지 않습니다.", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "'JSX.{0}' 인터페이스가 없으므로 JSX 요소는 암시적으로 'any' 형식입니다.", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "전역 형식 'JSX.Element'가 없으므로 JSX 요소는 암시적으로 'any' 형식입니다.", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "JSX 요소 형식 '{0}'에 구문 또는 호출 시그니처가 없습니다.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "JSX 요소에 이름이 같은 특성을 여러 개 사용할 수 없습니다.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "JSX 식은 쉼표 연산자를 사용할 수 없습니다. 배열을 작성하시겠습니까?", "JSX_expressions_must_have_one_parent_element_2657": "JSX 식에는 부모 요소가 하나 있어야 합니다.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "JSX 조각에 닫는 태그가 없습니다.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "JSX 속성 액세스 식은 JSX 네임스페이스 이름을 포함할 수 없습니다.", "JSX_spread_child_must_be_an_array_type_2609": "JSX 분배 자식은 배열 형식이어야 합니다.", "JavaScript_Support_6247": "JavaScript 지원", "Jump_target_cannot_cross_function_boundary_1107": "점프 대상은 함수 경계를 벗어날 수 없습니다.", "KIND_6034": "KIND", "Keywords_cannot_contain_escape_characters_1260": "키워드에는 이스케이프 문자를 사용할 수 없습니다.", "LOCATION_6037": "위치", "Language_and_Environment_6254": "언어 및 환경", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "쉼표 연산자의 왼쪽은 사용되지 않으며 이로 인해 의도하지 않은 결과가 발생하지 않습니다.", "Library_0_specified_in_compilerOptions_1422": "compilerOptions에 '{0}' 라이브러리가 지정되었습니다.", "Library_referenced_via_0_from_file_1_1405": "'{1}' 파일에서 '{0}'을(를) 통해 라이브러리가 참조되었습니다.", "Line_break_not_permitted_here_1142": "여기서는 줄 바꿈이 허용되지 않습니다.", "Line_terminator_not_permitted_before_arrow_1200": "줄 마침 표시는 화살표 앞에 사용할 수 없습니다.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "모듈을 확인할 때 검색할 파일 이름 접미사 목록입니다.", "List_of_folders_to_include_type_definitions_from_6161": "포함할 형식 정의가 있는 폴더의 목록입니다.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "런타임에 프로젝트의 구조를 나타내는 결합된 콘텐츠가 있는 루트 폴더의 목록입니다.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "루트 디렉터리 '{1}'에서 '{0}'을(를) 로드하고 있습니다. 후보 위치: '{2}'.", "Loading_module_0_from_node_modules_folder_target_file_types_Colon_1_6098": "'node_modules' 폴더에서 '{0}' 모듈을 로드하고 있습니다. 대상 파일 형식은 '{1}'입니다.", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_types_Colon_1_6095": "모듈을 파일/폴더로 로드하고 있습니다. 후보 모듈 위치는 '{0}', 대상 파일 형식은 '{1}'입니다.", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "로캘이 <language> 또는 <language>-<territory> 형식이어야 합니다. 예를 들어 '{0}' 또는 '{1}'입니다.", "Log_paths_used_during_the_moduleResolution_process_6706": "'moduleResolution' 프로세스 동안 사용된 로그 경로입니다.", "Longest_matching_prefix_for_0_is_1_6108": "'{0}'에 대해 일치하는 가장 긴 접두사는 '{1}'입니다.", "Looking_up_in_node_modules_folder_initial_location_0_6125": "'node_modules' 폴더에서 찾고 있습니다. 초기 위치: '{0}'.", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "모든 'super()' 호출을 생성자의 첫 번째 문으로 만들기", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "keyof가 문자열, 숫자 또는 기호 대신 문자열만 반환하도록 합니다. 레거시 옵션입니다.", "Make_super_call_the_first_statement_in_the_constructor_90002": "생성자의 첫 번째 문을 'super()'로 호출", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "매핑된 개체 형식에는 'any' 템플릿 형식이 암시적으로 포함됩니다.", "Mark_array_literal_as_const_90070": "배열 리터럴을 const로 표시", "Matched_0_condition_1_6403": "'{0}' 조건 '{1}'과(와) 일치합니다.", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "기본적으로 일치되는 포함 패턴 '**/*'", "Matched_by_include_pattern_0_in_1_1407": "'{1}'의 포함 패턴 '{0}'(으)로 일치되었습니다.", "Member_0_implicitly_has_an_1_type_7008": "'{0}' 멤버에는 암시적으로 '{1}' 형식이 포함됩니다.", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "'{0}' 멤버는 암시적으로 '{1}' 형식이지만, 사용량에서 더 나은 형식을 유추할 수 있습니다.", "Merge_conflict_marker_encountered_1185": "병합 충돌 표식을 발견했습니다.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "병합된 선언 '{0}'에는 기본 내보내기 선언을 포함할 수 없습니다. 대신 별도의 'export default {0}' 선언을 추가하세요.", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "메타 속성 '{0}'은(는) 함수 선언, 함수 식 또는 생성기의 본문에서만 사용할 수 있습니다.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "'{0}' 메서드는 abstract로 표시되어 있으므로 구현이 있을 수 없습니다.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "내보낸 인터페이스의 '{0}' 메서드가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "내보낸 인터페이스의 '{0}' 메서드가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Method_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9008": "메서드에는 --isolatedDeclarations가 있는 명시적 반환 형식 주석이 있어야 합니다.", "Method_not_implemented_95158": "메서드가 구현되지 않았습니다.", "Modifiers_cannot_appear_here_1184": "한정자를 여기에 표시할 수 없습니다.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "'{0}' 모듈은 '{1}' 플래그를 사용하는 가져온 기본값이어야만 합니다.", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "이 구문을 사용하여 '{0}' 모듈을 가져올 수 없습니다. 지정자는 'require'로 가져올 수 없는 ES 모듈로만 확인됩니다. ECMAScript 가져오기를 대신 사용하세요.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "'{0}' 모듈은 '{1}'을(를) 로컬로 선언하지만, 모듈을 '{2}'(으)로 내보냅니다.", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "'{0}' 모듈은 '{1}'을(를) 로컬로 선언하지만, 모듈을 내보내지 않습니다.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "모듈 '{0}'은(는) 형식을 참조하지 않지만, 여기에서 형식으로 사용됩니다. 'typeof 가져오기('{0}')'를 사용하시겠습니까?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "모듈 '{0}'은(는) 값을 참조하지 않지만, 여기에서 값으로 사용됩니다.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "{0} 모듈에서 '{1}'(이)라는 멤버를 이미 내보냈습니다. 모호성을 해결하려면 명시적으로 다시 내보내는 것이 좋습니다.", "Module_0_has_no_default_export_1192": "모듈 '{0}'에는 기본 내보내기가 없습니다.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "'{0}' 모듈에 기본 내보내기가 없습니다. 대신 '{0}에서 { {1} } 가져오기'를 사용하시겠습니까?", "Module_0_has_no_exported_member_1_2305": "'{0}' 모듈에 내보낸 멤버 '{1}'이(가) 없습니다.", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "'{0}' 모듈에 내보낸 멤버 '{1}'이(가) 없습니다. 대신 '{0}에서 {1} 가져오기'를 사용하시겠습니까?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "'{0}' 모듈은 이름이 같은 로컬 선언으로 숨겨집니다.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "모듈 '{0}'은(는) 'export ='을 사용하며 'export *'와 함께 사용할 수 없습니다.", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "'{0}' 모듈은 '{1}'에서 지역으로 선언된 앰비언트 모듈로 확인되었습니다.", "Module_0_was_resolved_to_1_but_allowArbitraryExtensions_is_not_set_6263": "'{0}' 모듈이 '{1}'(으)로 확인되었지만 '--allowArbitraryExtensions'가 설정되지 않았습니다.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "모듈 '{0}'이(가) '{1}'(으)로 확인되었지만 '--jsx'가 설정되지 않았습니다.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "'{0}' 모듈이 '{1}'(으)로 확인되었지만 '--resolveJsonModule'이 사용되지 않았습니다.", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "모듈 선언 이름에는 ' 또는 \" 인용 문자열만 사용할 수 있습니다.", "Module_name_0_matched_pattern_1_6092": "모듈 이름: '{0}', 일치하는 패턴: '{1}'", "Module_name_0_was_not_resolved_6090": "======== 모듈 이름 '{0}'이(가) 확인되지 않았습니다. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== 모듈 이름 '{0}'이(가) '{1}'(으)로 확인되었습니다. ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== 모듈 이름 '{0}'이(가) 패키지 ID가 '{2}'인 '{1}'(으)로 확인되었습니다. ========", "Module_resolution_kind_is_not_specified_using_0_6088": "모듈 확인 종류가 지정되지 않았습니다. '{0}'을(를) 사용합니다.", "Module_resolution_using_rootDirs_has_failed_6111": "'rootDirs'를 사용한 모듈 확인에 실패했습니다.", "Modules_6244": "모듈", "Move_labeled_tuple_element_modifiers_to_labels_95117": "레이블이 지정된 튜플 요소 한정자를 레이블로 이동", "Move_the_expression_in_default_export_to_a_variable_and_add_a_type_annotation_to_it_9036": "기본 내보내기에서 식을 변수로 이동하고 형식 주석을 추가합니다.", "Move_to_a_new_file_95049": "새 파일로 이동", "Move_to_file_95178": "파일로 이동", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "여러 개의 연속된 숫자 구분 기호는 허용되지 않습니다.", "Multiple_constructor_implementations_are_not_allowed_2392": "여러 생성자 구현은 허용되지 않습니다.", "NEWLINE_6061": "줄 바꿈", "Name_is_not_valid_95136": "이름이 잘못되었습니다.", "Named_capturing_groups_are_only_available_when_targeting_ES2018_or_later_1503": "명명된 캡처 그룹은 'ES2018' 이상을 대상으로 하는 경우에만 사용할 수 있습니다.", "Named_capturing_groups_with_the_same_name_must_be_mutually_exclusive_to_each_other_1515": "이름이 같은 명명된 캡처 그룹은 상호 배타적이어야 합니다.", "Named_imports_from_a_JSON_file_into_an_ECMAScript_module_are_not_allowed_when_module_is_set_to_0_1544": "'module'이 '{0}'(으)로 설정된 경우 JSON 파일에서 ECMAScript 모듈로 명명된 가져오기를 사용할 수 없습니다.", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "명명된 속성 '{0}'의 형식 '{1}' 및 '{2}'이(가) 동일하지 않습니다.", "Namespace_0_has_no_exported_member_1_2694": "'{0}' 네임스페이스에 내보낸 멤버 '{1}'이(가) 없습니다.", "Namespace_must_be_given_a_name_1437": "네임스페이스에 이름을 지정해야 합니다.", "Namespace_name_cannot_be_0_2819": "네임스페이스 이름은 '{0}'일 수 없습니다.", "Namespaces_are_not_allowed_in_global_script_files_when_0_is_enabled_If_this_file_is_not_intended_to__1280": "'{0}'이(가) 사용하도록 설정된 경우 전역 스크립트 파일에서는 네임스페이스를 사용할 수 없습니다. 이 파일이 전역 스크립트가 아닌 경우 'moduleDetection'을 'force'로 설정하거나 빈 'export {}' 문을 추가합니다.", "Neither_decorators_nor_modifiers_may_be_applied_to_this_parameters_1433": "데코레이터와 한정자를 'this' 매개 변수에 적용할 수 없습니다.", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "기본 생성자에 지정된 수의 형식 인수가 없습니다.", "No_constituent_of_type_0_is_callable_2755": "'{0}' 형식의 구성원을 호출할 수 없습니다.", "No_constituent_of_type_0_is_constructable_2759": "'{0}' 형식의 구성원을 생성할 수 없습니다.", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "'{1}' 형식에서 '{0}' 형식의 매개 변수가 포함된 인덱스 시그니처를 찾을 수 없습니다.", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "'{0}' 구성 파일에서 입력을 찾을 수 없습니다. 지정된 '포함' 경로는 '{1}'이고 '제외' 경로는 '{2}'이었습니다.", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "더 이상 지원되지 않습니다. 초기 버전에서는 파일을 읽기 위한 텍스트 인코딩을 수동으로 설정합니다.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "오버로드에 {0} 인수가 필요하지 않지만, {1} 또는 {2} 인수가 필요한 오버로드가 있습니다.", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "오버로드에 {0} 형식 인수가 필요하지 않지만, {1} 또는 {2} 형식 인수가 필요한 오버로드가 있습니다.", "No_overload_matches_this_call_2769": "이 호출과 일치하는 오버로드가 없습니다.", "No_type_could_be_extracted_from_this_type_node_95134": "이 형식 노드에서 형식을 추출할 수 없습니다.", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "줄임 속성 '{0}'의 범위에 값이 없습니다. 값을 선언하거나 이니셜라이저를 제공합니다.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "'{0}' 비추상 클래스는 '{2}' 클래스에서 상속된 추상 멤버 '{1}'을(를) 구현하지 않습니다.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_2654": "비추상 클래스 '{0}'에 '{1}'의 다음 멤버에 대한 구현이 없습니다. {2}.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_and_3_more_2655": "비추상 클래스 '{0}'에 '{1}'의 다음 멤버에 대한 구현이 없습니다. {2} 및 {3} 등.", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "비추상 클래스 식은 '{1}' 클래스에서 상속된 추상 멤버 '{0}'을(를) 구현하지 않습니다.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_2656": "비추상 클래스 식에 '{0}'의 다음 멤버에 대한 구현이 없습니다. {1}.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_and__2650": "비추상 클래스 식에 '{0}'의 다음 멤버에 대한 구현이 없습니다. {1} 및 {2} 등.", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "null이 아닌 어설션은 TypeScript 파일에서만 사용할 수 있습니다.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "'baseUrl'이 설정되지 않은 경우 상대 경로가 아닌 경로는 허용되지 않습니다. 선행 './'를 잊으셨습니까?", "Non_simple_parameter_declared_here_1348": "여기서 단순하지 않은 매개 변수가 선언되었습니다.", "Not_all_code_paths_return_a_value_7030": "일부 코드 경로가 값을 반환하지 않습니다.", "Not_all_constituents_of_type_0_are_callable_2756": "'{0}' 형식의 일부 구성원을 호출할 수 없습니다.", "Not_all_constituents_of_type_0_are_constructable_2760": "'{0}' 형식의 일부 구성원을 생성할 수 없습니다.", "Numbers_out_of_order_in_quantifier_1506": "수량자의 숫자가 순서를 벗어났습니다.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "절대값이 2^53보다 크거나 같은 숫자 리터럴은 너무 커서 정수로 정확하게 표시할 수 없습니다.", "Numeric_separators_are_not_allowed_here_6188": "숫자 구분 기호는 여기에서 허용되지 않습니다.", "Object_is_of_type_unknown_2571": "개체가 '알 수 없는' 형식입니다.", "Object_is_possibly_null_2531": "개체가 'null'인 것 같습니다.", "Object_is_possibly_null_or_undefined_2533": "개체가 'null' 또는 'undefined'인 것 같습니다.", "Object_is_possibly_undefined_2532": "개체가 'undefined'인 것 같습니다.", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "개체 리터럴은 알려진 속성만 지정할 수 있으며 '{1}' 형식에 '{0}'이(가) 없습니다.", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "개체 리터럴은 알려진 속성만 지정할 수 있지만 '{1}' 형식에 '{0}'이(가) 없습니다. '{2}'을(를) 쓰려고 했습니까?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "개체 리터럴의 '{0}' 속성에는 암시적으로 '{1}' 형식이 포함됩니다.", "Objects_that_contain_shorthand_properties_can_t_be_inferred_with_isolatedDeclarations_9016": "약식 속성이 포함된 개체는 --isolatedDeclarations를 사용하여 유추할 수 없습니다.", "Objects_that_contain_spread_assignments_can_t_be_inferred_with_isolatedDeclarations_9015": "분산 할당이 포함된 개체는 --isolatedDeclarations를 사용하여 유추할 수 없습니다.", "Octal_digit_expected_1178": "8진수가 있어야 합니다.", "Octal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_If_this_was_intended__1536": "문자 클래스에서는 8진수 이스케이프 시퀀스 및 역참조를 사용할 수 없습니다. 이 시퀀스가 이스케이프 시퀀스로 의도된 경우 대신 '{0}' 구문을 사용합니다.", "Octal_escape_sequences_are_not_allowed_Use_the_syntax_0_1487": "8진수 이스케이프 시퀀스는 허용되지 않습니다. '{0}' 구문을 사용합니다.", "Octal_literals_are_not_allowed_Use_the_syntax_0_1121": "8진수 리터럴은 허용되지 않습니다. '{0}' 구문을 사용합니다.", "One_value_of_0_1_is_the_string_2_and_the_other_is_assumed_to_be_an_unknown_numeric_value_4126": "'{0}.{1}'의 한 값은 '{2}' 문자열이며, 다른 값은 알 수 없는 숫자 값으로 간주됩니다.", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "'for...in' 문에는 단일 변수 선언만 허용됩니다.", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "'for...of' 문에는 단일 변수 선언만 허용됩니다.", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "void 함수만 'new' 키워드로 호출할 수 있습니다.", "Only_ambient_modules_can_use_quoted_names_1035": "앰비언트 모듈만 따옴표가 붙은 이름을 사용할 수 있습니다.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "'amd' 및 'system' 모듈만 --{0}과(와) 함께 사용할 수 있습니다.", "Only_const_arrays_can_be_inferred_with_isolatedDeclarations_9017": "const 배열만 --isolatedDeclarations를 사용하여 유추할 수 있습니다.", "Only_emit_d_ts_declaration_files_6014": "'.d.ts' 선언 파일만 내보냅니다.", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "JavaScript 파일은 출력하지 않고 d.ts 파일만 출력합니다.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "기본 클래스의 공용 및 보호된 메서드만 'super' 키워드를 통해 액세스할 수 있습니다.", "Operator_0_cannot_be_applied_to_type_1_2736": "'{0}' 연산자는 '{1}' 형식에 적용할 수 없습니다.", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "'{0}' 연산자를 '{1}' 및 '{2}' 형식에 적용할 수 없습니다.", "Operators_must_not_be_mixed_within_a_character_class_Wrap_it_in_a_nested_class_instead_1519": "연산자는 문자 클래스 내에서 혼합될 수 없습니다. 대신 중첩 클래스에서 래핑합니다.", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "편집할 때 다중 프로젝트 참조 검사에서 프로젝트를 옵트아웃합니다.", "Option_0_1_has_been_removed_Please_remove_it_from_your_configuration_5108": "'{0}={1}' 옵션이 제거되었습니다. 구성에서 제거하세요.", "Option_0_1_is_deprecated_and_will_stop_functioning_in_TypeScript_2_Specify_compilerOption_ignoreDepr_5107": "'{0}={1}' 옵션은 더 이상 사용되지 않으며 TypeScript {2}에서 작동하지 않습니다. 이 오류를 무시하려면 compilerOption '\"ignoreDeprecations\": \"{3}\"'을(를) 지정합니다.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "'{0}' 옵션은 'tsconfig. json' 파일에만 지정하거나 명령줄에서 'false' 또는 'null'로 설정할 수 있습니다.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "'{0}' 옵션은 'tsconfig. json' 파일에만 지정하거나 명령줄에서 'null'로 설정할 수 있습니다.", "Option_0_can_only_be_specified_on_command_line_6266": "'{0}' 옵션은 명령줄에서만 지정할 수 있습니다.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "'{0}' 옵션은 '--inlineSourceMap' 옵션 또는 '--sourceMap' 옵션이 제공되는 경우에만 사용할 수 있습니다.", "Option_0_can_only_be_used_when_moduleResolution_is_set_to_node16_nodenext_or_bundler_5098": "'moduleResolution'이 'node16', 'nodenext' 또는 'bundler'로 설정된 경우에만 '{0}' 옵션을 사용할 수 있습니다.", "Option_0_can_only_be_used_when_module_is_set_to_preserve_or_to_es2015_or_later_5095": "'{0}' 옵션은 'module'이 'preserve' 또는 'es2015' 이상으로 설정된 경우에만 사용할 수 있습니다.", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "'jsx' 옵션이 '{1}'인 경우 '{0}' 옵션을 지정할 수 없습니다.", "Option_0_cannot_be_specified_with_option_1_5053": "'{0}' 옵션은 '{1}' 옵션과 함께 지정할 수 없습니다.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "'{1}' 옵션을 지정하지 않고 '{0}' 옵션을 지정할 수 없습니다.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "'{1}' 또는 '{2}' 옵션을 지정하지 않고 '{0}' 옵션을 지정할 수 없습니다.", "Option_0_has_been_removed_Please_remove_it_from_your_configuration_5102": "‘{0}’ 옵션이 제거되었습니다. 구성에서 제거하세요.", "Option_0_is_deprecated_and_will_stop_functioning_in_TypeScript_1_Specify_compilerOption_ignoreDeprec_5101": "'{0}’ 옵션은 더 이상 사용되지 않으며 TypeScript {1}에서 작동하지 않습니다. 이 오류를 무시하려면 compilerOption '\"ignoreDeprecations\": \"{2}\"'을(를) 지정합니다.", "Option_0_is_redundant_and_cannot_be_specified_with_option_1_5104": "'{0}' 옵션은 중복되므로 '{1}' 옵션으로 지정할 수 없습니다.", "Option_allowImportingTsExtensions_can_only_be_used_when_either_noEmit_or_emitDeclarationOnly_is_set_5096": "'allowImportingTsExtensions' 옵션은 'noEmit' 또는 'emitDeclarationOnly'가 설정된 경우에만 사용할 수 있습니다.", "Option_build_must_be_the_first_command_line_argument_6369": "'--build' 옵션은 첫 번째 명령줄 인수여야 합니다.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "'--incremental' 옵션은 tsconfig를 사용하거나 단일 파일로 내보내서 지정하거나 '--tsBuildInfoFile' 옵션을 지정할 때만 지정할 수 있습니다.", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "'isolatedModules' 옵션은 '--module' 옵션을 지정하거나 'target' 옵션이 'ES2015' 이상인 경우에만 사용할 수 있습니다.", "Option_moduleResolution_must_be_set_to_0_or_left_unspecified_when_option_module_is_set_to_1_5109": "'module' 옵션이 ‘{1}'(으)로 설정된 경우 'moduleResolution' 옵션을 '{0}'(또는 지정되지 않은 상태로 두어야 함)(으)로 설정해야 합니다.", "Option_module_must_be_set_to_0_when_option_moduleResolution_is_set_to_1_5110": "'moduleResolution' 옵션이 '{1}'으(로) 설정된 경우 'module' 옵션을 '{0}'(으)로 설정해야 합니다.", "Option_preserveConstEnums_cannot_be_disabled_when_0_is_enabled_5091": "'{0}'이(가) 사용하도록 설정된 경우 'preserveConstEnums' 옵션을 사용하지 않도록 설정할 수 없습니다.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "명령줄에서 'project' 옵션을 원본 파일과 혼합하여 사용할 수 없습니다.", "Option_resolveJsonModule_cannot_be_specified_when_moduleResolution_is_set_to_classic_5070": "'moduleResolution'이 'classic'으로 설정된 경우 '--resolveJsonModule' 옵션을 지정할 수 없습니다.", "Option_resolveJsonModule_cannot_be_specified_when_module_is_set_to_none_system_or_umd_5071": "'module'이 'none', 'system' 또는 'umd'로 설정된 경우 '--resolveJsonModule' 옵션을 지정할 수 없습니다.", "Option_verbatimModuleSyntax_cannot_be_used_when_module_is_set_to_UMD_AMD_or_System_5105": "'module'이 'UMD', 'AMD' 또는 'System'으로 설정된 경우 'verbatimModuleSyntax' 옵션을 사용할 수 없습니다.", "Options_0_and_1_cannot_be_combined_6370": "'{0}' 및 '{1}' 옵션은 조합할 수 없습니다.", "Options_Colon_6027": "옵션:", "Output_Formatting_6256": "출력 서식 지정", "Output_compiler_performance_information_after_building_6615": "빌드 후 컴파일러 성능 정보를 출력합니다.", "Output_directory_for_generated_declaration_files_6166": "생성된 선언 파일의 출력 디렉터리입니다.", "Output_file_0_has_not_been_built_from_source_file_1_6305": "출력 파일 '{0}'이(가) 소스 파일 '{1}'에서 빌드되지 않았습니다.", "Output_from_referenced_project_0_included_because_1_specified_1411": "'{1}'이(가) 지정되었기 때문에 참조된 프로젝트 '{0}'의 출력이 포함됩니다.", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "'--module'이 'none'으로 지정되었기 때문에 참조된 프로젝트 '{0}'의 출력이 포함됩니다.", "Output_more_detailed_compiler_performance_information_after_building_6632": "빌드 후 더 자세한 컴파일러 성능 정보를 출력합니다.", "Overload_0_of_1_2_gave_the_following_error_2772": "오버로드 {0}/{1}('{2}')에서 다음 오류가 발생했습니다.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "오버로드 시그니처는 모두 추상이거나 비추상이어야 합니다.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "오버로드 시그니처는 모두 앰비언트이거나 앰비언트가 아니어야 합니다.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "오버로드 시그니처는 모두 내보내거나 모두 내보내지 않아야 합니다.", "Overload_signatures_must_all_be_optional_or_required_2386": "오버로드 시그니처는 모두 선택 사항이거나 필수 사항이어야 합니다.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "오버로드 시그니처는 모두 퍼블릭, 프라이빗 또는 보호된 상태여야 합니다.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "매개 변수 '{0}'은(는) 이 매개 변수 뒤에 선언된 식별자 '{1}'을(를) 참조할 수 없습니다.", "Parameter_0_cannot_reference_itself_2372": "매개 변수 '{0}'은(는) 자신을 참조할 수 없습니다.", "Parameter_0_implicitly_has_an_1_type_7006": "'{0}' 매개 변수에는 암시적으로 '{1}' 형식이 포함됩니다.", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "'{0}' 매개 변수는 암시적으로 '{1}' 형식이지만, 사용량에서 더 나은 형식을 유추할 수 있습니다.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "'{0}' 매개 변수는 '{1}' 매개 변수와 같은 위치에 있지 않습니다.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "접근자의 '{0}' 매개 변수가 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "접근자의 '{0}' 매개 변수가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "접근자의 '{0}' 매개 변수가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "내보낸 인터페이스에 있는 호출 시그니처의 '{0}' 매개 변수가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "내보낸 인터페이스에 있는 호출 시그니처의 '{0}' 매개 변수가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "내보낸 클래스에 있는 생성자의 '{0}' 매개 변수가 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "내보낸 클래스에 있는 생성자의 '{0}' 매개 변수가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "내보낸 클래스에 있는 생성자의 '{0}' 매개 변수가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "내보낸 인터페이스에 있는 생성자 시그니처의 '{0}' 매개 변수가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "내보낸 인터페이스에 있는 생성자 시그니처의 '{0}' 매개 변수가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "내보낸 함수의 '{0}' 매개 변수가 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "내보낸 함수의 '{0}' 매개 변수가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "내보낸 함수의 '{0}' 매개 변수가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "내보낸 인터페이스에 있는 인덱스 시그니처의 '{0}' 매개 변수가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "내보낸 인터페이스에 있는 인덱스 시그니처의 '{0}' 매개 변수가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "내보낸 인터페이스에 있는 메서드의 '{0}' 매개 변수가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "내보낸 인터페이스에 있는 메서드의 '{0}' 매개 변수가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "내보낸 클래스에 있는 공용 메서드의 '{0}' 매개 변수가 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "내보낸 클래스에 있는 공용 메서드의 '{0}' 매개 변수가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "내보낸 클래스에 있는 공용 메서드의 '{0}' 매개 변수가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "내보낸 클래스에 있는 공용 정적 메서드의 '{0}' 매개 변수가 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "내보낸 클래스에 있는 공용 정적 메서드의 '{0}' 매개 변수가 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "내보낸 클래스에 있는 공용 정적 메서드의 '{0}' 매개 변수가 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_cannot_have_question_mark_and_initializer_1015": "매개 변수에 물음표와 이니셜라이저를 사용할 수 없습니다.", "Parameter_declaration_expected_1138": "매개 변수 선언이 필요합니다.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "매개 변수에 이름이 있지만 형식이 없습니다. '{0}: {1}'을(를) 사용하시겠습니까?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "매개 변수 한정자는 TypeScript 파일에서만 사용할 수 있습니다.", "Parameter_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9011": "매개 변수에는 --isolatedDeclarations가 있는 명시적 형식 주석이 있어야 합니다.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "내보낸 클래스에 있는 공용 setter '{0}'의 매개 변수 형식이 프라이빗 모듈 '{2}'의 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "내보낸 클래스에 있는 공용 setter '{0}'의 매개 변수 형식이 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "내보낸 클래스에 있는 공용 정적 setter '{0}'의 매개 변수 형식이 프라이빗 모듈 '{2}'의 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "내보낸 클래스에 있는 공용 정적 setter '{0}'의 매개 변수 형식이 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "strict 모드에서 구문 분석하고 각 소스 파일에 대해 \"use strict\"를 내보냅니다.", "Part_of_files_list_in_tsconfig_json_1409": "tsconfig.json의 'files' 목록의 일부", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "'{0}' 패턴에는 '*' 문자를 최대 하나만 사용할 수 있습니다.", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "이 세션에서는 '--diagnostics' 또는 '--extendedDiagnostics'에 대해 성능 타이밍을 사용할 수 없습니다. Web Performance API의 네이티브 구현을 찾을 수 없습니다.", "Platform_specific_6912": "플랫폼별", "Prefix_0_with_an_underscore_90025": "'{0}' 앞에 밑줄 추가", "Prefix_all_incorrect_property_declarations_with_declare_95095": "모든 잘못된 속성 선언에 'declare'를 접두사로 추가", "Prefix_all_unused_declarations_with_where_possible_95025": "가능한 경우 사용하지 않는 모든 선언에 '_'을 접두사로 추가", "Prefix_with_declare_95094": "'declare'를 접두사로 추가", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "JavaScript 출력에서 사용되지 않는 가져온 값을 유지합니다. 그렇지 않으면 제거됩니다.", "Print_all_of_the_files_read_during_the_compilation_6653": "컴파일 중에 읽은 모든 파일을 출력합니다.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "컴파일 중에 읽은 파일을 포함 이유와 함께 출력합니다.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "파일의 이름과 해당 파일이 컴파일에 포함된 이유를 출력합니다.", "Print_names_of_files_part_of_the_compilation_6155": "컴파일의 일부인 파일의 이름을 인쇄합니다.", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "컴파일에 포함된 파일의 이름을 인쇄한 다음, 처리를 중지합니다.", "Print_names_of_generated_files_part_of_the_compilation_6154": "컴파일의 일부인 생성된 파일의 이름을 인쇄합니다.", "Print_the_compiler_s_version_6019": "컴파일러 버전을 인쇄합니다.", "Print_the_final_configuration_instead_of_building_1350": "빌드 대신 최종 구성을 인쇄합니다.", "Print_the_names_of_emitted_files_after_a_compilation_6652": "컴파일 후 내보낸 파일의 이름을 출력합니다.", "Print_this_message_6017": "이 메시지를 출력합니다.", "Private_accessor_was_defined_without_a_getter_2806": "프라이빗 접근자가 getter 없이 정의되었습니다.", "Private_field_0_must_be_declared_in_an_enclosing_class_1111": "'{0}' 프라이빗 필드는 외부 클래스에서 선언되어야 합니다.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "변수 선언에서 프라이빗 식별자를 사용할 수 없습니다.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "클래스 본문 외부에서 프라이빗 식별자를 사용할 수 없습니다.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "비공개 식별자는 클래스 본문에서만 허용되며 클래스 멤버 선언의 일부, 속성 액세스 또는 'in' 식의 왼쪽에서만 사용할 수 있습니다.", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "프라이빗 식별자는 ECMAScript 2015 이상을 대상으로 지정할 때만 사용할 수 있습니다.", "Private_identifiers_cannot_be_used_as_parameters_18009": "프라이빗 식별자는 매개 변수로 사용할 수 없습니다.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "형식 매개 변수에서 프라이빗 또는 보호된 멤버 '{0}'에 액세스할 수 없습니다.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Project '{0}' can't be built because its dependency '{1}' has errors", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Project '{0}' can't be built because its dependency '{1}' was not built", "Project_0_is_being_forcibly_rebuilt_6388": "'{0}' 프로젝트가 강제로 재구축되고 있습니다.", "Project_0_is_out_of_date_because_1_6420": "{1} 때문에 ' {0}' 프로젝트가 최신 상태가 아닙니다.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_file_2_was_root_file_of_compilation_6412": "'{1}' buildinfo 파일이 '{2}' 파일이 컴파일의 루트 파일이었지만 더 이상 아님을 나타내므로 '{0}' 프로젝트는 최신 상태가 아닙니다.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_program_needs_to_report_errors_6419": "'{1}' buildinfo 파일은 프로그램에서 오류를 보고해야 함을 나타내기 때문에 '{0}' 프로젝트는 최신 상태가 아닙니다.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "buildinfo 파일 '{1}'이(가) 일부 변경 내용이 내보내지지 않았음을 나타내므로 프로젝트 '{0}'은(는) 최신 상태가 아닙니다.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_there_is_change_in_compilerOptions_6406": "'{1}' buildinfo 파일이 compilerOptions에 변경이 있음을 나타내므로 '{0}' 프로젝트는 최신 상태가 아닙니다.", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "'{1}' 종속성이 최신 상태가 아니기 때문에 '{0}' 프로젝트가 최신 상태가 아닙니다.", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "출력 '{1}'이(가) 최신 입력 '{2}'보다 오래되었기 때문에 '{0}' 프로젝트가 최신 상태가 아닙니다.", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "'{1}' 출력 파일이 존재하지 않기 때문에 '{0}' 프로젝트가 최신 상태가 아닙니다.", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "'{0}' 프로젝트의 출력이 현재 버전 '{2}'과(와) 다른 '{1}' 버전으로 생성되었기 때문에 이 프로젝트가 최신 상태가 아닙니다.", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "'{1}' 파일을 읽는 동안 오류가 발생하여 프로젝트 '{0}'이(가) 만료되었습니다.", "Project_0_is_up_to_date_6361": "'{0}' 프로젝트가 최신 상태입니다.", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "최신 입력 '{1}'이(가) 출력 '{2}'보다 오래되었기 때문에 '{0}' 프로젝트가 최신 상태입니다.", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "프로젝트 '{0}'이(가) 최신 상태이지만 입력 파일보다 오래된 출력 파일의 타임스탬프를 업데이트해야 합니다.", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "'{0}' 프로젝트는 종속성에 .d.ts 파일이 있는 최신 상태입니다.", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "프로젝트 참조는 순환 그래프를 형성할 수 없습니다. 순환이 발견되었습니다. {0}", "Projects_6255": "프로젝트", "Projects_in_this_build_Colon_0_6355": "이 빌드의 프로젝트: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "'accessor' 한정자가 있는 속성은 ECMAScript 2015 이상을 대상으로 하는 경우에만 사용할 수 있습니다.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "'{0}' 속성은 추상으로 표시되어 있으므로 이니셜라이저를 사용할 수 없습니다.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "'{0}' 속성은 인덱스 시그니처에서 가져오는 것이므로 ['{0}']을(를) 사용하여 액세스해야 합니다.", "Property_0_does_not_exist_on_type_1_2339": "'{1}' 형식에 '{0}' 속성이 없습니다.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "'{0}' 속성이 '{1}' 형식에 없습니다. '{2}'을(를) 사용하시겠습니까?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "'{0}' 속성이 '{1}' 형식에 없습니다. 대신 정적 멤버 '{2}'에 액세스하려고 하셨습니까?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "'{0}' 속성이 '{1}' 형식에 없습니다. 대상 라이브러리를 변경해야 하는 경우 'lib' 컴파일러 옵션을 '{2}' 이상으로 변경해 보세요.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "속성 '{0}'(은)는 '{1}'에 없습니다. 'lib' 컴파일러 옵션을 변경하여 'dom'을 포함하세요.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "'{0}' 속성에 이니셜라이저가 없으며 클래스 정적 블록에 확실히 할당되지 않았습니다.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "속성 '{0}'은(는) 이니셜라이저가 없고 생성자에 할당되어 있지 않습니다.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "'{0}' 속성에는 해당 get 접근자에 반환 형식 주석이 없으므로 암시적으로 'any' 형식이 포함됩니다.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "'{0}' 속성에는 해당 set 접근자에 매개 변수 형식 주석이 없으므로 암시적으로 'any' 형식이 포함됩니다.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "'{0}' 속성은 암시적으로 'any' 형식이지만, 사용량에서 get 접근자의 더 나은 형식을 유추할 수 있습니다.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "'{0}' 속성은 암시적으로 'any' 형식이지만, 사용량에서 set 접근자의 더 나은 형식을 유추할 수 있습니다.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "'{1}' 형식의 '{0}' 속성을 기본 형식 '{2}'의 동일한 속성에 할당할 수 없습니다.", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "'{1}' 형식의 '{0}' 속성을 '{2}' 형식에 할당할 수 없습니다.", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "'{1}' 형식의 '{0}' 속성이 '{2}' 형식 내에서 액세스할 수 없는 다른 멤버를 참조합니다.", "Property_0_is_declared_but_its_value_is_never_read_6138": "속성 '{0}'이(가) 선언은 되었지만 해당 값이 읽히지는 않았습니다.", "Property_0_is_incompatible_with_index_signature_2530": "'{0}' 속성이 인덱스 시그니처와 호환되지 않습니다.", "Property_0_is_missing_in_type_1_2324": "'{0}' 속성이 '{1}' 형식에 없습니다.", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "'{0}' 속성이 '{1}' 형식에 없지만 '{2}' 형식에서 필수입니다.", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "'{0}' 속성은 프라이빗 식별자를 포함하기 때문에 '{1}' 클래스 외부에서 액세스할 수 없습니다.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "'{0}' 속성은 '{1}' 형식에서 선택적이지만 '{2}' 형식에서는 필수입니다.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "'{0}' 속성은 private이며 '{1}' 클래스 내에서만 액세스할 수 있습니다.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "'{0}' 속성은 '{1}' 형식에서 private이지만 '{2}' 형식에서는 그렇지 않습니다.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "'{0}' 속성은 보호되며 '{1}' 클래스의 인스턴스를 통해서만 액세스할 수 있습니다. 이는 '{2}' 클래스의 인스턴스입니다.", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "'{0}' 속성은 보호된 속성이며 '{1}' 클래스 및 해당 하위 클래스 내에서만 액세스할 수 있습니다.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "'{0}' 속성은 보호된 속성이지만 '{1}' 형식은 '{2}'에서 파생된 클래스가 아닙니다.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "'{0}' 속성은 '{1}' 형식에서는 보호된 속성이지만 '{2}' 형식에서는 공용입니다.", "Property_0_is_used_before_being_assigned_2565": "'{0}' 속성이 할당되기 전에 사용되었습니다.", "Property_0_is_used_before_its_initialization_2729": "초기화하기 전에 '{0}' 속성이 사용됩니다.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "'{0}' 속성이 '{1}' 형식에 없을 수 있습니다. '{2}'을(를) 사용하시겠습니까?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "JSX 분배 특성의 '{0}' 속성을 대상 속성에 할당할 수 없습니다.", "Property_0_of_exported_anonymous_class_type_may_not_be_private_or_protected_4094": "내보낸 익명 클래스 형식의 '{0}' 속성은 비공개이거나 보호됨이 아닐 수 있습니다.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "내보낸 인터페이스의 '{0}' 속성이 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "내보낸 인터페이스의 '{0}' 속성이 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "'{0}' 형식의 '{1}' 속성을 '{2}' 인덱스 유형 '{3}'에 할당할 수 없습니다.", "Property_0_was_also_declared_here_2733": "여기서도 '{0}' 속성이 선언되었습니다.", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "'{0}' 속성은 '{1}'의 기본 속성을 덮어씁니다. 의도적인 경우 이니셜라이저를 추가합니다. 그렇지 않으면 'declare' 한정자를 추가하거나 중복 선언을 제거합니다.", "Property_assignment_expected_1136": "속성 할당이 필요합니다.", "Property_destructuring_pattern_expected_1180": "속성 구조 파괴 패턴이 필요합니다.", "Property_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9012": "속성에는 --isolatedDeclarations가 있는 명시적 형식 주석이 있어야 합니다.", "Property_or_signature_expected_1131": "속성 또는 서명이 필요합니다.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "속성 값은 문자열 리터럴, 숫자 리터럴, 'true', 'false', 'null', 개체 리터럴 또는 배열 리터럴이어야 합니다.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_6179": "'ES5'를 대상으로 할 경우 'for-of', 분산 및 파괴의 반복 가능한 개체를 완벽히 지원합니다.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "내보낸 클래스의 공용 메서드 '{0}'이(가) 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "내보낸 클래스의 공용 메서드 '{0}'이(가) 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "내보낸 클래스의 공용 메서드의 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "내보낸 클래스의 공용 속성 '{0}'이(가) 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "내보낸 클래스의 공용 속성 '{0}'이(가) 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "내보낸 클래스의 공용 속성 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "내보낸 클래스의 공용 정적 메서드 '{0}'이(가) 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "내보낸 클래스에 있는 공용 정적 메서드 '{0}'이(가) 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "내보낸 클래스의 공용 정적 메서드 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "내보낸 클래스에 있는 공용 정적 속성 '{0}'이(가) 외부 모듈 {2}의 '{1}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "내보낸 클래스의 공용 정적 속성 '{0}'이(가) 프라이빗 모듈 '{2}'의 '{1}' 이름을 가지고 있거나 사용 중입니다.", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "내보낸 클래스의 공용 정적 속성 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "정규화된 이름 '{0}'은(는) 선행 '@param {object} {1}'과(와) 함께 사용해야 합니다.", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "함수 매개 변수를 읽지 않으면 오류가 발생합니다.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "암시된 'any' 형식이 있는 식 및 선언에서 오류를 발생합니다.", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "암시된 'any' 형식이 있는 'this' 식에서 오류를 발생합니다.", "Range_out_of_order_in_character_class_1517": "문자 클래스의 범위가 순서를 벗어났습니다.", "Re_exporting_a_type_when_0_is_enabled_requires_using_export_type_1205": "'{0}'을 사용하도록 설정한 경우 형식을 다시 내보내려면 'export type'을 사용해야 합니다.", "React_components_cannot_include_JSX_namespace_names_2639": "React 구성 요소에는 JSX 네임스페이스 이름을 포함할 수 없습니다.", "Redirect_output_structure_to_the_directory_6006": "출력 구조를 디렉터리로 리디렉션합니다.", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "TypeScript에서 자동으로 로드되는 프로젝트 수를 줄입니다.", "Referenced_project_0_may_not_disable_emit_6310": "참조된 프로젝트 '{0}'은(는) 내보내기를 사용하지 않도록 설정할 수 없습니다.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "참조되는 프로젝트 '{0}'에는 \"composite\": true 설정이 있어야 합니다.", "Referenced_via_0_from_file_1_1400": "'{1}' 파일에서 '{0}'을(를) 통해 참조되었습니다.", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2834": "'--moduleResolution'이 'node16' 또는 'nodenext'인 경우 상대 가져오기 경로에는 ECMAScript 가져오기의 명시적 파일 확장자가 필요합니다. 가져오기 경로에 확장자를 추가하는 것을 고려하세요.", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2835": "'--moduleResolution'이 'node16' 또는 'nodenext'인 경우 상대 가져오기 경로에는 ECMAScript 가져오기의 명시적 파일 확장자가 필요합니다. '{0}'을(를) 사용하시겠습니까?", "Remove_a_list_of_directories_from_the_watch_process_6628": "감시 프로세스에서 디렉터리 목록을 제거합니다.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "감시 모드의 처리에서 파일 목록을 제거합니다.", "Remove_all_unnecessary_override_modifiers_95163": "불필요한 'override' 한정자 모두 제거", "Remove_all_unnecessary_uses_of_await_95087": "불필요한 'await' 사용 모두 제거", "Remove_all_unreachable_code_95051": "접근할 수 없는 코드 모두 제거", "Remove_all_unused_labels_95054": "사용되지 않는 레이블 모두 제거", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "관련 문제가 있는 모든 화살표 함수 본문에서 중괄호 제거", "Remove_braces_from_arrow_function_95060": "화살표 함수에서 중괄호 제거", "Remove_braces_from_arrow_function_body_95112": "화살표 함수 본문에서 중괄호 제거", "Remove_import_from_0_90005": "'{0}'에서 가져오기 제거", "Remove_override_modifier_95161": "'override' 한정자 제거", "Remove_parentheses_95126": "괄호 제거", "Remove_template_tag_90011": "템플릿 태그 제거", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "TypeScript 언어 서버에서 JavaScript 파일의 총 소스 코드 크기에 적용되는 20MB 제한을 제거합니다.", "Remove_type_from_import_declaration_from_0_90055": "\"{0}\"의 가져오기 선언에서 '형식' 제거", "Remove_type_from_import_of_0_from_1_90056": "\"{1}\"의 '{0}' 가져오기에서 '형식' 제거", "Remove_type_parameters_90012": "형식 매개 변수 제거", "Remove_unnecessary_await_95086": "불필요한 'await' 제거", "Remove_unreachable_code_95050": "접근할 수 없는 코드 제거", "Remove_unused_declaration_for_Colon_0_90004": "'{0}'의 사용되지 않는 선언 제거", "Remove_unused_declarations_for_Colon_0_90041": "'{0}'의 사용되지 않는 선언 제거", "Remove_unused_destructuring_declaration_90039": "사용되지 않는 구조 파괴 선언 제거", "Remove_unused_label_95053": "사용되지 않는 레이블 제거", "Remove_variable_statement_90010": "변수 문 제거", "Rename_param_tag_name_0_to_1_95173": "'@param' 태그 이름 '{0}'의 이름을 '{1}'(으)로 바꿉니다.", "Replace_0_with_Promise_1_90036": "'{0}'을(를) 'Promise<{1}>'(으)로 바꾸기", "Replace_all_unused_infer_with_unknown_90031": "사용되지 않은 모든 'infer'를 'unknown'으로 바꾸기", "Replace_import_with_0_95015": "가져오기를 '{0}'(으)로 바꿉니다.", "Replace_infer_0_with_unknown_90030": "'infer {0}'을(를) 'unknown'으로 바꾸기", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "함수의 일부 코드 경로가 값을 반환하지 않는 경우 오류를 보고합니다.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "switch 문의 fallthrough case에 대한 오류를 보고합니다.", "Report_errors_in_js_files_8019": ".js 파일의 오류를 보고합니다.", "Report_errors_on_unused_locals_6134": "사용되지 않은 로컬 항목에 대한 오류를 보고합니다.", "Report_errors_on_unused_parameters_6135": "사용되지 않은 매개 변수에 대한 오류를 보고합니다.", "Require_sufficient_annotation_on_exports_so_other_tools_can_trivially_generate_declaration_files_6719": "다른 도구가 선언 파일을 간단하게 생성할 수 있도록 내보내기에서 충분한 주석이 필요합니다.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "요소 액세스를 사용하려면 인덱스 시그니처의 선언되지 않은 속성이 필요합니다.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "필수 형식 매개 변수는 선택적 형식 매개 변수 다음에 올 수 없습니다.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "'{0}' 모듈에 대한 해결을 '{1}' 위치의 캐시에서 찾았습니다.", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "'{1}' 위치의 캐시에서 형식 참조 지시어 '{0}'에 대한 해상도가 발견되었습니다.", "Resolution_of_non_relative_name_failed_trying_with_modern_Node_resolution_features_disabled_to_see_i_6277": "상대 이름이 아닌 이름을 확인하지 못했습니다. 최신 노드 확인 기능을 사용하지 않도록 설정하여 npm 라이브러리에 구성 업데이트가 필요한지 확인합니다.", "Resolution_of_non_relative_name_failed_trying_with_moduleResolution_bundler_to_see_if_project_may_ne_6279": "상대 이름이 아닌 이름을 확인하지 못했습니다. '--moduleResolution bundler'를 사용하여 프로젝트에 구성 업데이트가 필요한지 확인하려고 합니다.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "문자열 값 속성 이름에 대해서만 'keyof'를 확인합니다(숫자나 기호 아님).", "Resolved_under_condition_0_6414": "조건 '{0}'에서 확인되었습니다.", "Resolving_in_0_mode_with_conditions_1_6402": "조건이 {1}인 {0} 모드에서 확인하는 중입니다.", "Resolving_module_0_from_1_6086": "======== '{1}'에서 '{0}' 모듈을 확인하는 중입니다. ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "기본 URL '{1}' - '{2}'을(를) 기준으로 모듈 이름 '{0}'을(를) 확인하는 중입니다.", "Resolving_real_path_for_0_result_1_6130": "'{0}'의 실제 경로를 확인하는 중입니다. 결과: '{1}'.", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== 파일 '{1}'을(를) 포함하는 형식 참조 지시어 '{0}'을(를) 확인하는 중입니다.' ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== 형식 참조 지시문 '{0}'을(를) 확인하는 중입니다. 포함 파일: '{1}', 루트 디렉터리: '{2}' ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== 형식 참조 지시문 '{0}'을(를) 확인하는 중입니다. 포함 파일: '{1}', 루트 디렉터리: 설정되지 않음 ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== 형식 참조 지시문 '{0}'을(를) 확인하는 중입니다. 포함 파일: 설정되지 않음, 루트 디렉터리: '{1}' ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== 형식 참조 지시문 '{0}'을(를) 확인하는 중입니다. 포함 파일: 설정되지 않음, 루트 디렉터리: 설정되지 않음 ========", "Resolving_type_reference_directive_for_program_that_specifies_custom_typeRoots_skipping_lookup_in_no_6265": "사용자 지정 typeRoots를 지정하는 프로그램에 대한 형식 참조 지시문을 확인합니다. 'node_modules' 폴더에서 조회를 건너뜁니다.", "Resolving_with_primary_search_path_0_6121": "기본 검색 경로 '{0}'을(를) 사용하여 확인하는 중입니다.", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Rest 매개 변수 '{0}'에는 암시적으로 'any[]' 형식이 포함됩니다.", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "Rest 매개 변수 '{0}'은(는) 암시적으로 'any[]' 형식이지만, 사용량에서 더 나은 형식을 유추할 수 있습니다.", "Rest_types_may_only_be_created_from_object_types_2700": "rest 유형은 개체 형식에서만 만들 수 있습니다.", "Return_type_annotation_circularly_references_itself_2577": "반환 형식 주석이 자신을 순환 참조합니다.", "Return_type_must_be_inferred_from_a_function_95149": "반환 형식은 함수에서 유추되어야 합니다.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "내보낸 인터페이스에 있는 호출 시그니처의 반환 형식이 프라이빗 모듈 '{1}'의 '{0}' 이름을 가지고 있거나 사용 중입니다.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "내보낸 인터페이스에 있는 호출 시그니처의 반환 형식이 프라이빗 이름 '{0}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "내보낸 인터페이스에 있는 생성자 시그니처의 반환 형식이 프라이빗 모듈 '{1}'의 '{0}' 이름을 가지고 있거나 사용 중입니다.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "내보낸 인터페이스에 있는 생성자 시그니처의 반환 형식이 프라이빗 이름 '{0}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "생성자 시그니처의 반환 형식을 클래스의 인스턴스 형식에 할당할 수 있어야 합니다.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "내보낸 함수의 반환 형식이 외부 모듈 {1}의 '{0}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "내보낸 함수의 반환 형식이 프라이빗 모듈 '{1}'의 '{0}' 이름을 가지고 있거나 사용 중입니다.", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "내보낸 함수의 반환 형식이 프라이빗 이름 '{0}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "내보낸 인터페이스에 있는 인덱스 시그니처의 반환 형식이 프라이빗 모듈 '{1}'의 '{0}' 이름을 가지고 있거나 사용 중입니다.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "내보낸 인터페이스에 있는 인덱스 시그니처의 반환 형식이 프라이빗 이름 '{0}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "내보낸 인터페이스에 있는 메서드의 반환 형식이 프라이빗 모듈 '{1}'의 '{0}' 이름을 가지고 있거나 사용 중입니다.", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "내보낸 인터페이스에 있는 메서드의 반환 형식이 프라이빗 이름 '{0}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "내보낸 클래스에 있는 공용 getter '{0}'의 반환 형식이 외부 모듈 {2}의 이름 '{1}'을(를) 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "내보낸 클래스에 있는 공용 getter '{0}'의 반환 형식이 프라이빗 모듈 '{2}'의 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "내보낸 클래스에 있는 공용 getter '{0}'의 반환 형식이 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "내보낸 클래스에 있는 공용 메서드의 반환 형식이 외부 모듈 {1}의 '{0}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "내보낸 클래스에 있는 공용 메서드의 반환 형식이 프라이빗 모듈 '{1}'의 '{0}' 이름을 가지고 있거나 사용 중입니다.", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "내보낸 클래스에 있는 공용 메서드의 반환 형식이 프라이빗 이름 '{0}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "내보낸 클래스에 있는 공용 정적 getter '{0}'의 반환 형식이 외부 모듈 {2}의 이름 '{1}'을(를) 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "내보낸 클래스에 있는 공용 정적 getter '{0}'의 반환 형식이 프라이빗 모듈 '{2}'의 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "내보낸 클래스에 있는 공용 정적 getter '{0}'의 반환 형식이 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "내보낸 클래스에 있는 공용 정적 메서드의 반환 형식이 외부 모듈 {1}의 '{0}' 이름을 가지고 있거나 사용 중이지만 명명할 수 없습니다.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "내보낸 클래스에 있는 공용 정적 메서드의 반환 형식이 프라이빗 모듈 '{1}'의 '{0}' 이름을 가지고 있거나 사용 중입니다.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "내보낸 클래스에 있는 공용 정적 메서드의 반환 형식이 프라이빗 이름 '{0}'을(를) 가지고 있거나 사용 중입니다.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "'{1}'에서 '{0}' 모듈의 해결 방법을 '{2}위치 '의 캐시에 다시 사용하는 중이었으므로 해결되지 않았습니다.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "위치 '{2}'의 캐시에 있는 '{1}'에서 '{0}' 모듈의 해결 방법을 다시 사용하는 중입니다. '{3}'(으)로 해결되었습니다.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "위치 '{2}'의 캐시에 있는 '{1}'에서 '{0}' 모듈의 해결 방법을 다시 사용하는 중입니다. 패키지 ID가 '{4}'인 '{3}'(으)로 해결되었습니다.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "이전 프로그램의 '{1}'에서 '{0}' 모듈의 해결 방법을 다시 사용하는 중이었으므로 확인되지 않았습니다.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "이전 프로그램의 '{1}'에서 '{0}' 모듈의 해결 방법을 다시 사용 중이므로 '{2}'(으)로 해결되었습니다.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "이전 프로그램의 '{1}'에서 '{0}' 모듈의 해결 방법을 다시 사용 중이며 패키지 ID가 '{3}'인 '{2}'(으)로 해결되었습니다.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "위치 '{2}'의 캐시에 있는 '{1}'에서 형식 참조 지시문 '{0}'의 해결 방법을 다시 사용하는 중이며 해결되지 않았습니다.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "위치 '{2}'의 캐시에 있는 '{1}'에서 형식 참조 지시문 '{0}'의 해결 방법을 다시 사용하는 중입니다. '{3}'(으)로 해결되었습니다.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "위치 '{2}'의 캐시에 있는 '{1}'에서 형식 참조 지시문 '{0}'의 해결 방법을 다시 사용하는 중입니다. 패키지 ID가 '{4}'인 '{3}'(으)로 해결되었습니다.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "이전 프로그램의 '{1}'에서 형식 참조 지시문 '{0}'의 해결 방법을 다시 사용 중이므로 해결되지 않았습니다.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "이전 프로그램의 '{1}'에서 형식 참조 지시문 '{0}'의 해결 방법을 다시 사용 중이며 '{2}'(으)로 해결되었습니다.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "이전 프로그램의 '{1}'에서 형식 참조 지시문 '{0}'의 해결 방법을 다시 사용 중이며 패키지 ID가 '{3}'인 '{2}'(으)로 해결되었습니다.", "Rewrite_all_as_indexed_access_types_95034": "인덱싱된 액세스 형식으로 모두 다시 작성", "Rewrite_as_the_indexed_access_type_0_90026": "인덱싱된 액세스 형식 '{0}'(으)로 다시 작성", "Rewrite_ts_tsx_mts_and_cts_file_extensions_in_relative_import_paths_to_their_JavaScript_equivalent_i_6421": "출력 파일의 해당 JavaScript에 해당하는 상대 가져오기 경로에서 '.ts', '.tsx', '.mts' 및 '.cts' 파일 확장자를 다시 작성합니다.", "Right_operand_of_is_unreachable_because_the_left_operand_is_never_nullish_2869": "??의 오른쪽 피연산자는 왼쪽 피연산자가 nullish가 되지 않으므로 연결할 수 없습니다.", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "루트 디렉터리를 확인할 수 없어 기본 검색 경로를 건너뜁니다.", "Root_file_specified_for_compilation_1427": "컴파일을 위해 지정된 루트 파일", "STRATEGY_6039": "전략", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "프로젝트의 증분 컴파일을 허용하도록 .tsbuildinfo 파일을 저장합니다.", "Saw_non_matching_condition_0_6405": "비일치 조건 '{0}'을(를) 확인했습니다.", "Scoped_package_detected_looking_in_0_6182": "범위가 지정된 패키지가 검색되었습니다. '{0}'에서 찾습니다.", "Searching_all_ancestor_node_modules_directories_for_fallback_extensions_Colon_0_6418": "대체 확장에 대한 모든 상위 node_modules 디렉터리 검색: {0}.", "Searching_all_ancestor_node_modules_directories_for_preferred_extensions_Colon_0_6417": "기본 설정 확장에 대한 모든 상위 node_modules 디렉터리 검색: {0}.", "Selection_is_not_a_valid_statement_or_statements_95155": "선택 항목이 유효한 하나의 문 또는 여러 문이 아닙니다.", "Selection_is_not_a_valid_type_node_95133": "선택 영역이 유효한 형식 노드가 아닙니다.", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "내보낸 JavaScript의 JavaScript 언어 버전을 설정하고 호환되는 라이브러리 선언을 포함합니다.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "TypeScript에서 메시지 언어를 설정합니다. 이는 내보내기에 영향을 주지 않습니다.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "구성 파일의 'module' 옵션을 '{0}'(으)로 설정", "Set_the_newline_character_for_emitting_files_6659": "파일을 내보내기 위한 줄 바꿈 문자를 설정합니다.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "구성 파일의 'target' 옵션을 '{0}'(으)로 설정", "Setters_cannot_return_a_value_2408": "Setter가 값을 반환할 수 없습니다.", "Show_all_compiler_options_6169": "모든 컴파일러 옵션을 표시합니다.", "Show_diagnostic_information_6149": "진단 정보를 표시합니다.", "Show_verbose_diagnostic_information_6150": "자세한 진단 정보를 표시합니다.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "빌드될 항목 표시(또는 '--clean'으로 지정된 경우 삭제될 항목 표시)", "Signature_0_must_be_a_type_predicate_1224": "'{0}' 시그니처는 형식 조건자여야 합니다.", "Signature_declarations_can_only_be_used_in_TypeScript_files_8017": "서명 선언은 TypeScript 파일에서만 사용할 수 있습니다.", "Skip_building_downstream_projects_on_error_in_upstream_project_6640": "업스트림 프로젝트에서 오류 발생 시 다운스트림 프로젝트 빌드를 건너뜁니다.", "Skip_type_checking_all_d_ts_files_6693": "모든 .d.ts 파일의 형식 검사를 건너뜁니다.", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "TypeScript에 포함된 .d.ts 파일의 형식 검사를 건너뜁니다.", "Skip_type_checking_of_declaration_files_6012": "선언 파일 형식 검사를 건너뜁니다.", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Skipping build of project '{0}' because its dependency '{1}' has errors", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Skipping build of project '{0}' because its dependency '{1}' was not built", "Skipping_module_0_that_looks_like_an_absolute_URI_target_file_types_Colon_1_6164": "절대 URI처럼 보이는 '{0}' 모듈을 건너뛰는 중입니다. 대상 파일 형식은 {1}입니다.", "Source_from_referenced_project_0_included_because_1_specified_1414": "'{1}'이(가) 지정되었기 때문에 참조된 프로젝트 '{0}'의 소스가 포함됩니다.", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "'--module'이 'none'으로 지정되었기 때문에 참조된 프로젝트 '{0}'의 소스가 포함됩니다.", "Source_has_0_element_s_but_target_allows_only_1_2619": "소스에 {0}개 요소가 있지만, 대상에서 {1}개만 허용합니다.", "Source_has_0_element_s_but_target_requires_1_2618": "소스에 {0}개 요소가 있지만, 대상에 {1}개가 필요합니다.", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "소스가 대상에 있는 {0} 위치의 필수 요소와 일치하는 항목을 제공하지 않습니다.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "소스가 대상에 있는 {0} 위치의 가변 인자 요소와 일치하는 항목을 제공하지 않습니다.", "Specify_ECMAScript_target_version_6015": "ECMAScript 대상 버전을 지정합니다.", "Specify_JSX_code_generation_6080": "JSX 코드 생성을 지정합니다.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "모든 출력을 하나의 JavaScript 파일로 묶는 파일을 지정하세요. 'declaration'이 true이면 모든 .d.ts 출력을 묶는 파일도 지정합니다.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "컴파일에 포함할 파일과 일치하는 GLOB 패턴 목록을 지정합니다.", "Specify_a_list_of_language_service_plugins_to_include_6681": "포함할 언어 서비스 플러그 인 목록을 지정합니다.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "대상 런타임 환경을 설명하는 번들 라이브러리 선언 파일 세트를 지정합니다.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "추가 검색 위치로 가져오기를 다시 매핑할 항목 집합을 지정합니다.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "프로젝트의 경로를 지정하는 개체 배열을 지정합니다. 프로젝트 참조에 사용됩니다.", "Specify_an_output_folder_for_all_emitted_files_6678": "내보낸 모든 파일의 출력 폴더를 지정합니다.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "유형에만 사용되는 가져오기에 대한 방출/확인 동작을 지정합니다.", "Specify_file_to_store_incremental_compilation_information_6380": "증분 컴파일 정보를 저장할 파일 지정", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "TypeScript가 지정된 모듈 지정자에서 파일을 검색하는 방법을 지정합니다.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "재귀 파일 감시 기능이 없는 시스템에서 디렉터리를 감시하는 방법을 지정합니다.", "Specify_how_the_TypeScript_watch_mode_works_6715": "TypeScript 감시 모드의 작동 방식을 지정합니다.", "Specify_library_files_to_be_included_in_the_compilation_6079": "컴파일에 포함할 라이브러리 파일을 지정합니다.", "Specify_module_code_generation_6016": "모듈 코드 생성을 지정합니다.", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "'jsx: react-jsx*'를 사용할 때 JSX 팩토리 함수를 가져오기 위해 사용되는 모듈 지정자를 지정합니다.", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "'./node_modules/@types'와 같은 역할을 하는 여러 폴더를 지정합니다.", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "설정이 상속되는 기본 구성 파일에 대한 하나 이상의 경로 또는 노드 모듈 참조를 지정합니다.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "선언 파일의 자동 인식 옵션을 지정합니다.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "파일 시스템 이벤트를 사용하여 만들지 못할 경우 폴링 조사식을 만들기 위한 전략 지정: 'FixedInterval'(기본값), 'PriorityInterval', 'DynamicPriority', 'FixedChunkSize'.", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "기본적으로 재귀 감시를 지원하지 않는 플랫폼에서 디렉터리를 감시하기 위한 전략 지정: 'UseFsEvents'(기본값), 'FixedPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling'.", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "파일을 감시하기 위한 전략 지정: 'FixedPollingInterval'(기본값), 'PriorityPollingInterval', 'DynamicPriorityPolling', 'FixedChunkSizePolling', 'UseFsEvents', 'UseFsEventsOnParentDirectory'.", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "React JSX 내보내기를 대상으로 할 때 조각에 사용되는 JSX 조각 참조를 지정합니다(예: 'React.Fragment' 또는 'Fragment').", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "'react' JSX 내보내기를 대상으로 하는 경우 사용할 JSX 팩터리 함수를 지정합니다(예: 'React.createElement' 또는 'h').", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "React JSX 방출을 대상으로 할 때 사용되는 JSX 팩토리 함수를 지정하세요. 'React.createElement' 또는 'h'.", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "'JsxFactory' 컴파일러 옵션이 지정된 상태로 'react' JSX 내보내기를 대상으로 설정할 때 사용할 JSX 조각 팩터리 함수를 지정합니다(예: 'Fragment').", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "상대적이지 않은 모듈 이름을 확인할 기본 디렉터리를 지정합니다.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "파일을 내보낼 때 사용할 줄 시퀀스의 끝 지정: 'CRLF'(dos) 또는 'LF'(unix).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "소스 위치 대신 디버거가 TypeScript 파일을 찾아야 하는 위치를 지정하세요.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "생성된 위치 대신 디버거가 맵 파일을 찾아야 하는 위치를 지정하세요.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "'node_modules'에서 JavaScript 파일을 확인하는 데 사용되는 최대 폴더 깊이를 지정합니다. 'allowJs'에만 적용 가능합니다.", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "'jsx' 및 'jsxs' 팩터리 함수를 가져오는 데 사용할 모듈 지정자를 지정합니다(예: react).", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "'createElement'에 대해 호출된 개체를 지정합니다. '반응' JSX 방출을 대상으로 할 때만 적용됩니다.", "Specify_the_output_directory_for_generated_declaration_files_6613": "생성된 선언 파일의 출력 디렉터리를 지정합니다.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": ".tsbuildinfo 증분 컴파일 파일의 경로를 지정합니다.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "입력 파일의 루트 디렉터리를 지정하세요. --outDir이 포함된 출력 디렉터리 구조를 제어하는 데 사용됩니다.", "Specify_the_root_folder_within_your_source_files_6690": "소스 파일 내에서 루트 폴더를 지정합니다.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "디버거의 루트 경로를 지정하여 참조 소스 코드를 찾습니다.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "소스 파일에서 참조하지 않고 포함할 형식 패키지 이름을 지정합니다.", "Specify_what_JSX_code_is_generated_6646": "생성되는 JSX 코드를 지정합니다.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "시스템에 원시 파일 감시자가 부족한 경우 감시자가 사용해야 하는 접근 방법을 지정합니다.", "Specify_what_module_code_is_generated_6657": "생성되는 모듈 코드를 지정합니다.", "Split_all_invalid_type_only_imports_1367": "잘못된 형식 전용 가져오기 모두 분할", "Split_into_two_separate_import_declarations_1366": "두 개의 개별 가져오기 선언으로 분할", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "'new' 식에서 Spread 연산자는 ECMAScript 5 이상을 대상으로 하는 경우에만 사용할 수 있습니다.", "Spread_types_may_only_be_created_from_object_types_2698": "spread 유형은 개체 형식에서만 만들 수 있습니다.", "Starting_compilation_in_watch_mode_6031": "감시 모드에서 컴파일을 시작하는 중...", "Statement_expected_1129": "문이 필요합니다.", "Statements_are_not_allowed_in_ambient_contexts_1036": "앰비언트 컨텍스트에서는 문이 허용되지 않습니다.", "Static_members_cannot_reference_class_type_parameters_2302": "정적 멤버는 클래스 형식 매개 변수를 참조할 수 없습니다.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "정적 속성 '{0}'이(가) 생성자 함수 '{1}'의 기본 제공 속성 'Function.{0}'과(와) 충돌합니다.", "String_literal_expected_1141": "문자열 리터럴이 필요합니다.", "String_literal_import_and_export_names_are_not_supported_when_the_module_flag_is_set_to_es2015_or_es_18057": "'--module' 플래그가 'es2015' 또는 'es2020'으로 설정된 경우 문자열 리터럴 가져오기 및 내보내기 이름은 지원되지 않습니다.", "String_literal_with_double_quotes_expected_1327": "큰따옴표로 묶은 문자열 리터럴이 필요합니다.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "색과 컨텍스트를 사용하여 오류 및 메시지를 스타일화합니다(실험적).", "Subpattern_flags_must_be_present_when_there_is_a_minus_sign_1504": "en 대시가 있는 경우 하위 패턴 플래그가 있어야 합니다.", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "후속 속성 선언에 같은 형식이 있어야 합니다. '{0}' 속성이 '{1}' 형식이어야 하는데 여기에는 '{2}' 형식이 있습니다.", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "후속 변수 선언에 같은 형식이 있어야 합니다.  '{0}' 변수가 '{1}' 형식이어야 하는데 여기에는 '{2}' 형식이 있습니다.", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "'{1}' 패턴에 대한 '{0}' 대체의 형식이 잘못되었습니다. 'string'이 필요한데 '{2}'을(를) 얻었습니다.", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "'{1}' 패턴의 '{0}' 대체에는 '*' 문자를 최대 하나만 사용할 수 있습니다.", "Substitutions_for_pattern_0_should_be_an_array_5063": "'{0}' 패턴에 대한 대체는 배열이 되어야 합니다.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "패턴 '{0}'에 대한 대체에는 배열이 비어 있지 않아야 합니다.", "Successfully_created_a_tsconfig_json_file_6071": "tsconfig.json 파일을 만들었습니다.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "생성자 밖이나 생성자 내부에 중첩된 함수에서는 Super 호출이 허용되지 않습니다.", "Suppress_excess_property_checks_for_object_literals_6072": "개체 리터럴에 대한 초과 속성 검사를 생략합니다.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "인덱스 시그니처가 없는 개체 인덱싱에 대한 noImplicitAny 오류를 표시하지 않습니다.", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "인덱스 서명이 없는 개체를 인덱싱할 때 'noImplicitAny' 오류를 억제합니다.", "Switch_each_misused_0_to_1_95138": "잘못 사용된 각 '{0}'을(를) '{1}'(으)로 전환", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "기본적으로 재귀 감시를 지원하지 않는 플랫폼에서 동기적으로 콜백을 호출하고 디렉터리 감시자의 상태를 업데이트합니다.", "Syntax_Colon_0_6023": "구문: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "'{0}' 태그는 '{1}'개 이상의 인수가 필요한데 JSX 팩터리 '{2}'이(가) 최대 '{3}'개를 제공합니다.", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "태그가 지정된 템플릿 식은 선택적 체인에서 사용할 수 없습니다.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "대상에서 {0}개 요소만 허용하지만, 소스에 더 많이 있을 수 있습니다.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "대상에 {0}개 요소가 필요하지만, 소스에 더 적게 있을 수 있습니다.", "Target_signature_provides_too_few_arguments_Expected_0_or_more_but_got_1_2849": "대상 서명이 너무 적은 인수를 제공합니다. {0} 이상이 필요하지만 {1}을(를) 받았습니다.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "'{0}' 한정자는 TypeScript 파일에서만 사용할 수 있습니다.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "'{0}' 연산자는 'symbol' 유형에 적용될 수 없습니다.", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "'{0}' 연산자는 부울 형식에 사용할 수 없습니다. 대신 '{1}'을(를) 사용하세요.", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "비동기 반복기의 '{0}' 속성은 메서드여야 합니다.", "The_0_property_of_an_iterator_must_be_a_method_2767": "반복기의 '{0}' 속성은 메서드여야 합니다.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "'Object' 형식은 할당할 수 있는 다른 형식이 거의 없습니다. 대신 'any' 형식을 사용할까요?", "The_Unicode_u_flag_and_the_Unicode_Sets_v_flag_cannot_be_set_simultaneously_1502": "유니코드(u) 플래그와 유니코드 집합(v) 플래그를 동시에 설정할 수 없습니다.", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES5_Consider_using_a_standard_func_2496": "'arguments' 개체는 ES5의 화살표 함수에서 참조할 수 없습니다. 표준 함수 식을 사용해 보세요.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES5_Consider_using_a_sta_2522": "'arguments' 개체는 ES5의 비동기 함수 또는 메서드에서 참조할 수 없습니다. 표준 함수 또는 메서드를 사용해 보세요.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "'if' 문의 본문이 빈 문이면 안 됩니다.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "이 구현에 대한 호출이 성공하겠지만, 오버로드의 구현 시그니처는 외부에 표시되지 않습니다.", "The_character_set_of_the_input_files_6163": "입력 파일의 문자 집합입니다.", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "포함하는 화살표 함수는 'this'의 전역 값을 캡처합니다.", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "제어 흐름 분석에 대해 포함된 함수 또는 모듈 본문이 너무 큽니다.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "현재 파일은 CommonJS 모듈이며 최상위 수준에서 'await'를 사용할 수 없습니다.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "현재 파일은 가져오기에서 'require' 호출을 생성하는 CommonJS 모듈입니다. 그러나 참조된 파일은 ECMAScript 모듈이며 'require'로 가져올 수 없습니다. 대신 동적 'import(\"{0}\")' 호출을 작성하는 것이 좋습니다.", "The_current_host_does_not_support_the_0_option_5001": "현재 호스트가 '{0}' 옵션을 지원하지 않습니다.", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "사용하려는 '{0}'의 선언이 여기서 정의됩니다.", "The_declaration_was_marked_as_deprecated_here_2798": "선언이 여기에 사용되지 않음으로 표시되었습니다.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "필요한 형식은 여기에서 '{1}' 형식에 선언된 '{0}' 속성에서 가져옵니다.", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "필요한 형식은 이 시그니처의 반환 형식에서 가져옵니다.", "The_expected_type_comes_from_this_index_signature_6501": "필요한 형식은 이 인덱스 시그니처에서 가져옵니다.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "내보내기 할당의 식은 앰비언트 컨텍스트의 식별자 또는 정규화된 이름이어야 합니다.", "The_file_is_in_the_program_because_Colon_1430": "파일은 다음과 같은 이유로 프로그램에 있습니다.", "The_files_list_in_config_file_0_is_empty_18002": "'{0}' 구성 파일의 '파일' 목록이 비어 있습니다.", "The_first_export_default_is_here_2752": "첫 번째 내보내기 기본값은 여기에 있습니다.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "프라미스에서 'then' 메서드의 첫 번째 매개 변수는 콜백이어야 합니다.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "전역 형식 'JSX.{0}'에 속성이 둘 이상 있을 수 없습니다.", "The_implementation_signature_is_declared_here_2750": "여기에서는 구현 시그니처가 선언됩니다.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "'import.meta' 메타 속성은 CommonJS 출력으로 빌드될 파일에서 허용되지 않습니다.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "'import.meta' 메타 속성은 '--module' 옵션이 'es2020', 'es2022', 'esnext', 'system', 'node16', 'node18' 또는 'nodenext'인 경우에만 허용됩니다.", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "'{0}'의 유추된 형식 이름을 지정하려면 '{1}'에 대한 참조가 있어야 합니다. 이식하지 못할 수 있습니다. 형식 주석이 필요합니다.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "'{0}'의 유추된 형식이 일반적으로 직렬화될 수 없는 순환 구조가 있는 형식을 참조합니다. 형식 주석이 필요합니다.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "'{0}'의 유추 형식이 액세스할 수 없는 '{1}' 형식을 참조합니다. 형식 주석이 필요합니다.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "이 노드의 유추된 형식이 컴파일러가 직렬화할 최대 길이를 초과합니다. 명시적 형식 주석이 필요합니다.", "The_initializer_of_a_using_declaration_must_be_either_an_object_with_a_Symbol_dispose_method_or_be_n_2850": "'using' 선언의 이니셜라이저는 '[Symbol.dispose]()' 메서드가 있는 개체이거나 'null' 또는 'undefined'여야 합니다.", "The_initializer_of_an_await_using_declaration_must_be_either_an_object_with_a_Symbol_asyncDispose_or_2851": "'await using' 선언의 이니셜라이저는 '[Symbol.asyncDispose]()' 또는 '[Symbol.dispose]5D;()' 메서드가 있는 개체이거나 'null' 또는 'undefined'여야 합니다.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "속성 '{1}'이(가) 여러 구성원에 있고 일부 구성원에서는 프라이빗 상태이므로 교집합 '{0}'이(가) 'never'로 감소했습니다.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "일부 구성원에 속성 '{1}'에 충돌하는 형식이 있으므로 교집합 '{0}'이(가) 'never'로 감소했습니다.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "'내장' 키워드는 컴파일러에서 제공하는 내장 형식을 선언하는 데에만 사용할 수 있습니다.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "'JsxFactory' 컴파일러 옵션과 함께 JSX 조각을 사용하려면 'jsxFragmentFactory' 컴파일러 옵션을 제공해야 합니다.", "The_last_overload_gave_the_following_error_2770": "마지막 오버로드에서 다음 오류가 발생했습니다.", "The_last_overload_is_declared_here_2771": "여기서 마지막 오버로드가 선언됩니다.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "'for...in' 문의 왼쪽에는 구조 파괴 패턴을 사용할 수 없습니다.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_using_declaration_1493": "'for... in' 문의 왼쪽은 'using' 선언일 수 없습니다.", "The_left_hand_side_of_a_for_in_statement_cannot_be_an_await_using_declaration_1494": "'for... in' 문의 왼쪽은 'await using' 선언일 수 없습니다.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "'for...in' 문의 왼쪽에는 형식 주석을 사용할 수 없습니다.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "'for...in' 문의 왼쪽은 선택적 속성 액세스일 수 없습니다.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "'for...in' 문의 왼쪽은 변수 또는 속성 액세스여야 합니다.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "'for...in' 문의 왼쪽은 'string' 또는 'any' 형식이어야 합니다.", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "'for...of' 문의 왼쪽에는 형식 주석을 사용할 수 없습니다.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "'for...of' 문의 왼쪽은 선택적 속성 액세스일 수 없습니다.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "'for...of' 문의 왼쪽은 'async'일 수 없습니다.", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "'for...of' 문의 왼쪽은 변수 또는 속성 액세스여야 합니다.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "산술 연산의 왼쪽은 'any', 'number', 'bigint' 또는 열거형 형식이어야 합니다.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "할당 식의 왼쪽은 선택적 속성 액세스일 수 없습니다.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "할당 식의 왼쪽은 변수 또는 속성 액세스여야 합니다.", "The_left_hand_side_of_an_instanceof_expression_must_be_assignable_to_the_first_argument_of_the_right_2860": "'instanceof' 식의 왼쪽은 오른쪽 '[Symbol.hasInstance]' 메서드의 첫 번째 인수에 할당할 수 있어야 합니다.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "'instanceof' 식 왼쪽은 'any' 형식, 개체 형식 또는 형식 매개 변수여야 합니다.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "사용자에게 메시지를 표시할 때 사용되는 로캘입니다(예: 'en-us').", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "node_modules에서 검색하고 JavaScript 파일을 로드할 최대 종속성 깊이입니다.", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "'delete' 연산자의 피연산자는 프라이빗 식별자일 수 없습니다.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "'delete' 연산자의 피연산자는 읽기 전용 속성일 수 없습니다.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "'delete' 연산자의 피연산자는 속성 참조여야 합니다.", "The_operand_of_a_delete_operator_must_be_optional_2790": "'delete' 연산자의 피연산자는 선택 사항이어야 합니다.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "증분 또는 감소 연산자의 피연산자는 선택적 속성 액세스일 수 없습니다.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "증가 또는 감소 연산자의 피연산자는 변수 또는 속성 액세스여야 합니다.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "여기서 파서는 '{0}' 토큰과 일치하는 '{1}'을(를) 찾아야 합니다.", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "프로젝트 루트가 모호하지만 '{1}' 파일에서 '{0}' 내보내기 맵 항목을 확인하는 데 필요합니다. 명확하게 하려면 `rootDir` 컴파일러 옵션을 제공하세요.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "프로젝트 루트가 모호하지만 '{1}' 파일에서 '{0}' 가져오기 맵 항목을 확인하는 데 필요합니다. 명확하게 하려면 `rootDir` 컴파일러 옵션을 제공하세요.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "철자가 동일한 다른 프라이빗 식별자에서 섀도 처리되기 때문에 이 클래스 내의 '{1}' 형식에서 '{0}' 속성에 액세스할 수 없습니다.", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "매개 변수 데코레이터 함수의 반환 형식은 'void' 또는 'any'여야 합니다.", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "속성 데코레이터 함수의 반환 형식은 'void' 또는 'any'여야 합니다.", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "비동기 함수의 반환 형식은 유효한 프라미스여야 하거나 호출 가능 'then' 멤버를 포함하지 않아야 합니다.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1065": "비동기 함수 또는 메서드의 반환 형식은 전역 Promise<T> 형식이어야 합니다.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "비동기 함수 또는 메서드의 반환 형식은 전역 Promise<T> 형식이어야 합니다. 'Promise<{0}>'을(를) 쓰려고 했습니까?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "'for...in' 문 오른쪽은 'any' 형식, 개체 형식 또는 형식 매개 변수여야 하는데, 여기에 '{0}' 형식이 있습니다.", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "산술 연산 오른쪽은 'any', 'number', 'bigint' 또는 열거형 형식이어야 합니다.", "The_right_hand_side_of_an_instanceof_expression_must_be_either_of_type_any_a_class_function_or_other_2359": "'instanceof' 식의 오른쪽은 'any' 형식, 클래스, 함수 또는 'Function' 인터페이스 형식에 할당할 수 있는 다른 형식이거나 'Symbol.hasInstance' 메서드가 있는 개체 형식이어야 합니다.", "The_right_hand_side_of_an_instanceof_expression_must_not_be_an_instantiation_expression_2848": "'instanceof' 식의 오른쪽은 인스턴스화 식이 아니어야 합니다.", "The_root_value_of_a_0_file_must_be_an_object_5092": "'{0}' 파일의 루트 값은 개체여야 합니다.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_0_1278": "런타임에서는 {1} 인수를 사용하여 데코레이터를 호출하지만 데코레이터에는 {0}이(가) 필요합니다.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_at_least_0_1279": "런타임에서는 {1} 인수를 사용하여 데코레이터를 호출하지만 데코레이터에는 {0} 이상이 필요합니다.", "The_shadowing_declaration_of_0_is_defined_here_18017": "여기서는 '{0}'의 섀도 선언이 정의됩니다.", "The_signature_0_of_1_is_deprecated_6387": "'{1}'의 시그니처 '{0}'은(는) 사용되지 않습니다.", "The_specified_path_does_not_exist_Colon_0_5058": "지정된 경로가 없습니다. '{0}'.", "The_tag_was_first_specified_here_8034": "태그가 처음에 여기에 지정되었습니다.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "개체 rest 할당의 대상은 선택적 속성 액세스일 수 없습니다.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "개체 rest 할당의 대상은 변수 또는 속성 액세스여야 합니다.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "'{0}' 형식의 'this' 컨텍스트를 메서드의 '{1}' 형식 'this'에 할당할 수 없습니다.", "The_this_types_of_each_signature_are_incompatible_2685": "각 시그니처의 'this' 형식이 호환되지 않습니다.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "'{0}' 형식은 'readonly'이며 변경 가능한 형식 '{1}'에 할당할 수 없습니다.", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "export 문에 'export type'이 사용될 때 명명된 내보내기에서 'type' 한정자를 사용할 수 없습니다.", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "import 문에 'import type'이 사용되는 경우 'type' 수정자는 명명된 가져오기에 사용할 수 없습니다.", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "함수 선언의 형식은 함수의 시그니처와 일치해야 합니다.", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "속성 '{0}'을(를) 직렬화할 수 없기 때문에 이 노드의 유형을 직렬화할 수 없습니다.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "비동기 반복기의 '{0}()' 메서드에서 반환하는 형식은 'value' 속성이 있는 형식에 대한 프라미스여야 합니다.", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "반복기의 '{0}()' 메서드에 의해 반환되는 형식에는 'value' 속성이 있어야 합니다.", "The_types_of_0_are_incompatible_between_these_types_2200": "'{0}'의 형식은 해당 형식 간에 호환되지 않습니다.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "'{0}'에서 반환되는 형식은 해당 형식 간에 호환되지 않습니다.", "The_value_0_cannot_be_used_here_18050": "여기서는 '{0}' 값을 사용할 수 없습니다.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "'for...in' 문의 변수 선언에 이니셜라이저가 포함될 수 없습니다.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "'for...of' 문의 변수 선언에 이니셜라이저가 포함될 수 없습니다.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "'with' 문은 지원되지 않습니다. 'with' 블록의 모든 기호가 'any' 형식이 됩니다.", "There_are_types_at_0_but_this_result_could_not_be_resolved_under_your_current_moduleResolution_setti_6280": "'{0}'에 형식이 있지만 현재 'moduleResolution' 설정에서 이 결과를 확인할 수 없습니다. 'node16', 'nodenext' 또는 'bundler'로 업데이트하는 것이 좋습니다.", "There_are_types_at_0_but_this_result_could_not_be_resolved_when_respecting_package_json_exports_The__6278": "'{0}'에 형식이 있지만 package.json \"내보내기\"를 준수할 때 이 결과를 확인할 수 없습니다. '{1}' 라이브러리는 package.json 또는 입력을 업데이트해야 할 수 있습니다.", "There_is_no_capturing_group_named_0_in_this_regular_expression_1532": "이 정규식에는 이름이 '{0}'인 캡처 그룹이 없습니다.", "There_is_nothing_available_for_repetition_1507": "반복에 사용할 수 있는 항목이 없습니다.", "This_JSX_tag_requires_0_to_be_in_scope_but_it_could_not_be_found_2874": "이 JSX 태그를 사용하려면 '{0}'이(가) 범위에 있어야 하지만 찾을 수 없습니다.", "This_JSX_tag_requires_the_module_path_0_to_exist_but_none_could_be_found_Make_sure_you_have_types_fo_2875": "이 JSX 태그를 사용하려면 모듈 경로 '{0}'이(가) 있어야 하지만 찾을 수 없습니다. 적절한 패키지에 대해 형식이 설치되어 있는지 확인합니다.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "이 JSX 태그의 '{0}' 속성에는 '{1}' 형식의 자식 하나가 필요하지만, 여러 자식이 제공되었습니다.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "이 JSX 태그의 '{0}' 속성에는 여러 자식이 있어야 하는 '{1}' 형식이 필요하지만, 단일 자식만 제공되었습니다.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_no_capturing_groups_in_this_regul_1534": "이 역참조는 존재하지 않는 그룹을 참조합니다. 이 정규식에는 캡처 그룹이 없습니다.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_only_0_capturing_groups_in_this_r_1533": "이 역참조는 존재하지 않는 그룹을 참조합니다. 이 정규식에는 {0} 캡처 그룹만 있습니다.", "This_binary_expression_is_never_nullish_Are_you_missing_parentheses_2870": "이 이진 식은 nullish가 되지 않습니다. 괄호가 누락되었나요?", "This_character_cannot_be_escaped_in_a_regular_expression_1535": "이 문자는 정규식에서 이스케이프할 수 없습니다.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "'{0}'이(가) '{1}'과(와) 겹치지 않으므로 이 비교는 의도하지 않은 것 같습니다.", "This_condition_will_always_return_0_2845": "이 조건은 항상 '{0}'을(를) 반환합니다.", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "JavaScript는 값이 아닌 참조로 개체를 비교하므로 이 조건은 항상 '{0}'을(를) 반환합니다.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "이 '{0}'(은)는 항상 정의되어 있으므로 이 조건은 항상 true를 반환합니다.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "함수가 항상 정의되므로 이 조건은 항상 true를 반환합니다. 대신 호출하시겠어요?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "이 생성자 함수는 클래스 선언으로 변환될 수 있습니다.", "This_expression_is_always_nullish_2871": "이 식은 항상 nullish입니다.", "This_expression_is_not_callable_2349": "이 식은 호출할 수 없습니다.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "이 식은 'get' 접근자이므로 호출할 수 없습니다. '()' 없이 사용하시겠습니까?", "This_expression_is_not_constructable_2351": "이 식은 생성할 수 없습니다.", "This_file_already_has_a_default_export_95130": "이 파일에 이미 기본 내보내기가 있습니다.", "This_import_path_is_unsafe_to_rewrite_because_it_resolves_to_another_project_and_the_relative_path_b_2878": "이 가져오기 경로는 다른 프로젝트로 확인되고 프로젝트의 출력 파일 간의 상대 경로가 입력 파일 간의 상대 경로와 동일하지 않으므로 다시 쓰기에는 안전하지 않습니다.", "This_import_uses_a_0_extension_to_resolve_to_an_input_TypeScript_file_but_will_not_be_rewritten_duri_2877": "이 가져오기는 '{0}' 확장을 사용하여 입력 TypeScript 파일로 확인하지만 상대 경로가 아니므로 내보내는 동안 다시 작성되지 않습니다.", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "확대되는 선언입니다. 확대하는 선언을 같은 파일로 이동하는 것이 좋습니다.", "This_kind_of_expression_is_always_falsy_2873": "이러한 종류의 식은 항상 거짓입니다.", "This_kind_of_expression_is_always_truthy_2872": "이러한 종류의 식은 항상 사실입니다.", "This_may_be_converted_to_an_async_function_80006": "비동기 함수로 변환될 수 있습니다.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "이 멤버는 기본 클래스 '{0}'에서 선언되지 않았기 때문에 'override' 태그가 있는 JSDoc 주석을 가질 수 없습니다.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "이 멤버는 기본 클래스 '{0}'에서 선언되지 않았기 때문에 'override' 태그가 있는 JSDoc 주석을 가질 수 없습니다. ‘{1}’을(를) 의미했나요?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "이 멤버는 포함하는 클래스 '{0}'이(가) 다른 클래스를 확장하지 않기 때문에 '@override' 태그가 있는 JSDoc 주석을 가질 수 없습니다.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_name_is_dynamic_4128": "이 멤버는 이름이 동적이기 때문에 '@override' 태그가 포함된 JSDoc 주석을 가질 수 없습니다.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "이 멤버는 기본 클래스 '{0}'에 선언되지 않았으므로 'override' 한정자를 포함할 수 없습니다.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "이 멤버는 기본 클래스 '{0}'에 선언되지 않았으므로 'override' 한정자를 포함할 수 없습니다. '{1}'였습니까?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "이 멤버는 포함하는 클래스 '{0}'이(가) 다른 클래스를 확장하지 않으므로 'override' 한정자를 포함할 수 없습니다.", "This_member_cannot_have_an_override_modifier_because_its_name_is_dynamic_4127": "이름이 동적이므로 이 멤버에는 'override' 한정자를 사용할 수 없습니다.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "이 멤버는 기본 클래스 '{0}'의 멤버를 재정의하므로 '@override' 태그가 있는 JSDoc 주석이 있어야 합니다.", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "이 멤버는 기본 클래스 '{0}'의 멤버를 재정의하므로 'override' 한정자를 포함해야 합니다.", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "이 멤버는 기본 클래스 '{0}'에 선언된 추상 메서드를 재정의하므로 'override' 한정자를 포함해야 합니다.", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "이 모듈은 '{0}' 플래그를 켜고 기본 내보내기를 참조하여 ECMAScript 가져오기/내보내기를 통해서만 참조할 수 있습니다.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "이 모듈은 'export ='를 사용하여 선언되었으며 '{0}' 플래그를 사용하는 경우에만 기본 가져오기와 함께 사용할 수 있습니다.", "This_operation_can_be_simplified_This_shift_is_identical_to_0_1_2_6807": "이 작업은 간소화할 수 있습니다. 이 교대 근무는 '{0} {1} {2}'(와)과 동일합니다.", "This_overload_implicitly_returns_the_type_0_because_it_lacks_a_return_type_annotation_7012": "이 오버로드는 반환 형식 주석이 없으므로 '{0}' 형식을 암시적으로 반환합니다.", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "이 오버로드 시그니처는 해당 구현 시그니처와 호환되지 않습니다.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "이 매개 변수는 'use strict' 지시문에서 사용할 수 없습니다.", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "이 매개변수 속성은 기본 클래스 '{0}'의 멤버를 재정의하므로 '@override' 태그가 있는 JSDoc 주석이 있어야 합니다.", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "이 매개 변수 속성은 기본 클래스 '{0}'의 멤버를 재정의하므로 'override' 한정자를 포함해야 합니다.", "This_regular_expression_flag_cannot_be_toggled_within_a_subpattern_1509": "이 정규식 플래그는 하위 패턴 내에서 토글할 수 없습니다.", "This_regular_expression_flag_is_only_available_when_targeting_0_or_later_1501": "이 정규식 플래그는 '{0}' 이상을 대상으로 하는 경우에만 사용할 수 있습니다.", "This_relative_import_path_is_unsafe_to_rewrite_because_it_looks_like_a_file_name_but_actually_resolv_2876": "이 상대 가져오기 경로는 파일 이름처럼 보이지만 실제로는 \"{0}\"(으)로 확인되므로 다시 작성하는 것이 안전하지 않습니다.", "This_spread_always_overwrites_this_property_2785": "이 스프레드는 항상 이 속성을 덮어씁니다.", "This_syntax_is_not_allowed_when_erasableSyntaxOnly_is_enabled_1294": "'erasableSyntaxOnly'를 사용하도록 설정한 경우에는 이 구문을 사용할 수 없습니다.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "이 구문은 확장자가 .mts 또는 .cts인 파일에 예약되어 있습니다. 후행 쉼표 또는 명시적 제약 조건을 추가합니다.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "이 구문은 확장자가 .mts 또는 .cts인 파일에 예약되어 있습니다. 대신 'as' 식을 사용하세요.", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "이 구문에는 가져온 도우미가 필요하지만 '{0}' 모듈을 찾을 수 없습니다.", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "이 구문에는 '{0}'에 없는 '{1}'(이)라는 가져온 도우미가 필요합니다. '{0}'의 버전을 업그레이드하는 것이 좋습니다.", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "이 구문에는 {2} 매개 변수가 포함된 '{1}'(이)라는 가져온 도우미가 필요하지만, 이 도우미는 '{0}'에 있는 도우미와 호환되지 않습니다. '{0}' 버전을 업그레이드하는 것이 좋습니다.", "This_type_parameter_might_need_an_extends_0_constraint_2208": "이 형식 매개 변수에는 `extends {0}` 제약 조건이 필요할 수 있습니다.", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "이 '가져오기' 사용은 유효하지 않습니다. 'import()' 호출을 작성할 수 있지만 괄호가 있어야 하며 유형 인수를 가질 수 없습니다.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "이 파일을 ECMAScript 모듈로 변환하려면 `\"type\": \"module\"` 필드를 '{0}'에 추가하세요.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "이 파일을 ECMAScript 모듈로 변환하려면 파일 확장명을 '{0}'(으)로 변경하거나 `\"type\": \"module\"` 필드를 '{1}'에 추가하세요.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "이 파일을 ECMAScript 모듈로 변환하려면 파일 확장명을 '{0}'(으)로 변경하거나 `{ \"type\": \"module\" }`을 사용하여 로컬 package.json 파일을 만드세요.", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "이 파일을 ECMAScript 모듈로 변환하려면 `{ \"type\": \"module\" }`을 사용하여 로컬 package.json 파일을 만드세요.", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "최상위 'await' 식은 'module' 옵션이 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext' 또는 'preserve'로 설정되고 'target' 옵션이 'es2017' 이상으로 설정된 경우에만 허용됩니다.", "Top_level_await_using_statements_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_sys_2854": "최상위 'await using' 문은 'module' 옵션이 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext' 또는 'preserve'로 설정되고 'target' 옵션이 'es2017' 이상으로 설정된 경우에만 허용됩니다.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": ".d.ts 파일의 최상위 수준 선언은 'declare' 또는 'export' 한정자로 시작해야 합니다.", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "최상위 'for await' 루프는 'module' 옵션이 'es2022', 'esnext', 'system', 'node16', 'node18', 'nodenext' 또는 'preserve'로 설정되고 'target' 옵션이 'es2017' 이상으로 설정된 경우에만 허용됩니다.", "Trailing_comma_not_allowed_1009": "후행 쉼표는 허용되지 않습니다.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "각 파일을 별도 모듈로 변환 컴파일합니다('ts.transpileModule'과 유사).", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "해당 항목이 있는 경우 'npm i --save-dev @types/{1}'을(를) 시도하거나, 'declare module '{0}';'을(를) 포함하는 새 선언(.d.ts) 파일 추가", "Trying_other_entries_in_rootDirs_6110": "'rootDirs'의 다른 항목을 시도하는 중입니다.", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "'{0}' 대체를 시도하는 중입니다. 후보 모듈 위치: '{1}'.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "길이가 '{1}'인 튜플 형식 '{0}'의 인덱스 '{2}'에 요소가 없습니다.", "Tuple_type_arguments_circularly_reference_themselves_4110": "튜플 형식 인수가 자신을 순환 참조합니다.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "'{0}' 형식은 '--downlevelIteration' 플래그 또는 'es2015' 이상의 '--target'을 사용하는 경우에만 반복할 수 있습니다.", "Type_0_cannot_be_used_as_an_index_type_2538": "'{0}' 형식을 인덱스 형식으로 사용할 수 없습니다.", "Type_0_cannot_be_used_to_index_type_1_2536": "'{0}' 형식을 인덱스 형식 '{1}'에 사용할 수 없습니다.", "Type_0_does_not_satisfy_the_constraint_1_2344": "'{0}' 형식이 '{1}' 제약 조건을 만족하지 않습니다.", "Type_0_does_not_satisfy_the_expected_type_1_1360": "형식 '{0}'이(가) 필요한 형식 '{1}'을(를) 충족하지 않습니다.", "Type_0_has_no_call_signatures_2757": "'{0}' 형식에 호출 시그니처가 없습니다.", "Type_0_has_no_construct_signatures_2761": "'{0}' 형식에 구문 시그니처가 없습니다.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "'{0}' 형식에 '{1}' 형식에 대한 일치하는 인덱스 시그니처가 없습니다.", "Type_0_has_no_properties_in_common_with_type_1_2559": "'{0}' 유형에 '{1}' 유형과 공통적인 속성이 없습니다.", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "유형 '{0}'에는 유형 인수 목록을 적용할 수 있는 서명이 없습니다.", "Type_0_is_generic_and_can_only_be_indexed_for_reading_2862": "'{0}' 형식은 제네릭이며 읽기용으로만 인덱싱할 수 있습니다.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "'{0}' 형식에 '{1}' 형식의 {2} 속성이 없습니다.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "'{0}' 형식에 '{1}' 형식의 {2} 외 {3}개 속성이 없습니다.", "Type_0_is_not_a_constructor_function_type_2507": "'{0}' 형식은 생성자 함수 형식이 아닙니다.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compa_1055": "'{0}' 형식은 Promise 호환 생성자 값을 참조하지 않으므로 ES5에서 유효한 비동기 함수 반환 형식이 아닙니다.", "Type_0_is_not_an_array_type_2461": "'{0}' 형식은 배열 형식이 아닙니다.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "'{0}' 형식은 배열 형식 또는 문자열 형식이 아닙니다.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "'{0}' 형식은 배열 형식 또는 문자열 형식이 아니거나, 반복기를 반환하는 '[Symbol.iterator]()' 메서드가 없습니다.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "'{0}' 형식은 배열 형식이 아니거나 반복기를 반환하는 '[Symbol.iterator]()' 메서드가 없습니다.", "Type_0_is_not_assignable_to_type_1_2322": "'{0}' 형식은 '{1}' 형식에 할당할 수 없습니다.", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "'{0}' 형식은 '{1}' 형식에 할당할 수 없습니다. '{2}'을(를) 의미했나요?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "'{0}' 형식을 '{1}' 형식에 할당할 수 없습니다. 이름이 같은 2개의 서로 다른 형식이 있지만 서로 관련은 없습니다.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "'{0}' 형식은 가변성 주석에 암시된 대로 '{1}' 형식에 할당할 수 없습니다.", "Type_0_is_not_assignable_to_type_1_as_required_for_computed_enum_member_values_18033": "'{0}' 형식은 계산 열거형 멤버 값에 필요하므로 '{1}' 형식에 할당할 수 없습니다.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "'{0}' 유형은 'exactOptionalPropertyTypes: true'가 있는 '{1}' 유형에 할당할 수 없습니다. 대상 속성의 유형에 'undefined'를 추가하는 것을 고려하세요.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "'{0}' 유형은 'exactOptionalPropertyTypes: true'가 있는 '{1}' 유형에 할당할 수 없습니다. 대상 유형에 'undefined'를 추가하는 것을 고려하세요.", "Type_0_is_not_comparable_to_type_1_2678": "'{0}' 형식을 '{1}' 형식과 비교할 수 없습니다.", "Type_0_is_not_generic_2315": "'{0}' 형식이 제네릭이 아닙니다.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "형식 '{0}'은(는) 'in' 연산자의 오른쪽 피연산자로 허용되지 않는 기본 값을 나타낼 수 있습니다.", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "'{0}' 형식에는 비동기 반복기를 반환하는 '[Symbol.asyncIterator]()' 메서드가 있어야 합니다.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "'{0}' 형식에는 반복기를 반환하는 '[Symbol.iterator]()' 메서드가 있어야 합니다.", "Type_0_provides_no_match_for_the_signature_1_2658": "'{0}' 형식에서 '{1}' 시그니처에 대한 일치하는 항목을 제공하지 않습니다.", "Type_0_recursively_references_itself_as_a_base_type_2310": "Type '{0}' 형식은 자기 자신을 기본 형식으로 재귀적으로 참조합니다.", "Type_Checking_6248": "형식 검사", "Type_alias_0_circularly_references_itself_2456": "'{0}' 형식 별칭은 순환적으로 자신을 참조합니다.", "Type_alias_must_be_given_a_name_1439": "형식 별칭에 이름을 지정해야 합니다.", "Type_alias_name_cannot_be_0_2457": "형식 별칭 이름은 '{0}'일 수 없습니다.", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "형식 별칭은 TypeScript 파일에서만 사용할 수 있습니다.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "형식 주석은 생성자 선언에 표시될 수 없습니다.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "형식 주석은 TypeScript 파일에서만 사용할 수 있습니다.", "Type_argument_expected_1140": "형식 인수가 필요합니다.", "Type_argument_list_cannot_be_empty_1099": "형식 인수 목록은 비워 둘 수 없습니다.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "형식 인수는 TypeScript 파일에서만 사용할 수 있습니다.", "Type_arguments_for_0_circularly_reference_themselves_4109": "'{0}'의 형식 인수가 자신을 순환 참조합니다.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "형식 어설션 식은 TypeScript 파일에서만 사용할 수 있습니다.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "소스의 {0} 위치에 있는 형식이 대상의 {1} 위치에 있는 형식과 호환되지 않습니다.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "소스의 {0}~{1} 위치에 있는 형식이 대상의 {2} 위치에 있는 형식과 호환되지 않습니다.", "Type_containing_private_name_0_can_t_be_used_with_isolatedDeclarations_9039": "'{0}' 프라이빗 이름이 포함된 형식은 --isolatedDeclarations와 함께 사용할 수 없습니다.", "Type_declaration_files_to_be_included_in_compilation_6124": "컴파일에 포함할 선언 파일을 입력하세요.", "Type_expected_1110": "형식이 필요합니다.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "유형 가져오기 어설션에는 값이 '가져오기' 또는 '요구'인 정확히 하나의 키('resolution-mode')가 있어야 합니다.", "Type_import_attributes_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1464": "형식 가져오기 특성에는 값이 'import' 또는 'require'인 키 'resolution-mode'가 정확히 하나 있어야 합니다.", "Type_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribute_1542": "CommonJS 모듈에서 ECMAScript 모듈의 형식 가져오기에는 'resolution-mode' 특성이 있어야 합니다.", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "형식 인스턴스화는 깊이가 매우 깊으며 무한할 수도 있습니다.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "형식은 자체 'then' 메서드의 처리 콜백에서 직간접적으로 참조됩니다.", "Type_library_referenced_via_0_from_file_1_1402": "'{1}' 파일에서 '{0}'을(를) 통해 형식 라이브러리가 참조되었습니다.", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "packageId가 '{2}'인 '{1}' 파일에서 '{0}'을(를) 통해 형식 라이브러리가 참조되었습니다.", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "'await' 형식의 피연산자는 유효한 프라미스여야 하거나 호출 가능 'then' 멤버를 포함하지 않아야 합니다.", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "계산된 속성 값의 형식은 '{1}' 형식에 할당할 수 없는 '{0}'입니다.", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "인스턴스 멤버 변수 '{0}'의 형식은 생성자에 선언된 식별자 '{1}'을(를) 참조할 수 없습니다.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "'yield*'의 반복되는 요소 형식의 피연산자는 유효한 프라미스여야 하거나 호출 가능 'then' 멤버를 포함하지 않아야 합니다.", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "'{0}' 속성의 형식은 매핑된 형식 '{1}'에서 순환적으로 자신을 참조합니다.", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "비동기 생성기에 있는 'yield' 형식의 피연산자는 유효한 프라미스여야 하거나 호출 가능 'then' 멤버를 포함하지 않아야 합니다.", "Type_only_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribut_1541": "CommonJS 모듈에서 ECMAScript 모듈의 형식 전용 가져오기에는 'resolution-mode' 특성이 있어야 합니다.", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "형식이 이 가져오기에서 시작됩니다. 네임스페이스 스타일 가져오기는 호출하거나 생성할 수 없으며, 런타임에 오류를 초래합니다. 여기서는 대신 기본 가져오기 또는 가져오기 require를 사용하는 것이 좋습니다.", "Type_parameter_0_has_a_circular_constraint_2313": "형식 매개 변수 '{0}'에 순환 제약 조건이 있습니다.", "Type_parameter_0_has_a_circular_default_2716": "형식 매개 변수 '{0}'에 순환 기본값이 있습니다.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "내보낸 인터페이스에 있는 호출 시그니처의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "내보낸 인터페이스에 있는 생성자 시그니처의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "내보낸 클래스의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "내보낸 함수의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "내보낸 인터페이스의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "내보낸 매핑된 개체 형식의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 사용 중입니다.", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "내보낸 형식 별칭의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "내보낸 인터페이스에 있는 메서드의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "내보낸 클래스에 있는 공용 메서드의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "내보낸 클래스에 있는 공용 정적 메서드의 형식 매개 변수 '{0}'이(가) 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "Type_parameter_declaration_expected_1139": "형식 매개 변수 선언이 필요합니다.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "형식 매개 변수 선언은 TypeScript 파일에서만 사용할 수 있습니다.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "형식 매개 변수 기본값은 이전에 선언된 형식 매개 변수만 참조할 수 있습니다.", "Type_parameter_list_cannot_be_empty_1098": "형식 매개 변수 목록은 비워 둘 수 없습니다.", "Type_parameter_name_cannot_be_0_2368": "형식 매개 변수 이름은 '{0}'일 수 없습니다.", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "형식 매개 변수는 생성자 선언에 표시될 수 없습니다.", "Type_predicate_0_is_not_assignable_to_1_1226": "형식 조건자 '{0}'을(를) '{1}'에 할당할 수 없습니다.", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "형식이 너무 커서 표시할 수 없는 튜플 형식을 생성합니다.", "Type_reference_directive_0_was_not_resolved_6120": "======== 형식 참조 지시문 '{0}'이(가) 확인되지 않았습니다. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== 형식 참조 지시문 '{0}'이(가) '{1}'(으)로 확인되었습니다. 주: {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== 형식 참조 지시문 '{0}'이(가) 패키지 ID가 '{2}'인 '{1}'(으)로 확인되었습니다. 주: {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "형식 만족 식은 TypeScript 파일에서만 사용할 수 있습니다.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "유형은 JavaScript 파일의 내보내기 선언에 나타날 수 없습니다.", "Types_have_separate_declarations_of_a_private_property_0_2442": "형식에 별도의 프라이빗 속성 '{0}' 선언이 있습니다.", "Types_of_construct_signatures_are_incompatible_2419": "구문 시그니처 형식이 호환되지 않습니다.", "Types_of_parameters_0_and_1_are_incompatible_2328": "'{0}' 및 '{1}' 매개 변수의 형식이 호환되지 않습니다.", "Types_of_property_0_are_incompatible_2326": "'{0}' 속성의 형식이 호환되지 않습니다.", "Unable_to_open_file_0_6050": "'{0}' 파일을 열 수 없습니다.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "식으로 호출된 경우 클래스 데코레이터의 서명을 확인할 수 없습니다.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "식으로 호출된 경우 메서드 데코레이터의 서명을 확인할 수 없습니다.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "식으로 호출된 경우 매개 변수 데코레이터의 서명을 확인할 수 없습니다.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "식으로 호출된 경우 속성 데코레이터의 서명을 확인할 수 없습니다.", "Undetermined_character_escape_1513": "결정되지 않은 문자 이스케이프입니다.", "Unexpected_0_Did_you_mean_to_escape_it_with_backslash_1508": "예기치 않은 '{0}'입니다. 백슬래시로 이스케이프하려고 하셨나요?", "Unexpected_end_of_text_1126": "예기치 않은 텍스트 끝입니다.", "Unexpected_keyword_or_identifier_1434": "예기치 않은 키워드 또는 식별자입니다.", "Unexpected_token_1012": "예기치 않은 토큰입니다.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "예기치 않은 토큰입니다. 생성자, 메서드, 접근자 또는 속성이 필요합니다.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "예기치 않은 토큰입니다. 중괄호가 없는 형식 매개 변수 이름이 필요합니다.", "Unexpected_token_Did_you_mean_or_gt_1382": "예기치 않은 토큰입니다. '{'>'}' 또는 '&gt;'를 사용하시겠습니까?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "예기치 않은 토큰입니다. '{'}'}' 또는 '&rbrace;'를 사용하시겠습니까?", "Unexpected_token_expected_1179": "예기치 않은 토큰입니다. '{'가 있어야 합니다.", "Unicode_escape_sequence_cannot_appear_here_17021": "유니코드 이스케이프 시퀀스는 여기에 나타날 수 없습니다.", "Unicode_escape_sequences_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v_flag_is_se_1538": "유니코드 이스케이프 시퀀스는 유니코드(u) 플래그 또는 유니코드 집합(v) 플래그가 설정된 경우에만 사용할 수 있습니다.", "Unicode_property_value_expressions_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v__1530": "유니코드 속성 값 식은 유니코드(u) 플래그 또는 유니코드 집합(v) 플래그가 설정된 경우에만 사용할 수 있습니다.", "Unknown_Unicode_property_name_1524": "알 수 없는 유니코드 속성 이름입니다.", "Unknown_Unicode_property_name_or_value_1529": "알 수 없는 유니코드 속성 이름 또는 값입니다.", "Unknown_Unicode_property_value_1526": "알 수 없는 유니코드 속성 값입니다.", "Unknown_build_option_0_5072": "알 수 없는 빌드 옵션 '{0}'입니다.", "Unknown_build_option_0_Did_you_mean_1_5077": "알 수 없는 빌드 옵션 '{0}'입니다. '{1}'을(를) 사용하시겠습니까?", "Unknown_compiler_option_0_5023": "알 수 없는 컴파일러 옵션 '{0}'입니다.", "Unknown_compiler_option_0_Did_you_mean_1_5025": "알 수 없는 컴파일러 옵션 '{0}'입니다. '{1}'을(를) 사용하시겠습니까?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "알 수 없는 키워드 또는 식별자입니다. '{0}'을(를) 의미했나요?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "알 수 없는 옵션 'excludes'입니다. 'exclude'를 사용하시겠습니까?", "Unknown_regular_expression_flag_1499": "알 수 없는 정규식 플래그입니다.", "Unknown_type_acquisition_option_0_17010": "알 수 없는 형식 인식 옵션 '{0}'입니다.", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "알 수 없는 형식 인식 옵션 '{0}'입니다. '{1}'을(를) 사용하시겠습니까?", "Unknown_watch_option_0_5078": "알 수 없는 조사식 옵션 '{0}'입니다.", "Unknown_watch_option_0_Did_you_mean_1_5079": "알 수 없는 조사식 옵션 '{0}'입니다. '{1}'을(를) 사용하시겠습니까?", "Unreachable_code_detected_7027": "접근할 수 없는 코드가 있습니다.", "Unterminated_Unicode_escape_sequence_1199": "종결되지 않은 유니코드 이스케이프 시퀀스입니다.", "Unterminated_quoted_string_in_response_file_0_6045": "응답 파일 '{0}'의 종결되지 않은 따옴표 붙은 문자열입니다.", "Unterminated_regular_expression_literal_1161": "종결되지 않은 정규식 리터럴입니다.", "Unterminated_string_literal_1002": "종결되지 않은 문자열 리터럴입니다.", "Unterminated_template_literal_1160": "종결되지 않은 템플릿 리터럴입니다.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "형식화되지 않은 함수 호출에는 형식 인수를 사용할 수 없습니다.", "Unused_label_7028": "사용되지 않는 레이블입니다.", "Unused_ts_expect_error_directive_2578": "사용되지 않는 '@ts-expect-error' 지시문입니다.", "Update_import_from_0_90058": "\"{0}\"에서 가져오기 업데이트", "Update_modifiers_of_0_90061": "'{0}'의 한정자 업데이트", "Updating_output_timestamps_of_project_0_6359": "'{0}' 프로젝트의 출력 타임스탬프를 업데이트하는 중...", "Updating_unchanged_output_timestamps_of_project_0_6371": "'{0}' 프로젝트의 변경되지 않은 출력 타임스탬프를 업데이트하는 중...", "Use_0_95174": "`{0}`을(를) 사용합니다.", "Use_0_instead_5106": "대신 '{0}'을(를) 사용하세요.", "Use_Number_isNaN_in_all_conditions_95175": "모든 조건에서 'Number.isNaN'을 사용합니다.", "Use_element_access_for_0_95145": "'{0}'에 요소 액세스 사용", "Use_element_access_for_all_undeclared_properties_95146": "선언되지 않은 모든 속성에 요소 액세스를 사용합니다.", "Use_import_type_95180": "'import type' 사용", "Use_synthetic_default_member_95016": "가상 '기본' 멤버를 사용합니다.", "Use_the_package_json_exports_field_when_resolving_package_imports_6408": "패키지 가져오기를 확인할 때 package.json 'exports' 필드를 사용합니다.", "Use_the_package_json_imports_field_when_resolving_imports_6409": "가져오기를 확인할 때 package.json 'imports' 필드를 사용합니다.", "Use_type_0_95181": "'형식 {0}' 사용", "Using_0_subpath_1_with_target_2_6404": "'{0}' 하위 경로 '{1}'과(와) 대상 '{2}'을(를) 사용하는 중입니다.", "Using_JSX_fragments_requires_fragment_factory_0_to_be_in_scope_but_it_could_not_be_found_2879": "JSX 조각을 사용하려면 조각 팩터리 '{0}'이(가) 범위에 있어야 하지만 찾을 수 없습니다.", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "ECMAScript 5 이상에서만 'for...of' 문에서 문자열을 사용할 수 있습니다.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "--build를 사용하면 -b가 tsc로 하여금 컴파일러보다 빌드 오케스트레이터처럼 작동하도록 합니다. 이 항목은 {0}에서 더 자세히 알아볼 수 있는 복합 프로젝트를 구축하는 데 사용됩니다.", "Using_compiler_options_of_project_reference_redirect_0_6215": "프로젝트 참조 리디렉션 '{0}'의 컴파일러 옵션을 사용 중입니다.", "VERSION_6036": "버전", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "'{0}' 형식의 값에 '{1}' 형식과 공통된 속성이 없습니다. 속성을 호출하려고 했습니까?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "'{0}' 형식의 값은 호출할 수 없습니다. 'new'를 포함하려고 했습니까?", "Variable_0_implicitly_has_an_1_type_7005": "'{0}' 변수에는 암시적으로 '{1}' 형식이 포함됩니다.", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "'{0}' 변수는 암시적으로 '{1}' 형식이지만, 사용량에서 더 나은 형식을 유추할 수 있습니다.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "'{0}' 변수는 몇몇 위치에서 암시적으로 '{1}' 형식이지만, 사용량에서 더 나은 형식을 유추할 수 있습니다.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "'{0}' 변수는 형식을 확인할 수 없는 경우 일부 위치에서 암시적으로 '{1}' 형식입니다.", "Variable_0_is_used_before_being_assigned_2454": "'{0}' 변수가 할당되기 전에 사용되었습니다.", "Variable_declaration_expected_1134": "변수 선언이 필요합니다.", "Variable_declaration_list_cannot_be_empty_1123": "변수 선언 목록은 비워 둘 수 없습니다.", "Variable_declaration_not_allowed_at_this_location_1440": "이 위치에서는 변수 선언을 사용할 수 없습니다.", "Variable_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9010": "변수에는 --isolatedDeclarations가 있는 명시적 형식 주석이 있어야 합니다.", "Variables_with_multiple_declarations_cannot_be_inlined_95186": "선언이 여러 개 있는 변수는 인라인 처리할 수 없습니다.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "소스의 {0} 위치에 있는 가변 인자 요소가 대상의 {1} 위치에 있는 요소와 일치하지 않습니다.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "차이 주석은 개체, 함수, 생성자 및 매핑된 형식의 형식 별칭에서만 지원됩니다.", "Version_0_6029": "버전 {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "이 파일에 대해 자세히 알아보려면 https://aka.ms/tsconfig을 방문하세요.", "WATCH_OPTIONS_6918": "조사식 옵션", "Watch_and_Build_Modes_6250": "시청 및 빌드 모드", "Watch_input_files_6005": "조사식 입력 파일입니다.", "Watch_option_0_requires_a_value_of_type_1_5080": "조사식 옵션 '{0}'에 {1} 형식의 값이 필요합니다.", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "여기에서 전체 매개 변수에 대한 형식을 추가하여 '{0}'에 대한 형식만 작성할 수 있습니다.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "함수를 할당할 때 매개 변수와 반환 값이 하위 형식과 호환되는지 확인합니다.", "When_type_checking_take_into_account_null_and_undefined_6699": "유형 검사 시 'null' 및 'undefined'를 고려하세요.", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "화면을 지우지 않고, 감시 모드의 오래된 콘솔 출력을 유지할지 여부입니다.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "식 컨테이너에서 모든 잘못된 문자 래핑", "Wrap_all_invalid_decorator_expressions_in_parentheses_95195": "잘못된 데코레이터 식을 모두 괄호로 래핑합니다.", "Wrap_all_object_literal_with_parentheses_95116": "괄호를 사용하여 모든 개체 리터럴 래핑", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "JSX 조각에서 부모가 지정되지 않은 모든 JSX 래핑", "Wrap_in_JSX_fragment_95120": "JSX 조각에서 래핑", "Wrap_in_parentheses_95194": "괄호로 래핑", "Wrap_invalid_character_in_an_expression_container_95108": "식 컨테이너에서 잘못된 문자 래핑", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "괄호를 사용하여 개체 리터럴이어야 하는 다음 본문 래핑", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "{0}에서 모든 컴파일러 옵션에 대해 알아볼 수 있습니다.", "You_cannot_rename_a_module_via_a_global_import_8031": "전역 가져오기를 통해 모듈 이름을 바꿀 수 없습니다.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "'node_modules' 폴더에 정의된 요소의 이름은 변경할 수 없습니다.", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "다른 'node_modules' 폴더에 정의된 요소의 이름은 변경할 수 없습니다.", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "표준 TypeScript 라이브러리에 정의된 요소의 이름을 바꿀 수 없습니다.", "You_cannot_rename_this_element_8000": "이 요소의 이름을 바꿀 수 없습니다.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "'{0}'이(가) 여기에서 decorator로 사용할 인수를 너무 적게 허용합니다. 먼저 이를 호출하고 '@{0}()'을(를) 작성하시겠습니까?", "_0_and_1_index_signatures_are_incompatible_2330": "'{0}' 및 '{1}' 인덱스 시그니처가 호환되지 않습니다.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "'{0}' 및 '{1}' 작업은 괄호 없이 혼합해서 사용할 수 없습니다.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "'{0}'이(가) 두 번 지정되었습니다. 이름이 '{0}'인 특성을 덮어씁니다.", "_0_at_the_end_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17019": "형식의 끝에 있는 '{0}'은(는) 올바른 TypeScript 구문이 아닙니다. '{1}'을(를) 쓰려고 하셨나요?", "_0_at_the_start_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17020": "형식의 시작 부분에 있는 '{0}'은(는) 올바른 TypeScript 구문이 아닙니다. '{1}'을(를) 쓰려고 하셨나요?", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "'{0}'은(는) 'esModuleInterop' 플래그를 설정하고 기본 가져오기를 사용해서만 가져올 수 있습니다.", "_0_can_only_be_imported_by_using_a_default_import_2595": "'{0}'은(는) 기본 가져오기를 사용해서만 가져올 수 있습니다.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "'{0}'은(는) 'require' 호출을 사용하거나 'esModuleInterop' 플래그를 설정하고 기본 가져오기를 사용해서만 가져올 수 있습니다.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "'{0}'은(는) 'require' 호출을 사용하거나 기본 가져오기를 사용해서만 가져올 수 있습니다.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "'{0}'은(는) 'import {1} = require({2})' 또는 기본 가져오기를 사용해서만 가져올 수 있습니다.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "'{0}'은(는) 'import {1} = require({2})'를 사용하거나 'esModuleInterop' 플래그를 설정하고 기본 가져오기를 사용해서만 가져올 수 있습니다.", "_0_cannot_be_used_as_a_JSX_component_2786": "'{0}'은(는) JSX 구성 요소로 사용할 수 없습니다.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "'{0}'은(는) 'export type'을 사용하여 내보냈으므로 값으로 사용할 수 없습니다.", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "'{0}'은(는) 'import type'을 사용하여 가져왔으므로 값으로 사용할 수 없습니다.", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "'{0}' 구성 요소는 텍스트를 자식 요소로 수락하지 않습니다. JSX의 텍스트는 'string' 형식이지만, '{1}'의 필요한 형식은 '{2}'입니다.", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "'{0}'은(는) '{1}'과(와) 관련되지 않은 임의의 형식으로 인스턴스화할 수 있습니다.", "_0_declarations_can_only_be_declared_inside_a_block_1156": "'{0}' 선언은 블록 내에서만 선언할 수 있습니다.", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "'{0}' 선언은 TypeScript 파일에서만 사용할 수 있습니다.", "_0_declarations_may_not_have_binding_patterns_1492": "'{0}' 선언에는 바인딩 패턴이 없을 수 있습니다.", "_0_declarations_must_be_initialized_1155": "'{0}' 선언을 초기화해야 합니다.", "_0_expected_1005": "'{0}'이(가) 필요합니다.", "_0_has_a_string_type_but_must_have_syntactically_recognizable_string_syntax_when_isolatedModules_is__18055": "'{0}'에는 문자열 형식이 있지만 'isolatedModules'를 사용하도록 설정한 경우 구문으로 인식할 수 있는 문자열 구문이 있어야 합니다.", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "'{0}'에 내보낸 멤버 '{1}'이(가) 없습니다. '{2}'이(가) 아닌지 확인하세요.", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "'{0}'은(는) 암시적으로 '{1}' 반환 형식이지만, 사용량에서 더 나은 형식을 유추할 수 있습니다.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "반환 형식 주석이 없고 반환 식 중 하나에서 직간접적으로 참조되므로 '{0}'에는 암시적으로 'any' 반환 형식이 포함됩니다.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "'{0}'은(는) 형식 주석이 없고 자체 이니셜라이저에서 직간접적으로 참조되므로 암시적으로 'any' 형식입니다.", "_0_index_signatures_are_incompatible_2634": "'{0}' 인덱스 시그니처가 호환되지 않습니다.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "'{0}' 인덱스 유형 '{1}'을(를) '{2}' 인텍스 유형 '{3}'에 할당할 수 없습니다.", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "{0}'은(는) 기본 개체이지만 '{1}'은(는) 래퍼 개체입니다. 가능한 경우 '{0}'을(를) 사용하세요.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "'{0}'은(는) 유형이며 JavaScript 파일로 가져올 수 없습니다. JSDoc 유형 주석에서 '{1}'을(를) 사용하세요.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_verbatimModuleSyntax_is_enabled_1484": "'{0}'은(는) 형식이며 'verbatimModuleSyntax'를 사용하도록 설정한 경우 형식 전용 가져오기를 사용하여 가져와야 합니다.", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "'{0}'은(는) '{1}'의 사용되지 않는 이름 변경입니다. 형식 주석으로 사용하려고 했습니까?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "'{0}'은(는) '{1}' 형식의 제약 조건에 할당할 수 있지만, '{1}'은(는) '{2}' 제약 조건의 다른 하위 형식으로 인스턴스화할 수 있습니다.", "_0_is_automatically_exported_here_18044": "'{0}'은(는) 여기에서 자동으로 내보내집니다.", "_0_is_declared_but_its_value_is_never_read_6133": "'{0}'이(가) 선언은 되었지만 해당 값이 읽히지는 않았습니다.", "_0_is_declared_but_never_used_6196": "'{0}'이(가) 선언되었지만 사용되지 않았습니다.", "_0_is_declared_here_2728": "여기서는 '{0}'이(가) 선언됩니다.", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "'{0}'은(는) '{1}' 클래스의 속성으로 정의되지만, '{2}'에서 접근자로 재정의됩니다.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "'{0}'은(는) '{1}' 클래스의 접근자로 정의되지만, '{2}'에서 인스턴스 속성으로 재정의됩니다.", "_0_is_deprecated_6385": "'{0}'은(는) 사용되지 않습니다.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "'{0}'은(는) '{1}' 키워드에 대한 올바른 메타 속성이 아닙니다. '{2}'을(를) 사용하시겠습니까?", "_0_is_not_allowed_as_a_parameter_name_1390": "'{0}'은(는) 매개 변수 이름으로 사용할 수 없습니다.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "'{0}'은(는) 변수 선언 이름으로 사용할 수 없습니다.", "_0_is_of_type_unknown_18046": "'{0}'은(는) 'unknown' 형식입니다.", "_0_is_possibly_null_18047": "'{0}'은(는) 'null'일 수 있습니다.", "_0_is_possibly_null_or_undefined_18049": "'{0}'은(는) 'null' 또는 'undefined'일 수 있습니다.", "_0_is_possibly_undefined_18048": "'{0}'은(는) 'undefined'일 수 있습니다.", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "'{0}'은(는) 자체 기본 식에서 직간접적으로 참조됩니다.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "'{0}'은(는) 자체 형식 주석에서 직간접적으로 참조됩니다.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "'{0}'이(가) 두 번 이상 지정되어 이 사용량을 덮어씁니다.", "_0_list_cannot_be_empty_1097": "'{0}' 목록은 비워 둘 수 없습니다.", "_0_modifier_already_seen_1030": "'{0}' 한정자가 이미 있습니다.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "'{0}' 한정자는 클래스, 인터페이스 또는 형식 별칭의 형식 매개 변수에만 나타날 수 있습니다.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_function_method_or_class_1277": "'{0}' 한정자는 함수, 메서드 또는 클래스의 형식 매개 변수에만 나타날 수 있습니다.", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "'{0}' 한정자는 생성자 선언에 나타날 수 없습니다.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "'{0}' 한정자는 모듈 또는 네임스페이스 요소에 나타날 수 없습니다.", "_0_modifier_cannot_appear_on_a_parameter_1090": "{0}' 한정자는 매개 변수에 표시될 수 없습니다.", "_0_modifier_cannot_appear_on_a_type_member_1070": "'{0}' 한정자는 형식 멤버에 나타날 수 없습니다.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "'{0}' 한정자는 형식 매개 변수에 나타날 수 없습니다.", "_0_modifier_cannot_appear_on_a_using_declaration_1491": "'{0}' 한정자는 'using' 선언에 나타날 수 없습니다.", "_0_modifier_cannot_appear_on_an_await_using_declaration_1495": "'{0}' 한정자는 'await using' 선언에 나타날 수 없습니다.", "_0_modifier_cannot_appear_on_an_index_signature_1071": "'{0}' 한정자는 인덱스 시니그처에 나타날 수 없습니다.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "이 종류의 클래스 요소에는 '{0}' 한정자를 표시할 수 없습니다.", "_0_modifier_cannot_be_used_here_1042": "'{0}' 한정자는 여기에 사용할 수 없습니다.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "'{0}' 한정자는 앰비언트 컨텍스트에서 사용할 수 없습니다.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "'{0}' 한정자는 '{1}' 한정자와 함께 사용할 수 없습니다.", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "'{0}' 한정자는 프라이빗 식별자와 함께 사용할 수 없습니다.", "_0_modifier_must_precede_1_modifier_1029": "'{0}' 한정자는 '{1}' 한정자 앞에 와야 합니다.", "_0_must_be_followed_by_a_Unicode_property_value_expression_enclosed_in_braces_1531": "'\\{0}' 뒤에는 중괄호로 묶인 유니코드 속성 값 식이 와야 합니다.", "_0_needs_an_explicit_type_annotation_2782": "'{0}'에는 명시적 형식 주석이 필요합니다.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "'{0}'은(는) 형식만 참조하지만, 여기서는 네임스페이스로 사용되고 있습니다.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "'{0}'은(는) 형식만 참조하지만, 여기서는 값으로 사용되고 있습니다.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "'{0}'은(는) 형식만 참조하는데 여기에서 값으로 사용되고 있습니다. '{0}에서 {1}'을(를) 사용하려고 하셨습니까?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "'{0}'은(는) 형식만 참조하지만, 여기서는 값으로 사용되고 있습니다. 대상 라이브러리를 변경하려는 경우 'lib' 컴파일러 옵션을 es2015 이상으로 변경해 보세요.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "'{0}'은(는) UMD 전역을 참조하지만 현재 파일은 모듈입니다. 대신 가져오기를 추가해 보세요.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "'{0}'은(는) 값을 참조하지만, 여기서는 형식으로 사용되고 있습니다. 'typeof {0}'을(를) 사용하시겠습니까?", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1291": "'{0}'은(는) 형식으로 확인되며 '{1}'이(가) 사용하도록 설정된 경우 다시 내보내기 전에 이 파일에서 형식 전용으로 표시되어야 합니다. '{0}'을(를) 가져오는 'import type'을 사용해 보세요.", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1292": "'{0}'은(는) 형식으로 확인되며 '{1}'이(가) 사용하도록 설정된 경우 다시 내보내기 전에 이 파일에서 형식 전용으로 표시되어야 합니다. 'export type {기본값 {0}}'을(를) 사용해 보세요.", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_verbatimMo_1485": "'{0}'은(는) 형식 전용 선언으로 확인되며 'verbatimModuleSyntax'를 사용하도록 설정한 경우 형식 전용 가져오기를 사용하여 가져와야 합니다.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1289": "'{0}'은(는) 형식 전용 선언으로 확인되며 '{1}'이(가) 사용하도록 설정된 경우 다시 내보내기 전에 이 파일에서 형식 전용으로 표시되어야 합니다. '{0}'을(를) 가져오는 'import type'을 사용해 보세요.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1290": "'{0}'은(는) 형식 전용 선언으로 확인되며 '{1}'이(가) 사용하도록 설정된 경우 다시 내보내기 전에 이 파일에서 형식 전용으로 표시되어야 합니다. 'export type {기본값 {0}}'을(를) 사용해 보세요.", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_1_is_1448": "'{0}'은(는) 형식 전용 선언으로 확인되며, '{1}'을(를) 사용하도록 설정한 경우 형식 전용 다시 내보내기를 사용하여 다시 내보내야 합니다.", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "'{0}'은(는) 구성 json 파일의 'compilerOptions' 개체 내에 설정해야 합니다.", "_0_tag_already_specified_1223": "'{0}' 태그가 이미 지정되었습니다.", "_0_was_also_declared_here_6203": "여기서도 '{0}'이(가) 선언되었습니다.", "_0_was_exported_here_1377": "여기서는 '{0}'을(를) 내보냈습니다.", "_0_was_imported_here_1376": "여기서는 '{0}'을(를) 가져왔습니다.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "반환 형식 주석이 없는 '{0}'에는 암시적으로 '{1}' 반환 형식이 포함됩니다.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "반환 형식 주석이 없는 '{0}'에는 암시적으로 '{1}' yield 형식이 포함됩니다.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "'abstract' 한정자는 클래스, 메서드 또는 속성 선언에만 나타날 수 있습니다.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "'accessor' 한정자는 속성 선언에만 나타날 수 있습니다.", "and_here_6204": "및 여기.", "arguments_cannot_be_referenced_in_property_initializers_2815": "'arguments'는 속성 이니셜라이저에서 참조할 수 없습니다.", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "\"auto\": 가져오기, 내보내기, import.meta, jsx(jsx: react-jsx 포함) 또는 esm 형식(모듈: node16+ 포함)이 있는 파일을 모듈로 처리합니다.", "await_expression_cannot_be_used_inside_a_class_static_block_18037": "'await' 식은 클래스 정적 블록 내에서 사용할 수 없습니다.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "'await' 식은 파일이 모듈일 경우 해당 파일의 최상위 수준에서만 사용할 수 있지만, 이 파일에는 가져오기 또는 내보내기가 없습니다. 빈 'export {}'를 추가하여 이 파일을 모듈로 만드는 것이 좋습니다.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "'await' 식은 비동기 함수 내부 및 모듈의 최상위 수준에서만 사용할 수 있습니다.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "'await' 식은 매개 변수 이니셜라이저에서 사용할 수 없습니다.", "await_has_no_effect_on_the_type_of_this_expression_80007": "'await'는 이 식의 형식에 영향을 주지 않습니다.", "await_using_statements_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_th_2853": "'await using' 문은 파일이 모듈일 경우 해당 파일의 최상위에서만 사용할 수 있지만, 이 파일에는 가져오기 또는 내보내기가 없습니다. 빈 'export {}'를 추가하여 이 파일을 모듈로 만드는 것이 좋습니다.", "await_using_statements_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_2852": "'await using' 문은 비동기 함수 내부 및 모듈의 최상위 수준에서만 사용할 수 있습니다.", "await_using_statements_cannot_be_used_inside_a_class_static_block_18054": "'await using' 문은 클래스 정적 블록 내에서 사용할 수 없습니다.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "'baseUrl' 옵션이 '{0}'(으)로 설정되어 있습니다. 상대적이지 않은 모듈 이름 '{1}'을(를) 확인하려면 이 값을 사용합니다.", "c_must_be_followed_by_an_ASCII_letter_1512": "'\\c' 뒤에는 ASCII 문자가 와야 합니다.", "can_only_be_used_at_the_start_of_a_file_18026": "'#!'는 파일의 시작 부분에서만 사용할 수 있습니다.", "case_or_default_expected_1130": "'case' 또는 'default'가 필요합니다.", "catch_or_finally_expected_1472": "'catch' 또는 'finally'가 필요합니다.", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "'const' 열거형 멤버 이니셜라이저가 무한 값에 대해 평가되었습니다.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "'const' 열거형 멤버 이니셜라이저가 허용되지 않은 'NaN' 값에 대해 평가되었습니다.", "const_enum_member_initializers_must_be_constant_expressions_2474": "const 열거형 멤버 이니셜라이저는 상수 식이어야 합니다.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "'const' 열거형은 속성이나 인덱스 액세스 식, 또는 내보내기 할당이나 가져오기 선언의 오른쪽, 또는 형식 쿼리에서만 사용할 수 있습니다.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "'constructor'는 매개 변수 속성 이름으로 사용할 수 없습니다.", "constructor_is_a_reserved_word_18012": "'#constructor'는 예약어입니다.", "default_Colon_6903": "기본값:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "strict 모드에서는 식별자에 대해 'delete'를 호출할 수 없습니다.", "export_Asterisk_does_not_re_export_a_default_1195": "'export *'는 기본값을 다시 내보내지 않습니다.", "export_can_only_be_used_in_TypeScript_files_8003": "'export ='는 TypeScript 파일에서만 사용할 수 있습니다.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "앰비언트 모듈 및 모듈 확대는 항상 표시되므로 'export' 한정자를 적용할 수 없습니다.", "extends_clause_already_seen_1172": "'extends' 절이 이미 있습니다.", "extends_clause_must_precede_implements_clause_1173": "'extends' 절은 'implements' 절 앞에 와야 합니다.", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "내보낸 클래스 '{0}'의 Extends 절이 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "내보낸 클래스의 'extends' 절이 프라이빗 이름 '{0}'을(를) 가지고 있거나 사용 중입니다.", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "내보낸 인터페이스 '{0}'의 Extends 절이 프라이빗 이름 '{1}'을(를) 가지고 있거나 사용 중입니다.", "false_unless_composite_is_set_6906": "'composite'가 설정되지 않은 한 'false'입니다.", "false_unless_strict_is_set_6905": "'strict'가 설정되지 않은 한 'false'입니다.", "file_6025": "파일", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "'for await' 루프는 파일이 모듈일 경우 해당 파일의 최상위에서만 사용할 수 있지만, 이 파일에는 가져오기 또는 내보내기가 없습니다. 빈 'export {}'를 추가하여 이 파일을 모듈로 만드는 것이 좋습니다.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "'for await' 루프는 비동기 함수 내부 및 모듈의 최상위에서만 사용할 수 있습니다.", "for_await_loops_cannot_be_used_inside_a_class_static_block_18038": "'for await' 루프는 클래스 정적 블록 내에서 사용할 수 없습니다.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "'get' 및 'set' 접근자는 'this' 매개 변수를 선언할 수 없습니다.", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "'files'를 지정하면 '[]'이고, 그렇지 않으면 '[\"**/*\"]5D;'", "implements_clause_already_seen_1175": "'implements' 절이 이미 있습니다.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "'implements' 절은 TypeScript 파일에서만 사용할 수 있습니다.", "import_can_only_be_used_in_TypeScript_files_8002": "'import ... ='는 TypeScript 파일에서만 사용할 수 있습니다.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "'infer' 선언은 조건 형식의 'extends' 절에서만 사용할 수 있습니다.", "k_must_be_followed_by_a_capturing_group_name_enclosed_in_angle_brackets_1510": "'\\k' 뒤에는 꺾쇠 괄호로 묶인 캡처 그룹 이름이 와야 합니다.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "'let'은 'let' 또는 'const' 선언에서 이름으로 사용할 수 없습니다.", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "모듈 === 'AMD' 또는 'UMD' 또는 'System' 또는 'ES6', 'Classic', 그렇지 않으면 'Node'", "module_system_or_esModuleInterop_6904": "모듈 === \"system\" 또는 esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "대상에 구문 시그니처가 없는 'new' 식에는 암시적으로 'any' 형식이 포함됩니다.", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "'[\"node_modules\", \"bower_components\", \"jspm_packages\"]', 지정한 경우 'outDir' 값이 추가됩니다.", "one_of_Colon_6900": "다음 중 하나:", "one_or_more_Colon_6901": "하나 이상:", "options_6024": "옵션", "or_JSX_element_expected_1145": "'{' 또는 JSX 요소가 필요합니다.", "or_expected_1144": "'{' 또는 ';'이(가) 필요합니다.", "package_json_does_not_have_a_0_field_6100": "'package.json'에는 '{0}' 필드가 없습니다.", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "'package.json'에 '{0}' 버전과 일치하는 'typesVersions' 항목이 없습니다.", "package_json_had_a_falsy_0_field_6220": "'package.json'에 false로 평가되는 '{0}' 필드가 있습니다.", "package_json_has_0_field_1_that_references_2_6101": "'package.json'에 '{2}'을(를) 참조하는 '{0}' 필드 '{1}'이(가) 있습니다.", "package_json_has_a_peerDependencies_field_6281": "'package.json'에 'peerDependencies' 필드가 있습니다.", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "'package.json'에 유효한 semver 범위가 아닌 'typesVersions' 항목 '{0}'이(가) 있습니다.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "모듈 이름 '{2}'과(와) 일치하는 패턴을 검색하는 컴파일러 버전 '{1}'과(와) 일치하는 'typesVersions' 항목 '{0}'이(가) 'package.json'에 있습니다.", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "'package.json'에 버전별 경로 매핑이 포함된 'typesVersions' 필드가 있습니다.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "package.json 범위 '{0}'은(는) 명시적으로 '{1}' 지정자를 null에 매핑합니다.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "package.json 범위 '{0}'에 '{1}' 지정자의 대상 유형이 잘못되었습니다.", "package_json_scope_0_has_no_imports_defined_6273": "package.json 범위 '{0}'에 정의된 가져오기가 없습니다.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "'paths' 옵션이 지정되었습니다. 모듈 이름 '{0}'과(와) 일치하는 패턴을 찾는 중입니다.", "q_is_only_available_inside_character_class_1511": "'\\q'는 문자 클래스 내에서만 사용할 수 있습니다.", "q_must_be_followed_by_string_alternatives_enclosed_in_braces_1521": "'\\q' 뒤에는 중괄호로 묶인 문자열 대체가 와야 합니다.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "'readonly' 한정자는 속성 선언 또는 인덱스 시그니처에만 나타날 수 있습니다.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "'readonly' 형식 한정자는 배열 및 튜플 리터럴 형식에서만 사용할 수 있습니다.", "require_call_may_be_converted_to_an_import_80005": "'require' 호출이 가져오기로 변환될 수 있습니다.", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "'resolution-mode'는 유형 전용 가져오기에만 설정할 수 있습니다.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "'resolution-mode'는 유형 가져오기 어설션에 유효한 유일한 키입니다.", "resolution_mode_is_the_only_valid_key_for_type_import_attributes_1463": "'resolution-mode'는 형식 가져오기 특성에 사용할 수 있는 유일한 키입니다.", "resolution_mode_should_be_either_require_or_import_1453": "'해상도 모드'는 '요구' 또는 '가져오기'여야 합니다.", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "'rootDirs' 옵션이 설정되어 있습니다. 상대 모듈 이름 '{0}'을(를) 확인하려면 이 옵션을 사용합니다.", "super_can_only_be_referenced_in_a_derived_class_2335": "파생 클래스에서만 'super'를 참조할 수 있습니다.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "파생 클래스 또는 개체 리터럴 식의 멤버에서만 'super'를 참조할 수 있습니다.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "'super'는 계산된 속성 이름에서 참조할 수 없습니다.", "super_cannot_be_referenced_in_constructor_arguments_2336": "super'는 생성자 인수에서 참조할 수 없습니다.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "옵션 'target'이 'ES2015' 이상인 경우 개체 리터럴 식의 멤버에서만 'super'를 사용할 수 있습니다.", "super_may_not_use_type_arguments_2754": "'super'는 형식 인수를 사용할 수 없습니다.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "파생 클래스의 생성자에서 'super'의 속성에 액세스하기 전에 'super'를 호출해야 합니다.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "파생 클래스의 생성자에서 'this'에 액세스하기 전에 'super'를 호출해야 합니다.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "'super' 다음에는 인수 목록 또는 멤버 액세스가 와야 합니다.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "'super' 속성 액세스는 생성자, 멤버 함수 또는 파생 클래스의 멤버 접근자에서만 허용됩니다.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "'this'는 계산된 속성 이름에서 참조할 수 없습니다.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "'this'는 모듈 또는 네임스페이스 본문에서 참조될 수 없습니다.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "정적 속성 이니셜라이저에서 'this'를 참조할 수 없습니다.", "this_cannot_be_referenced_in_current_location_2332": "현재 위치에서 'this'를 참조할 수 없습니다.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "'this'에는 형식 주석이 없으므로 암시적으로 'any' 형식이 포함됩니다.", "true_for_ES2022_and_above_including_ESNext_6930": "ESNext를 포함하여 ES2022 이상의 경우 'true'입니다.", "true_if_composite_false_otherwise_6909": "'composite'이면 'true'이고, 그렇지 않으면 'false'입니다.", "true_when_moduleResolution_is_node16_nodenext_or_bundler_otherwise_false_6411": "'moduleResolution'이 'node16', 'nodenext' 또는 'bundler'인 경우 'true'입니다. 그렇지 않으면 'false'입니다.", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: TypeScript 컴파일러", "type_Colon_6902": "형식:", "unique_symbol_types_are_not_allowed_here_1335": "여기에서 'unique symbol' 형식은 허용되지 않습니다.", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "'unique symbol' 형식은 변수 문의 변수에만 허용됩니다.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "'unique symbol' 형식은 바인딩 이름과 함께 변수 선언에 사용할 수 없습니다.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "'use strict' 지시문은 단순하지 않은 매개 변수 목록에서 사용할 수 없습니다.", "use_strict_directive_used_here_1349": "여기서는 'use strict' 지시문이 사용됩니다.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "'with' 문은 비동기 함수 블록에서 사용할 수 없습니다.", "with_statements_are_not_allowed_in_strict_mode_1101": "'with' 문은 strict 모드에서 사용할 수 없습니다.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "'yield' 식은 포함하는 생성기에 반환 형식 주석이 없으므로 암시적으로 'any' 형식으로 생성됩니다.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "'yield' 식은 매개 변수 이니셜라이저에서 사용할 수 없습니다."}