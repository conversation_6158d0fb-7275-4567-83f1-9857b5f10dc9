// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`vineResolver > should return a single error from vineResolver when validation fails 1`] = `
{
  "errors": {
    "accessToken": {
      "message": "Invalid value provided for accessToken field",
      "ref": undefined,
      "type": "unionOfTypes",
    },
    "birthYear": {
      "message": "The birthYear field must be a number",
      "ref": undefined,
      "type": "number",
    },
    "dateStr": {
      "message": "The dateStr field must be defined",
      "ref": undefined,
      "type": "required",
    },
    "email": {
      "message": "The email field must be a valid email address",
      "ref": {
        "name": "email",
      },
      "type": "email",
    },
    "enabled": {
      "message": "The enabled field must be defined",
      "ref": undefined,
      "type": "required",
    },
    "like": [
      {
        "id": {
          "message": "The id field must be a number",
          "ref": undefined,
          "type": "number",
        },
        "name": {
          "message": "The name field must be defined",
          "ref": undefined,
          "type": "required",
        },
      },
    ],
    "password": {
      "message": "The password field format is invalid",
      "ref": {
        "name": "password",
      },
      "type": "regex",
    },
    "repeatPassword": {
      "message": "The repeatPassword field must be defined",
      "ref": undefined,
      "type": "required",
    },
    "tags": {
      "message": "The tags field must be defined",
      "ref": undefined,
      "type": "required",
    },
    "username": {
      "message": "The username field must be defined",
      "ref": {
        "name": "username",
      },
      "type": "required",
    },
  },
  "values": {},
}
`;

exports[`vineResolver > should return all the errors from vineResolver when validation fails with \`validateAllFieldCriteria\` set to true 1`] = `
{
  "errors": {
    "accessToken": {
      "message": "Invalid value provided for accessToken field",
      "ref": undefined,
      "type": "unionOfTypes",
      "types": {
        "unionOfTypes": "Invalid value provided for accessToken field",
      },
    },
    "birthYear": {
      "message": "The birthYear field must be a number",
      "ref": undefined,
      "type": "number",
      "types": {
        "number": "The birthYear field must be a number",
      },
    },
    "dateStr": {
      "message": "The dateStr field must be defined",
      "ref": undefined,
      "type": "required",
      "types": {
        "required": "The dateStr field must be defined",
      },
    },
    "email": {
      "message": "The email field must be a valid email address",
      "ref": {
        "name": "email",
      },
      "type": "email",
      "types": {
        "email": "The email field must be a valid email address",
      },
    },
    "enabled": {
      "message": "The enabled field must be defined",
      "ref": undefined,
      "type": "required",
      "types": {
        "required": "The enabled field must be defined",
      },
    },
    "like": [
      {
        "id": {
          "message": "The id field must be a number",
          "ref": undefined,
          "type": "number",
          "types": {
            "number": "The id field must be a number",
          },
        },
        "name": {
          "message": "The name field must be defined",
          "ref": undefined,
          "type": "required",
          "types": {
            "required": "The name field must be defined",
          },
        },
      },
    ],
    "password": {
      "message": "The password field format is invalid",
      "ref": {
        "name": "password",
      },
      "type": "regex",
      "types": {
        "regex": "The password field format is invalid",
      },
    },
    "repeatPassword": {
      "message": "The repeatPassword field must be defined",
      "ref": undefined,
      "type": "required",
      "types": {
        "required": "The repeatPassword field must be defined",
      },
    },
    "tags": {
      "message": "The tags field must be defined",
      "ref": undefined,
      "type": "required",
      "types": {
        "required": "The tags field must be defined",
      },
    },
    "username": {
      "message": "The username field must be defined",
      "ref": {
        "name": "username",
      },
      "type": "required",
      "types": {
        "required": "The username field must be defined",
      },
    },
  },
  "values": {},
}
`;
