/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [["path", { d: "M7 22V2l10 5-10 5", key: "17n18y" }]];
const FlagTriangleRight = createLucideIcon("flag-triangle-right", __iconNode);

export { __iconNode, FlagTriangleRight as default };
//# sourceMappingURL=flag-triangle-right.js.map
