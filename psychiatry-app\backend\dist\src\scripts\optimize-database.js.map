{"version": 3, "file": "optimize-database.js", "sourceRoot": "", "sources": ["../../../src/scripts/optimize-database.ts"], "names": [], "mappings": ";;AAoMS,4CAAgB;AApMzB,2CAA8C;AAE9C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAOlC,KAAK,UAAU,gBAAgB;IAC7B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IAEpD,IAAI,CAAC;QAIH,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAG5D,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAClD,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACtD,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAC1D,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAEpD,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,kBAAkB,YAAY,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,qBAAqB,cAAc,EAAE,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,sBAAsB,gBAAgB,EAAE,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,oBAAoB,aAAa,EAAE,CAAC,CAAC;QAGjD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAG9C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAG7B,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YAC7B,KAAK,EAAE,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;SACzC,CAAC,CAAC;QAGH,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACvD,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;gBAC9B,KAAK,EAAE,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,EAAE;gBACtC,OAAO,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;aAC9B,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QACrE,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YAChC,KAAK,EAAE;gBACL,IAAI,EAAE;oBACJ,GAAG,EAAE,KAAK;oBACV,GAAG,EAAE,QAAQ;iBACd;aACF;SACF,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,iCAAiC,SAAS,IAAI,CAAC,CAAC;QAG5D,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAExD,MAAM,eAAe,GAAG;YACtB;gBACE,KAAK,EAAE,SAAS;gBAChB,OAAO,EAAE;oBACP,6EAA6E;oBAC7E,oEAAoE;oBACpE,mFAAmF;oBACnF,qFAAqF;oBACrF,0EAA0E;iBAC3E;aACF;YACD;gBACE,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE;oBACP,8EAA8E;oBAC9E,0EAA0E;oBAC1E,0EAA0E;oBAC1E,0EAA0E;oBAC1E,oGAAoG;iBACrG;aACF;YACD;gBACE,KAAK,EAAE,aAAa;gBACpB,OAAO,EAAE;oBACP,kFAAkF;oBAClF,oFAAoF;oBACpF,0EAA0E;oBAC1E,8EAA8E;oBAC9E,iGAAiG;iBAClG;aACF;YACD;gBACE,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE;oBACP,8EAA8E;oBAC9E,oFAAoF;oBACpF,wFAAwF;oBACxF,iGAAiG;iBAClG;aACF;YACD;gBACE,KAAK,EAAE,UAAU;gBACjB,OAAO,EAAE;oBACP,wFAAwF;oBACxF,sEAAsE;oBACtE,8EAA8E;oBAC9E,qFAAqF;iBACtF;aACF;YACD;gBACE,KAAK,EAAE,sBAAsB;gBAC7B,OAAO,EAAE;oBACP,yFAAyF;oBACzF,2FAA2F;oBAC3F,oGAAoG;oBACpG,kGAAkG;iBACnG;aACF;SACF,CAAC;QAEF,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE;YAC7C,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC;YACtC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACtB,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,oEAAoE,CAAC,CAAC;QAClF,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,uEAAuE,CAAC,CAAC;QACrF,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAElE,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAGlD,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QACnF,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QACrF,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QAClF,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QAExE,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;IAEtE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,MAAM,KAAK,CAAC;IACd,CAAC;YAAS,CAAC;QACT,MAAM,MAAM,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,gBAAgB,EAAE;SACf,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}