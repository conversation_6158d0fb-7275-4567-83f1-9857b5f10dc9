generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id                            String                 @id @default(uuid())
  username                      String                 @unique
  email                         String                 @unique
  password                      String
  role                          String                 @default("CLINICIAN")
  firstName                     String
  lastName                      String
  isActive                      Boolean                @default(true)
  lastLogin                     DateTime?
  loginAttempts                 Int                    @default(0)
  lockedUntil                   DateTime?
  createdAt                     DateTime               @default(now())
  updatedAt                     DateTime               @updatedAt
  phone                         String?
  createdAppointments           Appointment[]          @relation("AppointmentsCreatedBy")
  providedAppointments          Appointment[]          @relation("AppointmentsProvidedBy")
  auditLogs                     AuditLog[]
  createdLabResults             LabResult[]            @relation("LabResultCreator")
  notifications                 Notification[]         @relation("NotificationRecipient")
  assessments                   PatientAssessment[]
  createdPatients               Patient[]              @relation("PatientCreatedBy")
  providedRecurringAppointments RecurringAppointment[] @relation("RecurringAppointmentProvider")
  refreshTokens                 RefreshToken[]
  mentalStatusExams             MentalStatusExam[]     @relation("MSEExaminer")
  prescribedMedications         MedicationHistory[]    @relation("MedicationPrescriber")

  @@index([email])
  @@index([username])
  @@index([role])
  @@index([isActive])
  @@index([lastLogin])
  @@map("users")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  isRevoked Boolean  @default(false)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expiresAt])
  @@index([isRevoked])
  @@map("refresh_tokens")
}

model Patient {
  id                    String                 @id @default(uuid())
  patientId             String                 @unique
  firstName             String
  lastName              String
  dateOfBirth           DateTime
  gender                String
  phone                 String?
  email                 String?
  address               String?
  occupation            String?
  education             String?
  maritalStatus         String?
  emergencyContact      String?
  insuranceInfo         String?
  medicalHistory        String?
  allergies             String?
  currentMeds           String?
  notes                 String?
  isActive              Boolean                @default(true)
  isDeleted             Boolean                @default(false)
  deletedAt             DateTime?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  createdBy             String
  appointments          Appointment[]
  auditLogs             AuditLog[]
  labResults            LabResult[]
  notifications         Notification[]
  assessments           PatientAssessment[]
  creator               User                   @relation("PatientCreatedBy", fields: [createdBy], references: [id])
  recurringAppointments RecurringAppointment[]
  psychTests            PsychTest[]
  mentalStatusExams     MentalStatusExam[]
  medicationHistory     MedicationHistory[]

  @@index([lastName, firstName])
  @@index([dateOfBirth])
  @@index([gender])
  @@index([isActive])
  @@index([isDeleted])
  @@index([createdAt])
  @@index([createdBy])
  @@map("patients")
}

model LabResult {
  id             String    @id @default(cuid())
  patientId      String
  testName       String
  testCategory   String // "chemistry", "hematology", "endocrine", "cardiac", "toxicology"
  testType       String // "routine", "stat", "fasting", "random"
  value          String
  numericValue   Float? // For calculations and trending
  unit           String?
  referenceRange String
  referenceMin   Float?
  referenceMax   Float?
  status         String // "normal", "abnormal", "critical", "pending"
  flagged        Boolean   @default(false)
  testDate       DateTime
  resultDate     DateTime?
  orderedBy      String
  reviewedBy     String?
  laboratoryId   String?
  specimenType   String? // "serum", "plasma", "whole blood", "urine"
  notes          String?
  criticalValue  Boolean   @default(false)
  deltaCheck     Boolean   @default(false) // Significant change from previous
  createdBy      String
  isDeleted      Boolean   @default(false)
  deletedAt      DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  auditLogs     AuditLog[]
  creator       User           @relation("LabResultCreator", fields: [createdBy], references: [id])
  patient       Patient        @relation(fields: [patientId], references: [id], onDelete: Cascade)
  notifications Notification[]

  @@index([patientId])
  @@index([testName])
  @@index([testCategory])
  @@index([testType])
  @@index([testDate])
  @@index([resultDate])
  @@index([status])
  @@index([flagged])
  @@index([criticalValue])
  @@index([createdBy])
  @@index([isDeleted])
  @@index([createdAt])
  @@index([patientId, testDate])
  @@index([patientId, testName])
  @@map("lab_results")
}

model Appointment {
  id                     String                @id @default(uuid())
  patientId              String
  providerId             String?
  recurringAppointmentId String?
  date                   DateTime
  endTime                DateTime?
  duration               Int
  type                   String
  status                 String                @default("SCHEDULED")
  title                  String?
  description            String?
  location               String?
  isVirtual              Boolean               @default(false)
  virtualMeetingUrl      String?
  notes                  String?
  isDeleted              Boolean               @default(false)
  deletedAt              DateTime?
  createdBy              String?
  createdAt              DateTime              @default(now())
  updatedAt              DateTime              @updatedAt
  recurringAppointment   RecurringAppointment? @relation(fields: [recurringAppointmentId], references: [id])
  creator                User?                 @relation("AppointmentsCreatedBy", fields: [createdBy], references: [id])
  provider               User?                 @relation("AppointmentsProvidedBy", fields: [providerId], references: [id])
  patient                Patient               @relation(fields: [patientId], references: [id], onDelete: Cascade)
  auditLogs              AuditLog[]
  notifications          Notification[]

  @@index([patientId])
  @@index([providerId])
  @@index([date])
  @@index([status])
  @@index([type])
  @@index([isDeleted])
  @@index([patientId, date])
  @@map("appointments")
}

model Notification {
  id            String       @id @default(uuid())
  recipientId   String
  type          String
  title         String
  message       String
  priority      String       @default("MEDIUM")
  channel       String       @default("IN_APP")
  status        String       @default("PENDING")
  isRead        Boolean      @default(false)
  isProcessed   Boolean      @default(false)
  scheduledFor  DateTime?
  processedAt   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  patientId     String?
  appointmentId String?
  labResultId   String?
  labResult     LabResult?   @relation(fields: [labResultId], references: [id])
  appointment   Appointment? @relation(fields: [appointmentId], references: [id])
  patient       Patient?     @relation(fields: [patientId], references: [id])
  recipient     User         @relation("NotificationRecipient", fields: [recipientId], references: [id])

  @@index([recipientId])
  @@index([type])
  @@index([channel])
  @@index([status])
  @@index([isRead])
  @@index([isProcessed])
  @@index([scheduledFor])
  @@index([createdAt])
  @@index([recipientId, isRead])
  @@map("notifications")
}

model RecurringAppointment {
  id             String        @id @default(uuid())
  patientId      String
  providerId     String
  startDate      DateTime
  endDate        DateTime?
  duration       Int
  type           String
  frequency      String
  interval       Int           @default(1)
  dayOfWeek      Int?
  dayOfMonth     Int?
  timeSlot       String
  notes          String?
  maxOccurrences Int?
  isActive       Boolean       @default(true)
  isDeleted      Boolean       @default(false)
  deletedAt      DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  appointments   Appointment[]
  provider       User          @relation("RecurringAppointmentProvider", fields: [providerId], references: [id])
  patient        Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@index([patientId])
  @@index([providerId])
  @@index([startDate])
  @@index([isActive])
  @@index([isDeleted])
  @@index([frequency])
  @@map("recurring_appointments")
}

model AuditLog {
  id            String       @id @default(uuid())
  userId        String
  action        String
  entityType    String
  entityId      String
  oldValues     String?
  newValues     String?
  ipAddress     String?
  userAgent     String?
  timestamp     DateTime     @default(now())
  patientId     String?
  labResultId   String?
  appointmentId String?
  appointment   Appointment? @relation(fields: [appointmentId], references: [id])
  labResult     LabResult?   @relation(fields: [labResultId], references: [id])
  patient       Patient?     @relation(fields: [patientId], references: [id])
  user          User         @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([entityType])
  @@index([entityId])
  @@index([timestamp])
  @@index([patientId])
  @@index([userId, timestamp])
  @@map("audit_logs")
}

model PatientAssessment {
  id             String    @id @default(uuid())
  patientId      String
  assessorId     String
  sessionDate    DateTime
  assessmentData String
  status         String
  duration       Int
  isDeleted      Boolean   @default(false)
  deletedAt      DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  assessor       User      @relation(fields: [assessorId], references: [id])
  patient        Patient   @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@index([patientId])
  @@index([assessorId])
  @@index([sessionDate])
  @@index([status])
  @@index([isDeleted])
  @@index([createdAt])
  @@map("patient_assessments")
}

model PsychTest {
  id               String   @id @default(cuid())
  patientId        String
  testName         String // "PHQ-9", "GAD-7", "Y-BOCS", etc.
  testCategory     String // "depression", "anxiety", "ocd", "psychosis", "cognitive"
  version          String?
  administeredBy   String
  administeredDate DateTime
  completionTime   Int? // Minutes
  location         String? // "office", "hospital", "telehealth"

  // Scoring
  rawScore       Int?
  totalScore     Int?
  subscaleScores String? // JSON string for multiple scores per test
  scaledScore    Int?
  percentile     Int?
  tScore         Int?
  zScore         Float?
  severity       String? // "minimal", "mild", "moderate", "severe", "extreme"
  clinicalRange  String? // "normal", "borderline", "clinical"

  // Clinical Data
  interpretation  String?
  recommendations String?
  responses       String // JSON string for individual item responses
  validity        String? // "valid", "questionable", "invalid"
  validityIndices String? // JSON string for detailed validity metrics

  // Follow-up
  notes            String?
  followUpDate     DateTime?
  followUpRequired Boolean   @default(false)

  // Metadata
  batteryId     String? // If part of test battery
  sessionNumber Int? // For repeated measures
  baselineTest  Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@index([patientId])
  @@index([testName])
  @@index([testCategory])
  @@index([administeredDate])
  @@index([administeredBy])
  @@index([severity])
  @@index([patientId, administeredDate])
  @@map("psych_tests")
}

model MentalStatusExam {
  id         String   @id @default(cuid())
  patientId  String
  examDate   DateTime
  examinerId String

  // Appearance
  appearance_grooming String? // "well-groomed", "disheveled", "inappropriate"
  appearance_dress    String? // "appropriate", "bizarre", "seductive"
  appearance_hygiene  String? // "good", "poor", "neglected"

  // Behavior
  behavior_eye_contact String? // "appropriate", "poor", "intense", "avoidant"
  behavior_motor       String? // "normal", "agitated", "retarded", "restless"
  behavior_cooperation String? // "cooperative", "guarded", "hostile", "withdrawn"

  // Speech
  speech_rate    String? // "normal", "pressured", "slow", "rapid"
  speech_volume  String? // "normal", "loud", "soft", "whispered"
  speech_tone    String? // "normal", "monotone", "dramatic", "anxious"
  speech_fluency String? // "fluent", "dysfluent", "stuttering"

  // Mood & Affect
  mood_reported          String? // Patient's reported mood
  mood_observed          String? // Clinician's observation
  affect_type            String? // "euthymic", "depressed", "anxious", "irritable"
  affect_range           String? // "full", "restricted", "blunted", "flat"
  affect_appropriateness String? // "appropriate", "inappropriate", "incongruent"

  // Thought Process
  thought_process      String? // "linear", "tangential", "circumstantial", "loose"
  thought_organization String? // "organized", "disorganized", "coherent"
  thought_flow         String? // "normal", "rapid", "slow", "blocked"

  // Thought Content
  thought_content String? // JSON string for multiple selections
  delusions       Boolean @default(false)
  delusion_type   String? // "persecutory", "grandiose", "somatic"
  obsessions      Boolean @default(false)
  compulsions     Boolean @default(false)
  phobias         Boolean @default(false)

  // Perceptual Disturbances
  hallucinations     Boolean @default(false)
  hallucination_type String? // "auditory", "visual", "tactile", "olfactory"
  illusions          Boolean @default(false)
  depersonalization  Boolean @default(false)
  derealization      Boolean @default(false)

  // Cognition
  orientation_person    Boolean @default(true)
  orientation_place     Boolean @default(true)
  orientation_time      Boolean @default(true)
  orientation_situation Boolean @default(true)

  attention_span String? // "good", "fair", "poor", "distractible"
  concentration  String? // "good", "fair", "poor", "impaired"

  memory_immediate String? // "intact", "impaired", "unable to assess"
  memory_recent    String? // "intact", "impaired", "unable to assess"
  memory_remote    String? // "intact", "impaired", "unable to assess"

  abstract_thinking String? // "intact", "concrete", "impaired"

  // Insight & Judgment
  insight_level        String? // "good", "fair", "poor", "absent"
  insight_description  String?
  judgment_level       String? // "good", "fair", "poor", "impaired"
  judgment_description String?

  // Risk Assessment
  suicidal_ideation  String? // "denied", "passive", "active", "with plan"
  suicidal_risk      String? // "low", "moderate", "high", "imminent"
  homicidal_ideation String? // "denied", "present", "with plan"
  homicidal_risk     String? // "low", "moderate", "high", "imminent"

  // Additional Notes
  clinical_notes  String?
  recommendations String?
  followup_needed Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  patient  Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
  examiner User    @relation("MSEExaminer", fields: [examinerId], references: [id])

  @@index([patientId])
  @@index([examinerId])
  @@index([examDate])
  @@index([suicidal_risk])
  @@index([homicidal_risk])
  @@index([patientId, examDate])
  @@map("mental_status_exams")
}

model MedicationHistory {
  id                 String    @id @default(cuid())
  patientId          String
  medicationName     String
  genericName        String?
  brandName          String?
  strength           String
  dosage             String
  frequency          String
  route              String // "PO", "IM", "IV", "SL", "PR", "Topical"
  indication         String
  startDate          DateTime
  endDate            DateTime?
  duration           String?
  prescribedBy       String
  prescriberId       String?
  pharmacy           String?
  sideEffects        String? // JSON string for array of side effects
  effectiveness      String? // "excellent", "good", "fair", "poor", "unknown"
  adherence          String? // "excellent", "good", "fair", "poor", "unknown"
  adherenceNotes     String?
  discontinuedReason String?
  allergicReaction   Boolean   @default(false)
  interactions       String? // JSON string for array of interactions
  monitoring         String? // Required lab monitoring
  prn                Boolean   @default(false) // As needed
  notes              String?
  isActive           Boolean   @default(true)
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  patient    Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
  prescriber User?   @relation("MedicationPrescriber", fields: [prescriberId], references: [id])

  @@index([patientId])
  @@index([prescriberId])
  @@index([medicationName])
  @@index([startDate])
  @@index([endDate])
  @@index([isActive])
  @@index([allergicReaction])
  @@index([patientId, startDate])
  @@map("medication_history")
}
