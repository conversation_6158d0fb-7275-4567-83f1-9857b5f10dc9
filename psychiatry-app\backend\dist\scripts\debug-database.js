"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
async function checkOrphanedRecords() {
    const orphaned = [];
    const patientsWithInvalidCreator = await prisma.$queryRawUnsafe(`SELECT id FROM patients WHERE createdBy NOT IN (SELECT id FROM users)`);
    if (patientsWithInvalidCreator.length > 0) {
        orphaned.push(`Patients with missing creator: ${patientsWithInvalidCreator.map(p => p.id).join(', ')}`);
    }
    const appointmentsWithInvalidPatient = await prisma.$queryRawUnsafe(`SELECT id FROM appointments WHERE patientId NOT IN (SELECT id FROM patients)`);
    if (appointmentsWithInvalidPatient.length > 0) {
        orphaned.push(`Appointments with missing patient: ${appointmentsWithInvalidPatient.map(a => a.id).join(', ')}`);
    }
    return orphaned;
}
async function checkNullsInRequiredFields() {
    const nulls = [];
    const patientsWithNullFirstName = await prisma.$queryRawUnsafe(`SELECT id FROM patients WHERE firstName IS NULL`);
    if (patientsWithNullFirstName.length > 0) {
        nulls.push(`Patients with NULL firstName: ${patientsWithNullFirstName.map(p => p.id).join(', ')}`);
    }
    return nulls;
}
async function checkIndexes() {
    return [];
}
async function main() {
    let bugs = [];
    const orphaned = await checkOrphanedRecords();
    if (orphaned.length > 0)
        bugs = bugs.concat(orphaned);
    const nulls = await checkNullsInRequiredFields();
    if (nulls.length > 0)
        bugs = bugs.concat(nulls);
    const indexes = await checkIndexes();
    if (indexes.length > 0)
        bugs = bugs.concat(indexes);
    if (bugs.length === 0) {
        console.log('✅ Database integrity checks passed. No bugs found.');
    }
    else {
        for (const bug of bugs) {
            console.log(`## BUG FOUND: ${bug}\n**Location**: Database\n**Severity**: High\n**Steps to Reproduce**:\n1. Inspect database records as described.\n\n**Expected**: No orphaned or NULL records.\n**Actual**: ${bug}\n**Fix Estimate**: <1hr\n`);
        }
    }
    await prisma.$disconnect();
}
main().catch(e => {
    console.error(e);
    prisma.$disconnect();
    process.exit(1);
});
//# sourceMappingURL=debug-database.js.map