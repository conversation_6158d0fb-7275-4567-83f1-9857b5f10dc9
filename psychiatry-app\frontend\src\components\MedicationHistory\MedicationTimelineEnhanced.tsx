import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import {
  Plus,
  Pill,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  Clock,
  User,
  Eye,
  EyeOff
} from 'lucide-react';
import api from '../../lib/api';

interface MedicationTimelineEnhancedProps {
  patientId: string;
}

interface MedicationHistory {
  id: string;
  medicationName: string;
  genericName?: string;
  brandName?: string;
  strength: string;
  dosage: string;
  frequency: string;
  route: string;
  indication: string;
  startDate: string;
  endDate?: string;
  prescribedBy: string;
  effectiveness?: string;
  adherence?: string;
  sideEffects?: string;
  notes?: string;
  isActive: boolean;
  discontinuedReason?: string;
  allergicReaction?: boolean;
  interactions?: string;
  monitoring?: string;
  prn?: boolean;
  patient?: {
    id: string;
    patientId: string;
    firstName: string;
    lastName: string;
  };
  prescriber?: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
  };
}

export const MedicationTimelineEnhanced: React.FC<MedicationTimelineEnhancedProps> = ({ patientId }) => {
  const [medications, setMedications] = useState<MedicationHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'active' | 'discontinued'>('all');
  const [expandedMedications, setExpandedMedications] = useState<Set<string>>(new Set());
  const [showInteractions, setShowInteractions] = useState(false);

  useEffect(() => {
    fetchMedicationHistory();
  }, [patientId]);

  const fetchMedicationHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.get(`/api/medication-history/patient/${patientId}`);
      
      if (response.data.success) {
        setMedications(response.data.data);
      } else {
        setError('Failed to fetch medication history');
      }
    } catch (err) {
      console.error('Error fetching medication history:', err);
      setError('Failed to load medication history');
    } finally {
      setLoading(false);
    }
  };

  const filteredMedications = medications.filter(med => {
    if (filter === 'active') return med.isActive;
    if (filter === 'discontinued') return !med.isActive;
    return true;
  });

  const toggleMedicationExpansion = (medicationId: string) => {
    const newExpanded = new Set(expandedMedications);
    if (newExpanded.has(medicationId)) {
      newExpanded.delete(medicationId);
    } else {
      newExpanded.add(medicationId);
    }
    setExpandedMedications(newExpanded);
  };

  const getEffectivenessColor = (effectiveness?: string) => {
    switch (effectiveness) {
      case 'excellent': return 'bg-green-100 text-green-800';
      case 'good': return 'bg-blue-100 text-blue-800';
      case 'fair': return 'bg-yellow-100 text-yellow-800';
      case 'poor': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAdherenceIcon = (adherence?: string) => {
    switch (adherence) {
      case 'excellent': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'good': return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'fair': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'poor': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const parseSideEffects = (sideEffects?: string): string[] => {
    if (!sideEffects) return [];
    try {
      return JSON.parse(sideEffects);
    } catch {
      return sideEffects.split(',').map(s => s.trim());
    }
  };

  const calculateDuration = (startDate: string, endDate?: string): string => {
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) return `${diffDays} days`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months`;
    return `${Math.floor(diffDays / 365)} years`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Medications</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={fetchMedicationHistory}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Medication History</h2>
          <p className="text-gray-600">Interactive timeline of prescribed medications</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowInteractions(!showInteractions)}
          >
            {showInteractions ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
            {showInteractions ? 'Hide' : 'Show'} Interactions
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Add Medication
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">Filter:</span>
          {(['all', 'active', 'discontinued'] as const).map((filterOption) => (
            <Button
              key={filterOption}
              variant={filter === filterOption ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter(filterOption)}
            >
              {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
            </Button>
          ))}
        </div>
        <div className="text-sm text-gray-600">
          {filteredMedications.length} medication{filteredMedications.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Timeline */}
      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

        {/* Medications */}
        <div className="space-y-6">
          {filteredMedications.map((medication) => {
            const isExpanded = expandedMedications.has(medication.id);
            const sideEffects = parseSideEffects(medication.sideEffects);
            const duration = calculateDuration(medication.startDate, medication.endDate);

            return (
              <div key={medication.id} className="relative flex items-start space-x-6">
                {/* Timeline dot */}
                <div className={`flex-shrink-0 w-4 h-4 rounded-full border-2 z-10 ${
                  medication.isActive 
                    ? 'bg-green-500 border-green-500' 
                    : 'bg-gray-300 border-gray-300'
                }`}></div>

                {/* Medication card */}
                <Card className={`flex-1 transition-all duration-200 hover:shadow-md cursor-pointer ${
                  medication.isActive ? 'border-green-200' : 'border-gray-200'
                } ${isExpanded ? 'shadow-lg' : ''}`}
                onClick={() => toggleMedicationExpansion(medication.id)}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Pill className={`h-5 w-5 ${medication.isActive ? 'text-green-600' : 'text-gray-400'}`} />
                        <div>
                          <CardTitle className="text-lg">
                            {medication.medicationName} {medication.strength}
                          </CardTitle>
                          <p className="text-sm text-gray-600">
                            {medication.brandName && `(${medication.brandName}) `}
                            {medication.genericName}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={medication.isActive ? 'default' : 'secondary'}>
                          {medication.isActive ? 'Active' : 'Discontinued'}
                        </Badge>
                        {medication.effectiveness && (
                          <Badge className={getEffectivenessColor(medication.effectiveness)}>
                            {medication.effectiveness}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Basic info */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Dosage:</span>
                        <p>{medication.dosage} {medication.frequency}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Route:</span>
                        <p>{medication.route}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Indication:</span>
                        <p>{medication.indication}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Duration:</span>
                        <p>{duration}</p>
                      </div>
                    </div>

                    {/* Timeline */}
                    <div className="flex items-center space-x-4 text-sm">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="font-medium">Started:</span>
                        <span>{new Date(medication.startDate).toLocaleDateString()}</span>
                      </div>
                      {medication.endDate && (
                        <div className="flex items-center space-x-1">
                          <span className="font-medium">Ended:</span>
                          <span>{new Date(medication.endDate).toLocaleDateString()}</span>
                        </div>
                      )}
                    </div>

                    {/* Expanded details */}
                    {isExpanded && (
                      <div className="space-y-4 pt-4 border-t border-gray-100">
                        {/* Effectiveness and Adherence */}
                        <div className="grid grid-cols-2 gap-4">
                          {medication.effectiveness && (
                            <div className="flex items-center space-x-2">
                              <TrendingUp className="h-4 w-4 text-blue-500" />
                              <span className="text-sm font-medium">Effectiveness:</span>
                              <Badge className={getEffectivenessColor(medication.effectiveness)}>
                                {medication.effectiveness}
                              </Badge>
                            </div>
                          )}
                          {medication.adherence && (
                            <div className="flex items-center space-x-2">
                              {getAdherenceIcon(medication.adherence)}
                              <span className="text-sm font-medium">Adherence:</span>
                              <span className="text-sm">{medication.adherence}</span>
                            </div>
                          )}
                        </div>

                        {/* Side Effects */}
                        {sideEffects.length > 0 && (
                          <div>
                            <span className="text-sm font-medium text-gray-700 flex items-center">
                              <AlertTriangle className="h-4 w-4 mr-1 text-yellow-500" />
                              Side Effects:
                            </span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {sideEffects.map((effect, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {effect}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Prescriber */}
                        <div className="flex items-center space-x-2 text-sm">
                          <User className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">Prescribed by:</span>
                          <span>{medication.prescribedBy}</span>
                        </div>

                        {/* Discontinued reason */}
                        {medication.discontinuedReason && (
                          <div>
                            <span className="text-sm font-medium text-gray-700">Discontinued reason:</span>
                            <p className="text-sm text-gray-600">{medication.discontinuedReason}</p>
                          </div>
                        )}

                        {/* Notes */}
                        {medication.notes && (
                          <div>
                            <span className="text-sm font-medium text-gray-700">Notes:</span>
                            <p className="text-sm text-gray-600">{medication.notes}</p>
                          </div>
                        )}

                        {/* Drug interactions warning */}
                        {showInteractions && (
                          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                            <div className="flex items-center space-x-2">
                              <AlertTriangle className="h-4 w-4 text-yellow-600" />
                              <span className="text-sm font-medium text-yellow-800">
                                Drug Interaction Check
                              </span>
                            </div>
                            <p className="text-xs text-yellow-700 mt-1">
                              Check for interactions with other active medications
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            );
          })}
        </div>
      </div>

      {/* Empty state */}
      {filteredMedications.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Pill className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No medications found</h3>
            <p className="text-gray-600 mb-4">
              {filter === 'all' 
                ? 'This patient has no medication history yet.' 
                : `No ${filter} medications found.`}
            </p>
            <Button onClick={fetchMedicationHistory}>
              <Plus className="h-4 w-4 mr-2" />
              Add First Medication
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
