import { PrismaClient } from '@prisma/client';
// Add this import for Node.js process type
import process from 'process';

const prisma = new PrismaClient();

// Use raw SQL to check for orphaned records
async function checkOrphanedRecords() {
  const orphaned: string[] = [];
  // Patients with non-existent creator
  const patientsWithInvalidCreator = await prisma.$queryRawUnsafe<any[]>(
    `SELECT id FROM patients WHERE createdBy NOT IN (SELECT id FROM users)`
  );
  if (patientsWithInvalidCreator.length > 0) {
    orphaned.push(`Patients with missing creator: ${patientsWithInvalidCreator.map(p => p.id).join(', ')}`);
  }
  // Appointments with non-existent patient
  const appointmentsWithInvalidPatient = await prisma.$queryRawUnsafe<any[]>(
    `SELECT id FROM appointments WHERE patientId NOT IN (SELECT id FROM patients)`
  );
  if (appointmentsWithInvalidPatient.length > 0) {
    orphaned.push(`Appointments with missing patient: ${appointmentsWithInvalidPatient.map(a => a.id).join(', ')}`);
  }
  // Add more checks as needed for other relations
  return orphaned;
}

// Use raw SQL to check for NULLs in required fields
async function checkNullsInRequiredFields() {
  const nulls: string[] = [];
  // Example: required fields in Patient
  const patientsWithNullFirstName = await prisma.$queryRawUnsafe<any[]>(
    `SELECT id FROM patients WHERE firstName IS NULL`
  );
  if (patientsWithNullFirstName.length > 0) {
    nulls.push(`Patients with NULL firstName: ${patientsWithNullFirstName.map(p => p.id).join(', ')}`);
  }
  // Add more checks for other required fields and tables
  return nulls;
}

async function checkIndexes() {
  // Index checks are not directly available via Prisma, but we can check query performance or use raw SQL if needed
  // For SQLite, pragma index_list can be used
  // This is a placeholder for index checks
  return [];
}

async function main() {
  let bugs: string[] = [];
  // 1. Orphaned records
  const orphaned = await checkOrphanedRecords();
  if (orphaned.length > 0) bugs = bugs.concat(orphaned);
  // 2. NULLs in required fields
  const nulls = await checkNullsInRequiredFields();
  if (nulls.length > 0) bugs = bugs.concat(nulls);
  // 3. Indexes (placeholder)
  const indexes = await checkIndexes();
  if (indexes.length > 0) bugs = bugs.concat(indexes);

  if (bugs.length === 0) {
    console.log('✅ Database integrity checks passed. No bugs found.');
  } else {
    for (const bug of bugs) {
      console.log(`## BUG FOUND: ${bug}\n**Location**: Database\n**Severity**: High\n**Steps to Reproduce**:\n1. Inspect database records as described.\n\n**Expected**: No orphaned or NULL records.\n**Actual**: ${bug}\n**Fix Estimate**: <1hr\n`);
    }
  }
  await prisma.$disconnect();
}

main().catch(e => {
  console.error(e);
  prisma.$disconnect();
  process.exit(1);
});
