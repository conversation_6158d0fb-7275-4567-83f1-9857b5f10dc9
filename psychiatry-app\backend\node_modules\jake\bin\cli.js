#!/usr/bin/env node
/*
 * Jake <PERSON> build tool
 * Copyright 2112 <PERSON> (<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *         http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
*/

// Try to load a local jake
try {
  require(`${ process.cwd() }/node_modules/jake`);
}
// If that fails, likely running globally
catch(e) {
  require('../lib/jake');
}

var args = process.argv.slice(2);

jake.run.apply(jake, args);
