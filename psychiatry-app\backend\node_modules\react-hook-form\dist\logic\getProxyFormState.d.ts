import type { Control, FieldValues, FormState, ReadFormState } from '../types';
declare const _default: <TFieldValues extends FieldValues, TContext = any, TTransformedValues = TFieldValues>(formState: FormState<TFieldValues>, control: Control<TFieldValues, TContext, TTransformedValues>, localProxyFormState?: ReadFormState, isRoot?: boolean) => FormState<TFieldValues>;
export default _default;
//# sourceMappingURL=getProxyFormState.d.ts.map