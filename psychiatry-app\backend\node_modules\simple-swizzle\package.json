{"name": "simple-swizzle", "description": "Simply swizzle your arguments", "version": "0.2.2", "author": "Qix (http://github.com/qix-)", "keywords": ["argument", "arguments", "swizzle", "swizzling", "parameter", "parameters", "mixed", "array"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "files": ["index.js"], "repository": "qix-/node-simple-swizzle", "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.7.1"}, "dependencies": {"is-arrayish": "^0.3.1"}}