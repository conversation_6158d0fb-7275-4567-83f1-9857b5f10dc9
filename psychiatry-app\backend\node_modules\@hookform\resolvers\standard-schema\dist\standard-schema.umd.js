!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("@standard-schema/utils")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","@standard-schema/utils"],r):r((e||self).hookformResolversStandardSchema={},e.hookformResolvers,e.utils)}(this,function(e,r,t){function s(){return s=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var s in t)({}).hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},s.apply(null,arguments)}e.standardSchemaResolver=function(e,o,n){return void 0===n&&(n={}),function(o,a,i){try{var l=function(){if(u.issues){var e=function(e,r){for(var o={},n=0;n<e.length;n++){var a=e[n],i=t.getDotPath(a);if(i&&(o[i]||(o[i]={message:a.message,type:""}),r)){var l,u=o[i].types||{};o[i].types=s({},u,((l={})[Object.keys(u).length]=a.message,l))}}return o}(u.issues,!i.shouldUseNativeValidation&&"all"===i.criteriaMode);return{values:{},errors:r.toNestErrors(e,i)}}return i.shouldUseNativeValidation&&r.validateFieldsNatively({},i),{values:n.raw?Object.assign({},o):u.value,errors:{}}},u=e["~standard"].validate(o),f=function(){if(u instanceof Promise)return Promise.resolve(u).then(function(e){u=e})}();return Promise.resolve(f&&f.then?f.then(l):l())}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=standard-schema.umd.js.map
