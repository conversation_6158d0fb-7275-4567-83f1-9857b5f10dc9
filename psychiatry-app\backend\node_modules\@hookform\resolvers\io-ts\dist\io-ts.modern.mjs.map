{"version": 3, "file": "io-ts.modern.mjs", "sources": ["../src/arrayToPath.ts", "../src/errorsToRecord.ts", "../src/io-ts.ts"], "sourcesContent": ["import * as Either from 'fp-ts/Either';\nimport { pipe } from 'fp-ts/function';\n\nconst arrayToPath = (paths: Either.Either<string, number>[]): string =>\n  paths.reduce(\n    (previous, path, index) =>\n      pipe(\n        path,\n        Either.fold(\n          (key) => `${index > 0 ? '.' : ''}${key}`,\n          (key) => `[${key}]`,\n        ),\n        (path) => `${previous}${path}`,\n      ),\n    '',\n  );\n\nexport default arrayToPath;\n", "import * as Either from 'fp-ts/Either';\nimport * as Option from 'fp-ts/Option';\nimport * as ReadonlyArray from 'fp-ts/ReadonlyArray';\nimport * as ReadonlyRecord from 'fp-ts/ReadonlyRecord';\nimport * as SemiGroup from 'fp-ts/Semigroup';\nimport { absurd, flow, identity, not, pipe } from 'fp-ts/function';\nimport * as t from 'io-ts';\nimport {\n  ExactType,\n  IntersectionType,\n  RefinementType,\n  TaggedUnionType,\n  UnionType,\n  ValidationError,\n} from 'io-ts';\nimport { FieldError } from 'react-hook-form';\nimport arrayToPath from './arrayToPath';\n\nexport type ErrorObject = Record<string, FieldError>;\nexport type FieldErrorWithPath = FieldError & { path: string };\n\nconst INSTANCE_TYPES_TO_FILTER = [\n  TaggedUnionType,\n  UnionType,\n  IntersectionType,\n  ExactType,\n  RefinementType,\n];\nconst formatErrorPath = (context: t.Context): string =>\n  pipe(\n    context,\n    ReadonlyArray.filterMapWithIndex((index, contextEntry) => {\n      const previousIndex = index - 1;\n      const previousContextEntry =\n        previousIndex === -1 ? undefined : context[previousIndex];\n      const shouldBeFiltered =\n        previousContextEntry === undefined ||\n        INSTANCE_TYPES_TO_FILTER.some(\n          (type) => previousContextEntry.type instanceof type,\n        );\n\n      return shouldBeFiltered ? Option.none : Option.some(contextEntry);\n    }),\n    ReadonlyArray.map(({ key }) => key),\n    ReadonlyArray.map((key) =>\n      pipe(\n        key,\n        (k) => parseInt(k, 10),\n        Either.fromPredicate(not<number>(Number.isNaN), () => key),\n      ),\n    ),\n    ReadonlyArray.toArray,\n    arrayToPath,\n  );\n\nconst formatError = (e: t.ValidationError): FieldErrorWithPath => {\n  const path = formatErrorPath(e.context);\n\n  const message = pipe(\n    e.message,\n    Either.fromNullable(e.context),\n    Either.mapLeft(\n      flow(\n        ReadonlyArray.last,\n        Option.map(\n          (contextEntry) =>\n            `expected ${contextEntry.type.name} but got ${JSON.stringify(\n              contextEntry.actual,\n            )}`,\n        ),\n        Option.getOrElseW(() =>\n          absurd<string>('Error context is missing name' as never),\n        ),\n      ),\n    ),\n    Either.getOrElseW(identity),\n  );\n\n  const type = pipe(\n    e.context,\n    ReadonlyArray.last,\n    Option.map((contextEntry) => contextEntry.type.name),\n    Option.getOrElse(() => 'unknown'),\n  );\n\n  return { message, type, path };\n};\n\n// this is almost the same function like Semigroup.getObjectSemigroup but reversed\n// in order to get the first error\nconst getObjectSemigroup = <\n  A extends Record<string, unknown> = never,\n>(): SemiGroup.Semigroup<A> => ({\n  concat: (first, second) => Object.assign({}, second, first),\n});\n\nconst concatToSingleError = (\n  errors: ReadonlyArray<FieldErrorWithPath>,\n): ErrorObject =>\n  pipe(\n    errors,\n    ReadonlyArray.map((error) => ({\n      [error.path]: {\n        type: error.type,\n        message: error.message,\n      },\n    })),\n    (errors) => SemiGroup.fold(getObjectSemigroup<ErrorObject>())({}, errors),\n  );\n\nconst appendSeveralErrors: SemiGroup.Semigroup<FieldErrorWithPath> = {\n  concat: (a, b) => ({\n    ...b,\n    types: { ...a.types, [a.type]: a.message, [b.type]: b.message },\n  }),\n};\n\nconst concatToMultipleErrors = (\n  errors: ReadonlyArray<FieldErrorWithPath>,\n): ErrorObject =>\n  pipe(\n    ReadonlyRecord.fromFoldableMap(appendSeveralErrors, ReadonlyArray.Foldable)(\n      errors,\n      (error) => [error.path, error],\n    ),\n    ReadonlyRecord.map((errorWithPath) => {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const { path, ...error } = errorWithPath;\n\n      return error;\n    }),\n  );\n\nconst errorsToRecord =\n  (validateAllFieldCriteria: boolean) =>\n  (validationErrors: ReadonlyArray<ValidationError>): ErrorObject => {\n    const concat = validateAllFieldCriteria\n      ? concatToMultipleErrors\n      : concatToSingleError;\n\n    return pipe(validationErrors, ReadonlyArray.map(formatError), concat);\n  };\n\nexport default errorsToRecord;\n", "import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport * as Either from 'fp-ts/Either';\nimport { pipe } from 'fp-ts/function';\nimport * as t from 'io-ts';\nimport {\n  FieldErrors,\n  FieldValues,\n  Resolver,\n  ResolverError,\n  ResolverSuccess,\n} from 'react-hook-form';\nimport errorsToRecord, { ErrorObject } from './errorsToRecord';\n\nexport function ioTsResolver<Input extends FieldValues, Context, Output>(\n  schema: t.Type<Output, Input>,\n  resolverOptions?: {\n    mode?: 'async' | 'sync';\n    raw?: false;\n  },\n): Resolver<Input, Context, Output>;\n\nexport function ioTsResolver<Input extends FieldValues, Context, Output>(\n  schema: t.Type<Output, Input>,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using io-ts schema validation\n * @param {t.Type<TFieldValues, T>} schema - The io-ts schema to validate against\n * @param {Object} options - Additional resolver configuration\n * @param {string} [options.mode='async'] - Validation mode\n * @returns {Resolver<t.OutputOf<typeof schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = t.type({\n *   name: t.string,\n *   age: t.number\n * });\n *\n * useForm({\n *   resolver: ioTsResolver(schema)\n * });\n */\nexport function ioTsResolver<Input extends FieldValues, Context, Output>(\n  schema: t.Type<Output, Input>,\n): Resolver<Input, Context, Input | Output> {\n  return (values, _context, options) =>\n    pipe(\n      values,\n      schema.decode,\n      Either.mapLeft(\n        errorsToRecord(\n          !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n        ),\n      ),\n      Either.mapLeft((errors: ErrorObject) =>\n        toNestErrors<Input>(errors, options),\n      ),\n      Either.fold<\n        FieldErrors<Input>,\n        Output,\n        ResolverError<Input> | ResolverSuccess<Output | Input>\n      >(\n        (errors) => ({\n          values: {},\n          errors,\n        }),\n        (values) => {\n          options.shouldUseNativeValidation &&\n            validateFieldsNatively({}, options);\n\n          return {\n            values,\n            errors: {},\n          };\n        },\n      ),\n    );\n}\n"], "names": ["arrayToPath", "paths", "reduce", "previous", "path", "index", "pipe", "Either", "fold", "key", "_excluded", "INSTANCE_TYPES_TO_FILTER", "TaggedUnionType", "UnionType", "IntersectionType", "ExactType", "RefinementType", "formatError", "e", "context", "<PERSON><PERSON>lyA<PERSON><PERSON>", "filterMapWithIndex", "contextEntry", "previousIndex", "previousContextEntry", "undefined", "some", "type", "Option", "none", "map", "k", "parseInt", "fromPredicate", "not", "Number", "isNaN", "toArray", "message", "fromNullable", "mapLeft", "flow", "last", "name", "JSON", "stringify", "actual", "getOrElseW", "absurd", "identity", "getOr<PERSON><PERSON>e", "concatToSingleError", "errors", "error", "SemiGroup", "getObjectSemigroup", "concat", "first", "second", "Object", "assign", "appendSeveralErrors", "a", "b", "_extends", "types", "concatToMultipleErrors", "<PERSON><PERSON><PERSON>Record", "fromFoldableMap", "Foldable", "errorWithPath", "_objectWithoutPropertiesLoose", "ioTsResolver", "schema", "values", "_context", "options", "decode", "validateAllFieldCriteria", "shouldUseNativeValidation", "criteriaMode", "validationErrors", "toNestErrors", "validateFieldsNatively"], "mappings": "upBAGA,MAAMA,EAAeC,GACnBA,EAAMC,OACJ,CAACC,EAAUC,EAAMC,IACfC,EACEF,EACAG,EAAOC,KACJC,GAAQ,GAAGJ,EAAQ,EAAI,IAAM,KAAKI,IAClCA,GAAQ,IAAIA,MAEdL,GAAS,GAAGD,IAAWC,KAE5B,ICdJM,EAAA,CAAA,QAqBMC,EAA2B,CAC/BC,EACAC,EACAC,EACAC,EACAC,GA6BIC,EAAeC,IACnB,MAAMd,EA3BNE,EADuBa,EA4BMD,EAAEC,QAzB7BC,EAAcC,mBAAmB,CAAChB,EAAOiB,KACvC,MAAMC,EAAgBlB,EAAQ,EACxBmB,GACe,IAAnBD,OAAuBE,EAAYN,EAAQI,GAO7C,YAL2BE,IAAzBD,GACAb,EAAyBe,KACtBC,GAASH,EAAqBG,gBAAgBA,GAGzBC,EAAOC,KAAOD,EAAOF,KAAKJ,EAAY,GAElEF,EAAcU,IAAI,EAAGrB,SAAUA,GAC/BW,EAAcU,IAAKrB,GACjBH,EACEG,EACCsB,GAAMC,SAASD,EAAG,IACnBxB,EAAO0B,cAAcC,EAAYC,OAAOC,OAAQ,IAAM3B,KAG1DW,EAAciB,QACdrC,GAxBqBmB,MAyDvB,MAAO,CAAEmB,QA3BOhC,EACdY,EAAEoB,QACF/B,EAAOgC,aAAarB,EAAEC,SACtBZ,EAAOiC,QACLC,EACErB,EAAcsB,KACdd,EAAOE,IACJR,GACC,YAAYA,EAAaK,KAAKgB,gBAAgBC,KAAKC,UACjDvB,EAAawB,WAGnBlB,EAAOmB,WAAW,IAChBC,EAAe,oCAIrBzC,EAAOwC,WAAWE,IAUFtB,KAPLrB,EACXY,EAAEC,QACFC,EAAcsB,KACdd,EAAOE,IAAKR,GAAiBA,EAAaK,KAAKgB,MAC/Cf,EAAOsB,UAAU,IAAM,YAGD9C,SAWpB+C,EACJC,GAEA9C,EACE8C,EACAhC,EAAcU,IAAKuB,IAAK,CACtB,CAACA,EAAMjD,MAAO,CACZuB,KAAM0B,EAAM1B,KACZW,QAASe,EAAMf,YAGlBc,GAAWE,EAAU9C,KAjBC+C,CAGzBC,OAAQA,CAACC,EAAOC,IAAWC,OAAOC,OAAO,CAAE,EAAEF,EAAQD,IAcvCH,CAAkD,CAAA,EAAIF,IAGhES,EAA+D,CACnEL,OAAQA,CAACM,EAAGC,IAACC,EAAA,CAAA,EACRD,EAAC,CACJE,MAAKD,EAAOF,CAAAA,EAAAA,EAAEG,MAAO,CAAA,CAACH,EAAEnC,MAAOmC,EAAExB,QAAS,CAACyB,EAAEpC,MAAOoC,EAAEzB,aAIpD4B,EACJd,GAEA9C,EACE6D,EAAeC,gBAAgBP,EAAqBzC,EAAciD,SAAlEF,CACEf,EACCC,GAAU,CAACA,EAAMjD,KAAMiD,IAE1Bc,EAAerC,IAAKwC,8IAEIC,CAAKD,EAAa5D,KClF9B,SAAA8D,EACdC,GAEA,MAAO,CAACC,EAAQC,EAAUC,KACxBtE,SACEoE,EACAD,EAAOI,OACPtE,EAAOiC,SDkFVsC,GChFQF,EAAQG,2BAAsD,QAAzBH,EAAQI,aDiFrDC,IACC,MAAMzB,EAASsB,EACXZ,EACAf,EAEJ,OAAO7C,EAAK2E,EAAkB7D,EAAcU,IAAIb,GAAcuC,EAAM,ICnFlEjD,EAAOiC,QAASY,GACd8B,EAAoB9B,EAAQwB,IAE9BrE,EAAOC,KAKJ4C,IAAY,CACXsB,OAAQ,CAAA,EACRtB,WAEDsB,IACCE,EAAQG,2BACNI,EAAuB,CAAA,EAAIP,GAEtB,CACLF,SACAtB,OAAQ,OD2DjB0B,KCtDH"}