"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizePatientAccess = exports.optionalAuth = exports.authorize = exports.authenticate = void 0;
const client_1 = require("@prisma/client");
const jwt_1 = require("@/utils/jwt");
const errors_1 = require("@/utils/errors");
const logger_1 = require("@/utils/logger");
const prisma = new client_1.PrismaClient();
const authenticate = async (req, res, next) => {
    try {
        logger_1.logger.info('Authentication attempt', {
            method: req.method,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
        });
        const token = (0, jwt_1.extractTokenFromHeader)(req.headers.authorization);
        if (!token) {
            logger_1.logger.warn('Authentication failed: No token provided', {
                method: req.method,
                url: req.url,
                ip: req.ip,
            });
            throw new errors_1.AuthenticationError('Access token required');
        }
        const payload = (0, jwt_1.verifyAccessToken)(token);
        logger_1.logger.info('Token verified successfully', { userId: payload.userId });
        const user = await prisma.user.findUnique({
            where: { id: payload.userId },
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
                firstName: true,
                lastName: true,
                isActive: true,
                lockedUntil: true,
            },
        });
        if (!user) {
            logger_1.logger.warn('Authentication failed: User not found', { userId: payload.userId });
            throw new errors_1.AuthenticationError('User not found');
        }
        if (!user.isActive) {
            logger_1.logger.warn('Authentication failed: Account deactivated', {
                userId: user.id,
                username: user.username
            });
            throw new errors_1.AuthenticationError('Account is deactivated');
        }
        if (user.lockedUntil && user.lockedUntil > new Date()) {
            logger_1.logger.warn('Authentication failed: Account locked', {
                userId: user.id,
                username: user.username,
                lockedUntil: user.lockedUntil
            });
            throw new errors_1.AuthenticationError('Account is temporarily locked');
        }
        req.user = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            firstName: user.firstName,
            lastName: user.lastName,
        };
        logger_1.logger.info('Authentication successful', {
            userId: user.id,
            username: user.username,
            role: user.role,
        });
        next();
    }
    catch (error) {
        logger_1.logger.error('Authentication error', {
            error: error instanceof Error ? error.message : 'Unknown error',
            method: req.method,
            url: req.url,
            ip: req.ip,
        });
        next(error);
    }
};
exports.authenticate = authenticate;
const authorize = (allowedRoles) => {
    return (req, res, next) => {
        try {
            logger_1.logger.info('Authorization check', {
                method: req.method,
                url: req.url,
                requiredRoles: allowedRoles,
                userRole: req.user?.role,
                userId: req.user?.id,
            });
            if (!req.user) {
                logger_1.logger.warn('Authorization failed: No authenticated user', {
                    method: req.method,
                    url: req.url,
                    requiredRoles: allowedRoles,
                });
                throw new errors_1.AuthenticationError('Authentication required');
            }
            if (!allowedRoles.includes(req.user.role)) {
                logger_1.logger.warn('Authorization failed: Insufficient permissions', {
                    method: req.method,
                    url: req.url,
                    requiredRoles: allowedRoles,
                    userRole: req.user.role,
                    userId: req.user.id,
                    username: req.user.username,
                });
                throw new errors_1.AuthorizationError(`Access denied. Required roles: ${allowedRoles.join(', ')}`);
            }
            logger_1.logger.info('Authorization successful', {
                method: req.method,
                url: req.url,
                userRole: req.user.role,
                userId: req.user.id,
                username: req.user.username,
            });
            next();
        }
        catch (error) {
            logger_1.logger.error('Authorization error', {
                error: error instanceof Error ? error.message : 'Unknown error',
                method: req.method,
                url: req.url,
                requiredRoles: allowedRoles,
                userRole: req.user?.role,
                userId: req.user?.id,
            });
            next(error);
        }
    };
};
exports.authorize = authorize;
const optionalAuth = async (req, res, next) => {
    try {
        const token = (0, jwt_1.extractTokenFromHeader)(req.headers.authorization);
        if (token) {
            try {
                const payload = (0, jwt_1.verifyAccessToken)(token);
                const user = await prisma.user.findUnique({
                    where: { id: payload.userId },
                    select: {
                        id: true,
                        username: true,
                        email: true,
                        role: true,
                        firstName: true,
                        lastName: true,
                        isActive: true,
                        lockedUntil: true,
                    },
                });
                if (user && user.isActive && (!user.lockedUntil || user.lockedUntil <= new Date())) {
                    req.user = {
                        id: user.id,
                        username: user.username,
                        email: user.email,
                        role: user.role,
                        firstName: user.firstName,
                        lastName: user.lastName,
                    };
                }
            }
            catch {
            }
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.optionalAuth = optionalAuth;
const authorizePatientAccess = async (req, res, next) => {
    try {
        if (!req.user) {
            throw new errors_1.AuthenticationError('Authentication required');
        }
        if (req.user.role === 'ADMIN') {
            return next();
        }
        const patientId = req.params.id || req.params.patientId || req.body.patientId;
        if (!patientId) {
            throw new errors_1.AuthorizationError('Patient ID required');
        }
        const patient = await prisma.patient.findUnique({
            where: { id: patientId },
            select: { createdBy: true },
        });
        if (!patient) {
            throw new errors_1.AuthorizationError('Patient not found');
        }
        if (patient.createdBy !== req.user.id && !['ADMIN'].includes(req.user.role)) {
            throw new errors_1.AuthorizationError('Access denied to this patient record');
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.authorizePatientAccess = authorizePatientAccess;
//# sourceMappingURL=auth.js.map