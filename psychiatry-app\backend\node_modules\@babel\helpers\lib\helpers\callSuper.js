"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _callSuper;
var _getPrototypeOf = require("./getPrototypeOf.js");
var _isNativeReflectConstruct = require("./isNativeReflectConstruct.js");
var _possibleConstructorReturn = require("./possibleConstructorReturn.js");
function _callSuper(_this, derived, args) {
  derived = (0, _getPrototypeOf.default)(derived);
  return (0, _possibleConstructorReturn.default)(_this, (0, _isNativeReflectConstruct.default)() ? Reflect.construct(derived, args || [], (0, _getPrototypeOf.default)(_this).constructor) : derived.apply(_this, args));
}

//# sourceMappingURL=callSuper.js.map
