/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M5 9v6", key: "158jrl" }],
  ["path", { d: "M9 9h3V5l7 7-7 7v-4H9V9z", key: "1sg2xn" }]
];
const ArrowBigRightDash = createLucideIcon("arrow-big-right-dash", __iconNode);

export { __iconNode, ArrowBigRightDash as default };
//# sourceMappingURL=arrow-big-right-dash.js.map
