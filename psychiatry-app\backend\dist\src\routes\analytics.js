"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const analyticsController_1 = require("../controllers/analyticsController");
const auth_1 = require("../middleware/auth");
const asyncHandler_1 = require("../utils/asyncHandler");
const router = express_1.default.Router();
router.use(auth_1.authenticate);
router.get('/dashboard', (0, asyncHandler_1.asyncHandler)(analyticsController_1.AnalyticsController.getDashboardAnalytics));
router.get('/patients', (0, asyncHandler_1.asyncHandler)(analyticsController_1.AnalyticsController.getPatientAnalytics));
router.get('/appointments', (0, asyncHandler_1.asyncHandler)(analyticsController_1.AnalyticsController.getAppointmentAnalytics));
router.get('/lab-results', (0, asyncHandler_1.asyncHandler)(analyticsController_1.AnalyticsController.getLabResultAnalytics));
router.get('/system', (0, auth_1.authorize)(['ADMIN']), (0, asyncHandler_1.asyncHandler)(analyticsController_1.AnalyticsController.getSystemAnalytics));
exports.default = router;
//# sourceMappingURL=analytics.js.map