"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const psychTestController_1 = require("../controllers/psychTestController");
const auth_1 = require("../middleware/auth");
const auth_2 = require("../middleware/auth");
const asyncHandler_1 = require("../utils/asyncHandler");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', (0, asyncHandler_1.asyncHandler)(psychTestController_1.PsychTestController.getAllPsychTests));
router.post('/', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(psychTestController_1.PsychTestController.createPsychTest));
router.get('/:id', (0, asyncHandler_1.asyncHandler)(psychTestController_1.PsychTestController.getPsychTestById));
router.put('/:id', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(psychTestController_1.PsychTestController.updatePsychTest));
router.delete('/:id', (0, auth_2.authorize)(['ADMIN', 'CLINICIAN']), (0, asyncHandler_1.asyncHandler)(psychTestController_1.PsychTestController.deletePsychTest));
router.get('/patient/:patientId', (0, asyncHandler_1.asyncHandler)(psychTestController_1.PsychTestController.getPsychTestsByPatient));
exports.default = router;
//# sourceMappingURL=psychTests.js.map