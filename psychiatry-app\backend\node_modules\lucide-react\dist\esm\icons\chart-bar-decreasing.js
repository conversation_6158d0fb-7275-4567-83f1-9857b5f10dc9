/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M3 3v16a2 2 0 0 0 2 2h16", key: "c24i48" }],
  ["path", { d: "M7 11h8", key: "1feolt" }],
  ["path", { d: "M7 16h3", key: "ur6vzw" }],
  ["path", { d: "M7 6h12", key: "sz5b0d" }]
];
const ChartBarDecreasing = createLucideIcon("chart-bar-decreasing", __iconNode);

export { __iconNode, ChartBarDecreasing as default };
//# sourceMappingURL=chart-bar-decreasing.js.map
