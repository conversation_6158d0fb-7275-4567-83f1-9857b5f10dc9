"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.aoutput = exports.anumber = exports.aexists = exports.abytes = void 0;
/**
 * Internal assertion helpers.
 * @module
 * @deprecated
 */
const utils_ts_1 = require("./utils.js");
/** @deprecated Use import from `noble/hashes/utils` module */
exports.abytes = utils_ts_1.abytes;
/** @deprecated Use import from `noble/hashes/utils` module */
exports.aexists = utils_ts_1.aexists;
/** @deprecated Use import from `noble/hashes/utils` module */
exports.anumber = utils_ts_1.anumber;
/** @deprecated Use import from `noble/hashes/utils` module */
exports.aoutput = utils_ts_1.aoutput;
//# sourceMappingURL=_assert.js.map