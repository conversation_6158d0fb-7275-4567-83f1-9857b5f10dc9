import { TaggedEventHandler } from './addEventListener';
/**
 * A `removeEventListener` ponyfill
 *
 * @param node the element
 * @param eventName the event name
 * @param handle the handler
 * @param options event options
 */
declare function removeEventListener<K extends keyof HTMLElementEventMap>(node: HTMLElement, eventName: K, handler: TaggedEventHandler<K>, options?: boolean | EventListenerOptions): void;
export default removeEventListener;
