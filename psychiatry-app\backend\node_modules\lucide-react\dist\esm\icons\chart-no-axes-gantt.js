/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M8 6h10", key: "9lnwnk" }],
  ["path", { d: "M6 12h9", key: "1g9pqf" }],
  ["path", { d: "M11 18h7", key: "c8dzvl" }]
];
const ChartNoAxesGantt = createLucideIcon("chart-no-axes-gantt", __iconNode);

export { __iconNode, ChartNoAxesGantt as default };
//# sourceMappingURL=chart-no-axes-gantt.js.map
