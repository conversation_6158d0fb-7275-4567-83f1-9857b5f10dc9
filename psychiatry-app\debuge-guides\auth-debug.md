# 🔐 Auth Debug Guide (JWT + bcrypt)

## 🔧 Key Debug Areas
- Token generation & structure
- Hash comparison issues
- Middleware order
- CORS + cookie config

## 🪙 JWT Debugging
```typescript
import jwt from 'jsonwebtoken';
const token = jwt.sign({ userId }, process.env.JWT_SECRET, { expiresIn: '1h' });
console.log('🧾 Token:', token);
```

### Verify Token
```typescript
try {
  const payload = jwt.verify(token, process.env.JWT_SECRET);
  console.log('✅ Valid token:', payload);
} catch (err) {
  console.error('❌ Invalid token', err);
}
```

## 🔑 Bcrypt Debugging
```typescript
const match = await bcrypt.compare(password, hash);
console.log('🔍 Password match?', match);
```

## 🧱 Middleware Order
- Place auth middleware **after** auth routes but **before** protected routes.

## 🐛 Common Issues
- Token expired
- Secret mismatch
- Bcrypt hash not matching
- CORS preflight failure
