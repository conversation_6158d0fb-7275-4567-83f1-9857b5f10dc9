name: ci

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20, 22]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - run: npm ci
      - name: Test
        run: npm test

  build-alpine:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20, 22]
    container:
      image: node:${{ matrix.node-version }}-alpine
    steps:
      - uses: actions/checkout@v4
      - name: Install dependencies
        run: |
          apk add make g++ python3
      - run: npm ci
      - name: Test
        run: |
          npm test --unsafe-perm
