# 🖼️ Frontend Debug Guide (React 18 + TypeScript + Vite)

## 🔧 Quick Tools
- **React DevTools**: Components + Profiler tabs
- **TypeScript**: `tsc --noEmit` for type checking
- **Console**: `console.log('🎯', data)` for state tracking

## ⚡ Common Issues

### React 18 Strict Mode
```typescript
// Double renders in development
useEffect(() => {
  console.log('Effect running'); // Will log twice
}, []);
```

### TypeScript Errors
```bash
# Check types without building
npm run type-check
tsc --noEmit
```

### State Management
```typescript
// Debug state updates
const [state, setState] = useState(initial);
console.log('🎯 State change:', { before: state, after: newState });
```

## 🐛 Debug Workflow
1. Check React DevTools Components tab
2. Inspect Network tab for API calls
3. Use Profiler for performance issues
4. Verify TypeScript compilation

## 🚨 Performance Red Flags
- Unnecessary re-renders in Profiler
- Large bundle size warnings
- Slow component mounting
- Memory leaks in useEffect
