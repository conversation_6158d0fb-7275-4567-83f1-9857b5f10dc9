import{toNestErrors as e,validateFieldsNatively as s}from"@hookform/resolvers";import{appendErrors as t}from"react-hook-form";function o(o,a,r={}){return async(a,i,n)=>{let c=o["~standard"].validate(a);if(c instanceof Promise&&(c=await c),c.issues){const s=((e,s)=>{const o=Object.assign([],e),a={};for(;o.length;){const r=e[0];if(!r.path)continue;const i=r.path.join(".");if(a[i]||(a[i]={message:r.message,type:""}),s){const e=a[i].types,o=e&&e[""];a[i]=t(i,s,a,"",o?[].concat(o,r.message):r.message)}o.shift()}return a})(c.issues,!n.shouldUseNativeValidation&&"all"===n.criteriaMode);return{values:{},errors:e(s,n)}}return n.shouldUseNativeValidation&&s({},n),{values:r.raw?Object.assign({},a):c.value,errors:{}}}}export{o as typeschemaResolver};
//# sourceMappingURL=typeschema.modern.mjs.map
