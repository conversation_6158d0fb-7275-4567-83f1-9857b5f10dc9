import{toNestErrors as s}from"@hookform/resolvers";import{appendErrors as e}from"react-hook-form";import{safeParseAsync as t,getDotPath as r}from"valibot";function o(o,a,i={}){return async(n,u,c)=>{const m=!c.shouldUseNativeValidation&&"all"===c.criteriaMode,p=await t(o,n,Object.assign({},a,{abortPipeEarly:!m}));if(p.issues){const t={};for(;p.issues.length;){const s=p.issues[0],o=r(s);if(o&&(t[o]||(t[o]={message:s.message,type:s.type}),m)){const r=t[o].types,a=r&&r[s.type];t[o]=e(o,m,t,s.type,a?[].concat(a,s.message):s.message)}p.issues.shift()}return{values:{},errors:s(t,c)}}return{values:i.raw?Object.assign({},n):p.output,errors:{}}}}export{o as valibotResolver};
//# sourceMappingURL=valibot.modern.mjs.map
