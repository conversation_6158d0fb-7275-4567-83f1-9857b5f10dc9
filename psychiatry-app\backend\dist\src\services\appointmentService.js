"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentService = void 0;
const client_1 = require("@prisma/client");
const errors_1 = require("@/utils/errors");
const date_fns_1 = require("date-fns");
const prisma = new client_1.PrismaClient();
class AppointmentService {
    static async isTimeSlotAvailable(providerId, startTime, endTime, excludeAppointmentId) {
        const conflictingAppointments = await prisma.appointment.findMany({
            where: {
                providerId,
                isDeleted: false,
                status: {
                    in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'],
                },
                AND: [
                    {
                        OR: [
                            {
                                AND: [
                                    { date: { lte: startTime } },
                                    { endTime: { gt: startTime } },
                                ],
                            },
                            {
                                AND: [
                                    { date: { lt: endTime } },
                                    { endTime: { gte: endTime } },
                                ],
                            },
                            {
                                AND: [
                                    { date: { gte: startTime } },
                                    { endTime: { lte: endTime } },
                                ],
                            },
                        ],
                    },
                ],
                ...(excludeAppointmentId && { id: { not: excludeAppointmentId } }),
            },
        });
        return conflictingAppointments.length === 0;
    }
    static async createAppointment(data, createdBy, auditData) {
        if (!data.patientId || !data.providerId || !data.date || !data.duration) {
            throw new errors_1.ValidationError('Patient ID, provider ID, date, and duration are required');
        }
        const appointmentDate = new Date(data.date);
        if (isNaN(appointmentDate.getTime()) || (0, date_fns_1.isBefore)(appointmentDate, new Date())) {
            throw new errors_1.ValidationError('Invalid appointment date or date is in the past');
        }
        const endTime = (0, date_fns_1.addMinutes)(appointmentDate, data.duration);
        const patient = await prisma.patient.findFirst({
            where: {
                id: data.patientId,
                isDeleted: false,
            },
        });
        if (!patient) {
            throw new errors_1.NotFoundError(`Patient not found with ID: ${data.patientId}`);
        }
        const provider = await prisma.user.findUnique({
            where: {
                id: data.providerId,
            },
        });
        if (!provider) {
            throw new errors_1.NotFoundError(`Provider not found with ID: ${data.providerId}`);
        }
        if (!provider.isActive) {
            throw new errors_1.ValidationError(`Provider with ID: ${data.providerId} is not active`);
        }
        if (!['ADMIN', 'CLINICIAN'].includes(provider.role)) {
            throw new errors_1.ValidationError(`User with ID: ${data.providerId} has role ${provider.role} which cannot provide appointments`);
        }
        const isAvailable = await this.isTimeSlotAvailable(data.providerId, appointmentDate, endTime);
        if (!isAvailable) {
            throw new errors_1.ConflictError('Time slot is not available');
        }
        try {
            const appointment = await prisma.appointment.create({
                data: {
                    patientId: data.patientId,
                    providerId: data.providerId,
                    date: appointmentDate,
                    endTime,
                    duration: data.duration,
                    type: data.type || 'CONSULTATION',
                    status: data.status || 'SCHEDULED',
                    title: data.title?.trim() || null,
                    description: data.description?.trim() || null,
                    location: data.location?.trim() || null,
                    isVirtual: data.isVirtual || false,
                    virtualMeetingUrl: data.virtualMeetingUrl?.trim() || null,
                    notes: data.notes?.trim() || null,
                    createdBy,
                },
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                            phone: true,
                            email: true,
                        },
                    },
                    provider: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            username: true,
                        },
                    },
                    creator: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            username: true,
                        },
                    },
                },
            });
            await this.createAuditLog({
                userId: createdBy,
                action: 'CREATE',
                entityType: 'APPOINTMENT',
                entityId: appointment.id,
                patientId: data.patientId,
                appointmentId: appointment.id,
                newValues: {
                    date: appointment.date,
                    duration: appointment.duration,
                    type: appointment.type,
                    status: appointment.status,
                    provider: `${provider.firstName} ${provider.lastName}`,
                    patient: `${patient.firstName} ${patient.lastName}`,
                },
                ipAddress: auditData?.ipAddress,
                userAgent: auditData?.userAgent,
            });
            return {
                success: true,
                data: { appointment },
                message: 'Appointment created successfully',
            };
        }
        catch (error) {
            if (error.message && error.message.includes('foreign key constraint')) {
                if (error.message.includes('providerId')) {
                    throw new errors_1.ValidationError(`Provider with ID ${data.providerId} does not exist or is not active`);
                }
                else if (error.message.includes('patientId')) {
                    throw new errors_1.ValidationError(`Patient with ID ${data.patientId} does not exist or is deleted`);
                }
            }
            throw error;
        }
    }
    static async getAppointments(query, userId, userRole) {
        const page = parseInt(query.page || '1', 10);
        const limit = Math.min(parseInt(query.limit || '10', 10), 100);
        const skip = (page - 1) * limit;
        const where = {
            isDeleted: false,
        };
        if (userRole === 'CLINICIAN') {
            where.providerId = userId;
        }
        else if (userRole === 'STAFF') {
        }
        if (query.patientId) {
            where.patientId = query.patientId;
        }
        if (query.providerId) {
            where.providerId = query.providerId;
        }
        if (query.status) {
            where.status = query.status;
        }
        if (query.type) {
            where.type = query.type;
        }
        if (query.dateFrom || query.dateTo) {
            where.date = {};
            if (query.dateFrom) {
                where.date.gte = (0, date_fns_1.startOfDay)(new Date(query.dateFrom));
            }
            if (query.dateTo) {
                where.date.lte = (0, date_fns_1.endOfDay)(new Date(query.dateTo));
            }
        }
        const orderBy = {};
        if (query.sortBy) {
            const direction = query.sortOrder === 'desc' ? 'desc' : 'asc';
            orderBy[query.sortBy] = direction;
        }
        else {
            orderBy.date = 'asc';
        }
        const [appointments, total] = await Promise.all([
            prisma.appointment.findMany({
                where,
                skip,
                take: limit,
                orderBy,
                include: {
                    patient: {
                        select: {
                            id: true,
                            patientId: true,
                            firstName: true,
                            lastName: true,
                            phone: true,
                            email: true,
                        },
                    },
                    provider: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            username: true,
                        },
                    },
                    creator: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            username: true,
                        },
                    },
                },
            }),
            prisma.appointment.count({ where }),
        ]);
        const totalPages = Math.ceil(total / limit);
        return {
            success: true,
            data: appointments,
            pagination: {
                page,
                limit,
                total,
                pages: totalPages,
            },
        };
    }
    static async getAppointmentById(id, userId, userRole) {
        const where = {
            id,
            isDeleted: false,
        };
        if (userRole === 'CLINICIAN') {
            where.providerId = userId;
        }
        const appointment = await prisma.appointment.findFirst({
            where,
            include: {
                patient: {
                    select: {
                        id: true,
                        patientId: true,
                        firstName: true,
                        lastName: true,
                        phone: true,
                        email: true,
                        dateOfBirth: true,
                        gender: true,
                    },
                },
                provider: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        username: true,
                        email: true,
                    },
                },
                creator: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        username: true,
                    },
                },
            },
        });
        if (!appointment) {
            throw new errors_1.NotFoundError('Appointment not found');
        }
        await this.createAuditLog({
            userId,
            action: 'VIEW',
            entityType: 'APPOINTMENT',
            entityId: appointment.id,
            patientId: appointment.patientId,
            appointmentId: appointment.id,
        });
        return {
            success: true,
            data: { appointment },
        };
    }
    static async updateAppointment(id, data, userId, userRole, auditData) {
        const existingAppointment = await prisma.appointment.findFirst({
            where: {
                id,
                isDeleted: false,
                ...(userRole === 'CLINICIAN' && { providerId: userId }),
            },
        });
        if (!existingAppointment) {
            throw new errors_1.NotFoundError('Appointment not found');
        }
        const updateData = {};
        let newEndTime = existingAppointment.endTime;
        if (data.date || data.duration) {
            const newDate = data.date ? new Date(data.date) : existingAppointment.date;
            const newDuration = data.duration || existingAppointment.duration;
            if (data.date && (isNaN(newDate.getTime()) || (0, date_fns_1.isBefore)(newDate, new Date()))) {
                throw new errors_1.ValidationError('Invalid appointment date or date is in the past');
            }
            newEndTime = (0, date_fns_1.addMinutes)(newDate, newDuration);
            if (data.date || data.duration) {
                const isAvailable = await this.isTimeSlotAvailable(data.providerId || existingAppointment.providerId, newDate, newEndTime, id);
                if (!isAvailable) {
                    throw new errors_1.ConflictError('Time slot is not available');
                }
            }
            updateData.date = newDate;
            updateData.duration = newDuration;
            updateData.endTime = newEndTime;
        }
        if (data.providerId !== undefined)
            updateData.providerId = data.providerId;
        if (data.type !== undefined)
            updateData.type = data.type;
        if (data.status !== undefined)
            updateData.status = data.status;
        if (data.title !== undefined)
            updateData.title = data.title?.trim() || null;
        if (data.description !== undefined)
            updateData.description = data.description?.trim() || null;
        if (data.location !== undefined)
            updateData.location = data.location?.trim() || null;
        if (data.isVirtual !== undefined)
            updateData.isVirtual = data.isVirtual;
        if (data.virtualMeetingUrl !== undefined)
            updateData.virtualMeetingUrl = data.virtualMeetingUrl?.trim() || null;
        if (data.notes !== undefined)
            updateData.notes = data.notes?.trim() || null;
        const appointment = await prisma.appointment.update({
            where: { id },
            data: updateData,
            include: {
                patient: {
                    select: {
                        id: true,
                        patientId: true,
                        firstName: true,
                        lastName: true,
                        phone: true,
                        email: true,
                    },
                },
                provider: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        username: true,
                    },
                },
                creator: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        username: true,
                    },
                },
            },
        });
        const oldValues = {
            date: existingAppointment.date,
            duration: existingAppointment.duration,
            status: existingAppointment.status,
            type: existingAppointment.type,
            notes: existingAppointment.notes,
        };
        await this.createAuditLog({
            userId,
            action: 'UPDATE',
            entityType: 'APPOINTMENT',
            entityId: appointment.id,
            patientId: appointment.patientId,
            appointmentId: appointment.id,
            oldValues: oldValues,
            newValues: {
                date: appointment.date,
                duration: appointment.duration,
                type: appointment.type,
                status: appointment.status,
                provider: `${appointment.provider.firstName} ${appointment.provider.lastName}`,
                patient: `${appointment.patient.firstName} ${appointment.patient.lastName}`,
            },
            ipAddress: auditData?.ipAddress,
            userAgent: auditData?.userAgent,
        });
        return {
            success: true,
            data: { appointment },
            message: 'Appointment updated successfully',
        };
    }
    static async cancelAppointment(id, reason, userId, userRole, auditData) {
        const appointment = await prisma.appointment.findFirst({
            where: {
                id,
                isDeleted: false,
                ...(userRole === 'CLINICIAN' && { providerId: userId }),
            },
            include: {
                patient: {
                    select: {
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        if (!appointment) {
            throw new errors_1.NotFoundError('Appointment not found');
        }
        await prisma.appointment.update({
            where: { id },
            data: {
                status: 'CANCELLED',
                notes: appointment.notes
                    ? `${appointment.notes}\n\nCancelled: ${reason}`
                    : `Cancelled: ${reason}`,
            },
        });
        await this.createAuditLog({
            userId,
            action: 'UPDATE',
            entityType: 'APPOINTMENT',
            entityId: id,
            patientId: appointment.patientId,
            appointmentId: id,
            newValues: {
                status: 'CANCELLED',
                reason,
            },
            oldValues: { status: appointment.status },
            ipAddress: auditData?.ipAddress,
            userAgent: auditData?.userAgent,
        });
        return {
            success: true,
            data: null,
            message: 'Appointment cancelled successfully',
        };
    }
    static async getProviderAvailability(providerId, date, userId, userRole) {
        const targetDate = (0, date_fns_1.parseISO)(date);
        const dayStart = (0, date_fns_1.startOfDay)(targetDate);
        const dayEnd = (0, date_fns_1.endOfDay)(targetDate);
        const existingAppointments = await prisma.appointment.findMany({
            where: {
                providerId,
                isDeleted: false,
                status: {
                    in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'],
                },
                date: {
                    gte: dayStart,
                    lte: dayEnd,
                },
            },
            orderBy: { date: 'asc' },
        });
        const workDayStart = new Date(targetDate);
        workDayStart.setHours(9, 0, 0, 0);
        const workDayEnd = new Date(targetDate);
        workDayEnd.setHours(17, 0, 0, 0);
        const timeSlots = [];
        let currentTime = new Date(workDayStart);
        while ((0, date_fns_1.isBefore)(currentTime, workDayEnd)) {
            const slotEnd = (0, date_fns_1.addMinutes)(currentTime, 30);
            const isAvailable = !existingAppointments.some(apt => (currentTime >= apt.date && currentTime < apt.endTime) ||
                (slotEnd > apt.date && slotEnd <= apt.endTime) ||
                (currentTime <= apt.date && slotEnd >= apt.endTime));
            timeSlots.push({
                startTime: new Date(currentTime),
                endTime: new Date(slotEnd),
                isAvailable,
                duration: 30,
            });
            currentTime = (0, date_fns_1.addMinutes)(currentTime, 30);
        }
        return {
            success: true,
            data: { availability: timeSlots },
        };
    }
    static async createAuditLog(data) {
        const { oldValues, newValues, ...rest } = data;
        await prisma.auditLog.create({
            data: {
                ...rest,
                oldValues: oldValues ? JSON.stringify(oldValues) : null,
                newValues: newValues ? JSON.stringify(newValues) : null,
            },
        });
    }
}
exports.AppointmentService = AppointmentService;
//# sourceMappingURL=appointmentService.js.map