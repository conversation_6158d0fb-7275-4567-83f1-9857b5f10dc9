{"version": 3, "file": "analyticsServiceSimple.js", "sourceRoot": "", "sources": ["../../src/services/analyticsServiceSimple.ts"], "names": [], "mappings": ";;;AAAA,2CAA8C;AAC9C,uCAA2E;AAG3E,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAMlC,MAAa,sBAAsB;IAIjC,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAChC,MAAc,EACd,QAAgB;QAEhB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YAEjD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,IAAA,qBAAU,EAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,QAAQ,GAAG,IAAA,sBAAW,EAAC,GAAG,CAAC,CAAC;YAClC,MAAM,SAAS,GAAG,IAAA,uBAAY,EAAC,GAAG,CAAC,CAAC;YAGpC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC/C,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACrD,KAAK,EAAE;oBACL,SAAS,EAAE,KAAK;oBAChB,SAAS,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;iBAC7B;aACF,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACtD,KAAK,EAAE;oBACL,SAAS,EAAE,KAAK;oBAChB,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;iBAC9B;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;gBACvD,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;gBACvD,KAAK,EAAE;oBACL,SAAS,EAAE,KAAK;oBAChB,IAAI,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,IAAA,mBAAQ,EAAC,KAAK,CAAC,EAAE;iBAC3C;aACF,CAAC,CAAC;YAEH,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;gBAC1D,KAAK,EAAE;oBACL,SAAS,EAAE,KAAK;oBAChB,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;oBAClB,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;iBAC3C;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;gBACnD,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;gBACrD,KAAK,EAAE;oBACL,SAAS,EAAE,KAAK;oBAChB,MAAM,EAAE,SAAS;iBAClB;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACtC,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBACzC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG;gBAChB,QAAQ,EAAE;oBACR,KAAK,EAAE,aAAa;oBACpB,WAAW,EAAE,mBAAmB;oBAChC,YAAY,EAAE,oBAAoB;oBAClC,UAAU,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,oBAAoB,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC7F;gBACD,YAAY,EAAE;oBACZ,KAAK,EAAE,iBAAiB;oBACxB,KAAK,EAAE,iBAAiB;oBACxB,QAAQ,EAAE,mBAAmB;oBAC7B,QAAQ,EAAE,oBAAoB;iBAC/B;gBACD,UAAU,EAAE;oBACV,KAAK,EAAE,eAAe;oBACtB,OAAO,EAAE,iBAAiB;oBAC1B,OAAO,EAAE,CAAC;iBACX;gBACD,MAAM,EAAE;oBACN,UAAU;oBACV,WAAW,EAAE,UAAU;iBACxB;aACF,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC;YAE3D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,EAAE,SAAS,EAAE;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YAErD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,SAAS,EAAE;wBACT,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE;wBACtE,YAAY,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;wBAC9D,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;wBAChD,MAAM,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;qBAC1C;iBACF;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAC9B,SAAuC,EACvC,MAAc,EACd,QAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAEtC,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC/C,KAAK,EAAE;oBACL,SAAS,EAAE,KAAK;oBAChB,SAAS,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE;iBAC1C;aACF,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG;gBAChB,YAAY,EAAE;oBACZ,KAAK,EAAE,aAAa;oBACpB,kBAAkB,EAAE;wBAClB,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,GAAG,CAAC,EAAE;wBAC1D,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,GAAG,CAAC,EAAE;qBAC7D;iBACF;gBACD,MAAM,EAAE;oBACN,iBAAiB,EAAE,EAAE;iBACtB;aACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,EAAE,SAAS,EAAE;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,iCAAiC;aACzC,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAClC,SAAuC,EACvC,MAAc,EACd,QAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAEtC,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;gBACvD,KAAK,EAAE;oBACL,SAAS,EAAE,KAAK;oBAChB,IAAI,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE;iBACrC;aACF,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG;gBAChB,QAAQ,EAAE;oBACR,KAAK,EAAE,iBAAiB;oBACxB,kBAAkB,EAAE;wBAClB,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,EAAE;wBACnE,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,EAAE;qBACpE;iBACF;aACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,EAAE,SAAS,EAAE;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,qCAAqC;aAC7C,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAChC,SAAuC,EACvC,MAAc,EACd,QAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAEtC,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;gBACnD,KAAK,EAAE;oBACL,SAAS,EAAE,KAAK;oBAChB,QAAQ,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE;iBACzC;aACF,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG;gBAChB,QAAQ,EAAE;oBACR,KAAK,EAAE,eAAe;oBACtB,kBAAkB,EAAE;wBAClB,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,EAAE;wBACjE,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,EAAE;qBAChE;iBACF;aACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,EAAE,SAAS,EAAE;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,oCAAoC;aAC5C,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAC7B,SAAuC,EACvC,MAAc,EACd,QAAgB;QAEhB,IAAI,CAAC;YACH,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACzB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,cAAc;iBACtB,CAAC;YACJ,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBACzC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE;oBACL,KAAK,EAAE,UAAU;oBACjB,MAAM,EAAE,UAAU;iBACnB;aACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,EAAE,SAAS,EAAE;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,gCAAgC;aACxC,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAhSD,wDAgSC"}