# VSCode AI Agent Debug Prompt: Psychiatry Application Critical Issues

## Context & Systematic Analysis Overview
I have a Node.js/React psychiatry application with 7 CRITICAL bugs identified through systematic debugging methodology. The application has core functionality completely broken due to infrastructure and API contract issues.

## Primary Issues to Debug (In Priority Order)

### 🚨 CRITICAL ISSUE #1: Port Configuration Mismatch
**Problem**: All API endpoints returning 404 errors due to port conflicts
**Files to examine**:
- `ecosystem.config.js` (sets PORT=3002)
- `backend/src/index.ts` (defaults to PORT=3001) 
- Frontend API configuration files
- Any `.env` files with port settings

**Debug Tasks**:
1. Search for all port configurations across the codebase
2. Identify which port the server is actually running on
3. Find where frontend makes API calls and what base URL it uses
4. Standardize all configurations to use the same port

**Search patterns**:
```
- Search: "PORT.*=.*300[12]" (regex)
- Search: "localhost:300[12]" (regex)
- Search: "process.env.PORT"
- Search: "3001" AND "3002" in same files
```

### 🚨 CRITICAL ISSUE #2: Assessment Sessions API Route Mismatch
**Problem**: Frontend calls `/api/patients/${patientId}/assessment-sessions` but backend only has `/api/assessment-sessions`
**Files to examine**:
- `frontend/src/services/historyTakingService.ts` (line ~20)
- `backend/src/routes/assessmentSessions.ts`
- `backend/src/controllers/AssessmentController.ts` (line ~90)
- `backend/src/index.ts` (route registrations)

**Debug Tasks**:
1. Compare frontend API calls vs backend route definitions
2. Check if AssessmentController expects `req.params.patientId`
3. Verify route registration in main server file
4. Design proper RESTful nested resource structure

**Search patterns**:
```
- Search: "assessment-sessions" (all files)
- Search: "patientId" in controllers
- Search: "router.post" in routes
- Search: "/api/patients/" in frontend services
```

### 🚨 CRITICAL ISSUE #3: Incomplete Assessment Sessions CRUD
**Problem**: Only GET route implemented, missing POST/PUT/DELETE operations
**Files to examine**:
- `backend/src/routes/assessmentSessions.ts`
- `backend/src/controllers/AssessmentController.ts`
- Frontend forms that submit assessment data

**Debug Tasks**:
1. List all HTTP methods implemented for assessment-sessions routes
2. Find frontend forms/services that need POST/PUT operations
3. Identify missing controller methods
4. Map required CRUD operations

**Search patterns**:
```
- Search: "router\.(get|post|put|delete)" (regex) in assessmentSessions.ts
- Search: "createAssessmentSession|updateAssessmentSession|deleteAssessmentSession"
- Search: "POST.*assessment" in frontend
```

### 🚨 CRITICAL ISSUE #4: React Query Error Handling
**Problem**: Undefined data errors when API endpoints return 404
**Files to examine**:
- Frontend analytics query files
- `App.tsx` or main React Query configuration
- Any files using `useQuery` for analytics

**Debug Tasks**:
1. Find React Query configurations with missing error handling
2. Locate queries that don't handle undefined data
3. Search for infinite retry loops (retry: true or no retry limit)
4. Identify missing error boundaries

**Search patterns**:
```
- Search: "useQuery" AND "analytics"
- Search: "retry.*true" OR "retry.*Infinity"
- Search: "ErrorBoundary" OR "componentDidCatch"
- Search: "undefined.*data" in React components
```

## Secondary High-Priority Issues

### ⚠️ Analytics Dashboard Non-Functional
**Files to examine**:
- `backend/src/routes/analytics.ts`
- `backend/src/controllers/AnalyticsController.ts`  
- `backend/src/services/AnalyticsServiceSimple.ts`
- Frontend analytics dashboard components

**Search patterns**:
```
- Search: "/api/analytics" in frontend
- Search: "AnalyticsService" imports and usage
- Search: "analytics.*dashboard" (case insensitive)
```

### ⚠️ Missing Error Boundaries
**Files to examine**:
- React component hierarchy
- Main App.tsx or routing components
- Any existing error handling components

**Search patterns**:
```
- Search: "ErrorBoundary" OR "componentDidCatch" OR "getDerivedStateFromError"
- Search: "try.*catch" in React components
- Search: "error" in component state or props
```

## Common Issues Found in Similar Projects & Solutions

### 🔍 Port Configuration Issues (Very Common)
Multiple developers report 404 errors when React frontend tries to fetch from Node.js backend due to port mismatches. 

**Typical Scenarios**:
- Development: Backend on 3001, frontend expecting 3002
- Production: Backend URL differs from development configuration  
- Missing "proxy" configuration in package.json to match backend API URL

**Verification Commands**:
```bash
# Check which ports are actually in use
netstat -tlnp | grep :300[12]
ps aux | grep node
lsof -i :3001 -i :3002
```

### 🔍 React Query 404/Undefined Data Patterns
Developers frequently encounter useQuery returning undefined when response status is 404 or 500, since 404 responses are not considered errors by default.

**Common Problems**:
- React-query logs failed requests to console in dev mode
- Error handling not triggered for network errors
- Infinite retries on missing endpoints causing performance issues

**React Query Error Handling Best Practices**:
```javascript
// Proper error handling for missing endpoints
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // Don't retry 404s (missing endpoints) 
        if (error?.response?.status === 404) return false;
        // Don't retry 500s more than once
        if (error?.response?.status >= 500 && failureCount >= 1) return false;
        return failureCount < 3;
      },
      staleTime: 5 * 60 * 1000,
    },
  },
});

// Component-level error handling
const AnalyticsDashboard = () => {
  const { data, error, isError, isLoading } = useQuery({
    queryKey: ['analytics'],
    queryFn: async () => {
      const response = await fetch('/api/analytics/patients');
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }
      return response.json();
    },
  });

  if (isError) {
    console.error('Analytics API Error:', error);
    return <div className="error">Unable to load analytics data. Please try again later.</div>;
  }

  if (isLoading) return <div>Loading analytics...</div>;
  
  // Handle undefined data gracefully
  const safeData = data || { patients: 0, appointments: 0, labResults: 0 };
  return <AnalyticsDisplay data={safeData} />;
};
```

## Debugging Strategy for VSCode AI Agent
Common pattern: API endpoints work in development but return 404 in production due to route registration issues.

**Debug Checklist**:
```bash
# Verify route registration in main server file
grep -r "app.use.*api" backend/src/
grep -r "router\." backend/src/routes/

# Check if routes are properly exported/imported
grep -r "export.*router" backend/src/routes/
grep -r "require.*routes" backend/src/
```

### Phase 1: Infrastructure Discovery (30 minutes)
1. **Port Configuration Audit**:
   ```bash
   # Search for all port references
   grep -r "300[12]" --include="*.js" --include="*.ts" --include="*.json" .
   grep -r "PORT" --include="*.env*" --include="*.config.*" .
   ```

2. **API Contract Mapping**:
   ```bash
   # Find all API endpoints
   grep -r "/api/" --include="*.ts" --include="*.js" frontend/src/services/
   grep -r "router\." --include="*.ts" backend/src/routes/
   ```

3. **Route Registration Verification**:
   ```bash
   # Check main server route registration
   grep -r "app.use.*api" backend/src/
   ```

### Phase 2: Critical Path Analysis (45 minutes)
1. **Assessment Workflow Mapping**:
   - Find all files related to assessment creation
   - Map frontend form submission to backend endpoints
   - Verify database models match API expectations

2. **Analytics Pipeline Check**:
   - Trace analytics data flow from database to frontend
   - Verify service implementations exist and are imported
   - Check for circular dependencies or import errors

### Phase 3: Error Handling Audit (30 minutes)
1. **React Query Configuration Review**:
   - Find query configurations without proper error handling
   - Identify queries with infinite retry settings
   - Locate missing loading and error states

2. **API Error Response Analysis**:
   - Check if backend sends proper HTTP status codes
   - Verify error response formats are consistent
   - Test error propagation to frontend

## Files Most Likely to Contain Issues

### Backend Critical Files:
```
backend/src/index.ts
backend/src/routes/assessmentSessions.ts
backend/src/routes/analytics.ts
backend/src/controllers/AssessmentController.ts
backend/src/controllers/AnalyticsController.ts
ecosystem.config.js
```

### Frontend Critical Files:
```
frontend/src/services/historyTakingService.ts
frontend/src/services/analyticsService.ts
frontend/src/App.tsx
frontend/src/components/analytics/AnalyticsDashboard.*
frontend/src/components/history-taking/*
```

### Configuration Files:
```
.env*
package.json (both frontend and backend)
ecosystem.config.js
docker-compose.yml (if exists)
```

## Expected Findings & Solutions (Based on Similar Issues)

### Port Configuration Fix:
- **Expected**: Multiple conflicting port settings
- **Common Pattern**: Frontend and backend running on different ports causing 404 errors in production
- **Solution**: Standardize on PORT=3002 across all files
- **Similar Issues**: React.js trying to fetch from Node.js while server running on different port

### Route Mismatch Fix:
- **Expected**: Frontend expects RESTful nested routes, backend has flat structure  
- **Common Pattern**: API 404 errors due to missing or incorrectly configured routes
- **Solution**: Implement `/api/patients/:patientId/assessment-sessions` routes
- **Reference**: Need to add "proxy" configuration to match backend API URL

### Missing CRUD Operations:
- **Expected**: Only read operations implemented
- **Solution**: Add POST, PUT, DELETE routes with proper controller methods
- **Common Issue**: POST requests failing with 404 when routes not properly configured

### React Query Fixes:
- **Expected**: No error handling, infinite retries
- **Common Pattern**: useQuery returning undefined when response status is 404 or 500
- **Solution**: Add error boundaries, retry limits, graceful degradation
- **Best Practice**: 404 responses not considered errors by default - need additional logic

### React Query Error Handling Implementation:
```javascript
// Add proper error handling for 404/500 responses
const { data, error, isError, isLoading } = useQuery({
  queryKey: ['analytics'],
  queryFn: async () => {
    const response = await fetch('/api/analytics/patients');
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  },
  retry: (failureCount, error) => {
    // Don't retry on 404 (missing endpoints)
    if (error?.message?.includes('404')) return false;
    return failureCount < 3;
  },
  staleTime: 5 * 60 * 1000, // 5 minutes
});

// Handle undefined data gracefully
const analyticsData = data || { patients: 0, appointments: 0, labResults: 0 };
```

## Validation Commands

After implementing fixes, run these commands to verify:

```bash
# Check server starts successfully
npm run dev (in backend)

# Verify routes are registered
curl -X GET http://localhost:3002/api/health

# Test assessment endpoint
curl -X POST http://localhost:3002/api/patients/1/assessment-sessions \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Test analytics endpoints  
curl -X GET http://localhost:3002/api/analytics/patients
```

## Priority Order for Fixes:
1. **Port Configuration** (15 minutes) - Fixes all 404 errors
2. **Assessment Routes** (45 minutes) - Restores core functionality  
3. **React Query Error Handling** (30 minutes) - Improves UX
4. **Complete CRUD Operations** (60 minutes) - Full feature set
5. **Analytics Dashboard** (30 minutes) - Secondary features

## Success Metrics:
- ✅ All API endpoints return proper responses (not 404)
- ✅ Assessment creation workflow completes successfully
- ✅ Analytics dashboard loads data without errors
- ✅ No infinite retry loops in browser network tab
- ✅ Proper error messages displayed to users instead of silent failures

This systematic approach should resolve all critical issues within 4-6 hours of focused debugging.