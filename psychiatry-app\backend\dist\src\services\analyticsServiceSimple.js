"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsServiceSimple = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
class AnalyticsServiceSimple {
    static async getDashboardAnalytics(userId, userRole) {
        try {
            const [totalPatients, totalAppointments, totalLabResults, recentPatients] = await Promise.all([
                prisma.patient.count({
                    where: { isDeleted: false }
                }),
                prisma.appointment.count({
                    where: { isDeleted: false }
                }),
                prisma.labResult.count({
                    where: { isDeleted: false }
                }),
                prisma.patient.count({
                    where: {
                        isDeleted: false,
                        createdAt: {
                            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                        }
                    }
                })
            ]);
            return {
                success: true,
                data: {
                    patients: totalPatients,
                    appointments: totalAppointments,
                    labResults: totalLabResults,
                    recentPatients,
                    lastUpdated: new Date().toISOString()
                }
            };
        }
        catch (error) {
            console.error('Error in getDashboardAnalytics:', error);
            return {
                success: false,
                error: 'Failed to fetch dashboard analytics'
            };
        }
    }
    static async getPatientAnalytics(dateRange, userId, userRole) {
        try {
            const fromDate = new Date(dateRange.from);
            const toDate = new Date(dateRange.to);
            const [totalPatients, newPatients] = await Promise.all([
                prisma.patient.count({
                    where: {
                        isDeleted: false,
                        createdAt: { gte: fromDate, lte: toDate }
                    }
                }),
                prisma.patient.count({
                    where: {
                        isDeleted: false,
                        createdAt: { gte: fromDate, lte: toDate }
                    }
                })
            ]);
            return {
                success: true,
                data: {
                    totalPatients,
                    newPatients,
                    trends: []
                }
            };
        }
        catch (error) {
            console.error('Error in getPatientAnalytics:', error);
            return {
                success: false,
                error: 'Failed to fetch patient analytics'
            };
        }
    }
    static async getAppointmentAnalytics(dateRange, userId, userRole) {
        try {
            const fromDate = new Date(dateRange.from);
            const toDate = new Date(dateRange.to);
            const [totalAppointments, completedAppointments] = await Promise.all([
                prisma.appointment.count({
                    where: {
                        isDeleted: false,
                        date: { gte: fromDate, lte: toDate }
                    }
                }),
                prisma.appointment.count({
                    where: {
                        isDeleted: false,
                        date: { gte: fromDate, lte: toDate },
                        status: 'COMPLETED'
                    }
                })
            ]);
            return {
                success: true,
                data: {
                    totalAppointments,
                    completedAppointments,
                    trends: []
                }
            };
        }
        catch (error) {
            console.error('Error in getAppointmentAnalytics:', error);
            return {
                success: false,
                error: 'Failed to fetch appointment analytics'
            };
        }
    }
    static async getLabResultAnalytics(dateRange, userId, userRole) {
        try {
            const fromDate = new Date(dateRange.from);
            const toDate = new Date(dateRange.to);
            const [totalLabResults, abnormalResults] = await Promise.all([
                prisma.labResult.count({
                    where: {
                        isDeleted: false,
                        testDate: { gte: fromDate, lte: toDate }
                    }
                }),
                prisma.labResult.count({
                    where: {
                        isDeleted: false,
                        testDate: { gte: fromDate, lte: toDate },
                        status: 'ABNORMAL'
                    }
                })
            ]);
            return {
                success: true,
                data: {
                    totalLabResults,
                    abnormalResults,
                    trends: []
                }
            };
        }
        catch (error) {
            console.error('Error in getLabResultAnalytics:', error);
            return {
                success: false,
                error: 'Failed to fetch lab result analytics'
            };
        }
    }
    static async getSystemAnalytics(dateRange, userId, userRole) {
        try {
            return {
                success: true,
                data: {
                    systemHealth: 'good',
                    performance: {
                        responseTime: '150ms',
                        uptime: '99.9%'
                    },
                    usage: {
                        activeUsers: 10,
                        totalRequests: 1000
                    }
                }
            };
        }
        catch (error) {
            console.error('Error in getSystemAnalytics:', error);
            return {
                success: false,
                error: 'Failed to fetch system analytics'
            };
        }
    }
}
exports.AnalyticsServiceSimple = AnalyticsServiceSimple;
//# sourceMappingURL=analyticsServiceSimple.js.map